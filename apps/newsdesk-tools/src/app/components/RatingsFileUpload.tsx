import * as React from 'react';

import { connect } from 'react-redux';
import { RootState } from '../state';
import { selectSession } from '../selectors/sessionSelectors';
import { Button, Intent } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import { message, Tooltip, Upload, UploadProps } from 'antd';
import { Row, RowData, RowState } from '../reducers/entities/collectionEntities';
import { updateRowAsync } from '../actions';
import { Dispatch } from 'redux';
import { CollectionId } from './collections/collectionEntities';
import styled from '@benzinga/themetron';
import axios from 'axios';
import RecordDelete from './grid/RecordDelete';
import { RcFile } from 'antd/lib/upload';
import { isEmpty } from '../utils/utils';

interface ReduxState {
  token: string;
}

interface DispatchableActions {
  onRowSave(collectionId: CollectionId, row: Row, dataKey: string, isConfirm: boolean): void;
}

type firmData = {
  _id: string;
};

type ModifiedRowData = RowData & {
  pdf_file: string;
  firm: firmData;
  ticker: string;
  state: RowState;
};

interface OwnProps {
  data: ModifiedRowData;
}

type Props = DispatchableActions & ReduxState & OwnProps;

const RatingsFileUpload: React.FC<Props> = props => {
  const [fileUrl, setFileUrl] = React.useState<string>('');
  const myRefname = React.useRef<HTMLAnchorElement>(null);

  const uploadProps: UploadProps = {
    action: `${global.env.API_ADDR}/ratings/pdf`,
    data: {
      firm_id: props.data.firm && props.data.firm._id,
      row_id: props.data._id,
      ticker: props.data.ticker,
    },
    headers: {
      token: props.token,
    },
    name: 'file',
    onChange(info) {
      if (info.file.status === 'done') {
        props.data.pdf_file = info.file.response.file;
        props.onRowSave(
          CollectionId.rating,
          {
            data: props.data,
            errors: {},
            state: props.data.state,
          },
          'pdf_file',
          true,
        );
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };

  const beforeUpload = (file: RcFile) => {
    console.log(file.type);
    const idPdf = file.type === 'application/pdf';
    if (!idPdf) {
      message.error('You can only upload PDF file!');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('PDF must be smaller than 10MB!');
    }
    if (isEmpty(props.data.ticker)) {
      message.error('Please Enter Ticker first! Before uploading file.');
    }
    if (isEmpty(props.data.firm)) {
      message.error('Please Enter Analyst Firm first! Before uploading file.');
    }
    return idPdf && isLt10M && !isEmpty(props.data.ticker) && !isEmpty(props.data.firm);
  };

  const deleteFile = (file: string) => {
    axios({
      data: { file: file },
      headers: { token: props.token },
      method: 'DELETE',
      url: `${global.env.API_ADDR}/ratings/pdf`,
    })
      .then(() => {
        message.success(`${props.data.pdf_file} file Deleted successfully`);
        props.data.pdf_file = '';
        props.onRowSave(
          CollectionId.rating,
          {
            data: props.data,
            errors: {},
            state: props.data.state,
          },
          'pdf_file',
          true,
        );
      })
      .catch(errorObj => {
        console.log(errorObj);
      });
  };

  const downloadFile = () => {
    axios({
      headers: { token: props.token },
      method: 'GET',
      url: `${global.env.API_ADDR}/ratings/pdf/url?pdf_url=${props.data.pdf_file}`,
    })
      .then(response => {
        setFileUrl(response.data.url);
        setTimeout(() => myRefname.current?.click(), 1);
      })
      .catch(errorObj => {
        console.log(errorObj);
      });
  };

  if (props.data.pdf_file)
    return (
      <MainDiv>
        <Tooltip placement="bottom" title={'Download File'}>
          <a hidden href={fileUrl} ref={myRefname} rel="noreferrer" target="_blank">
            This is hidden hyperlink to fill the download link and open in new tab
          </a>
          <Button
            icon={IconNames.Download}
            intent={Intent.PRIMARY}
            minimal
            onClick={() => downloadFile()}
            tabIndex={-1}
          />
        </Tooltip>
        <RecordDelete
          disabled={false}
          icon={IconNames.DELETE}
          intent={Intent.DANGER}
          minimal
          onConfirm={() => deleteFile(props.data.pdf_file)}
          tabIndex={-1}
          toolTip={'Delete File'}
        />
      </MainDiv>
    );

  return (
    <UploadDiv>
      <Upload {...uploadProps} beforeUpload={beforeUpload}>
        <Tooltip placement="bottom" title={'Upload File'}>
          <Button
            icon={IconNames.Document}
            intent={Intent.NONE}
            minimal
            style={{ paddingBottom: `5px` }}
            tabIndex={-1}
          />
        </Tooltip>
      </Upload>
    </UploadDiv>
  );
};

const UploadDiv = styled.div`
  display: flex;
  justify-content: center;
  padding-bottom: 5px;
`;

const MainDiv = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 25px;
`;

const mapStateToProps = (state: RootState): ReduxState => {
  const session = selectSession(state);
  return {
    token: session.serverToken,
  };
};

const mapDispatchToProps = (dispatch: Dispatch): DispatchableActions => ({
  onRowSave: (collectionId: CollectionId, row: Row, dataKey: string, isConfirm: boolean) => {
    dispatch(updateRowAsync(collectionId, row, dataKey, isConfirm));
  },
});

export default connect<ReduxState, DispatchableActions>(mapStateToProps, mapDispatchToProps)(RatingsFileUpload);

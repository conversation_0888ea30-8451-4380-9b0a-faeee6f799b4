import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
}

export function formatTime(timeString: string): string {
  // Assuming timeString is in format "HH:MM:SS"
  const parts = timeString.split(':');
  if (parts.length < 2) return timeString;

  const hours = Number.parseInt(parts[0]);
  const minutes = parts[1];
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const formattedHours = hours % 12 || 12;

  return `${formattedHours}:${minutes} ${ampm}`;
}

export function formatDateTime(dateTimeString: string): string {
  if (!dateTimeString) return '';

  const date = new Date(dateTimeString);

  // Check if date is valid
  if (isNaN(date.getTime())) return dateTimeString;

  return date.toLocaleString('en-US', {
    day: 'numeric',
    hour: 'numeric',
    hour12: true,
    minute: '2-digit',
    month: 'short',
    year: 'numeric',
  });
}

export function formatTimestamp(timestamp: string): string {
  // Assuming timestamp is in format "HH:MM:SS" or "MM:SS"
  if (!timestamp.includes(':')) return timestamp;

  const parts = timestamp.split(':');
  if (parts.length === 2) {
    return `${parts[0]}:${parts[1]}`;
  } else if (parts.length === 3) {
    return `${parts[0]}:${parts[1]}:${parts[2]}`;
  }

  return timestamp;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const isEmpty = (value: any): boolean => {
  return (
    value == null || // null or undefined
    (typeof value === 'string' && value.trim() === '') ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
  );
};

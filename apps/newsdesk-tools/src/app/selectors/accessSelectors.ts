import { createSelector } from 'reselect';

import { CollectionId, PermissionGroup, PermissionResourceId } from '../components/collections/collectionEntities';
import { selectCollectionPermissionResourceId } from '../components/collections/collectionSelectors';
import { permissionSet } from '../components/grid/entities';
import { RootState } from '../redux/types';
import { hasAccess } from '../utils/acl';
import { selectUserPermissions } from './sessionSelectors';

const READ_ACTION = 'newsdesk/read';
const WRITE_ACTION = 'newsdesk/edit';

export function createAccessSelector(action: string, resource: string) {
  return createSelector(selectUserPermissions, rules => hasAccess(rules, action, resource));
}

export const adminAccessSelector = createAccessSelector('#', '#');

export const selectCollectionAccess = (permissionResourceId: PermissionResourceId) => (state: RootState) => {
  const read = createAccessSelector(READ_ACTION, permissionResourceId)(state);
  const write = createAccessSelector(WRITE_ACTION, permissionResourceId)(state);

  if (adminAccessSelector(state)) {
    return { read, write };
  }

  return !read && !write ? PermissionGroup.noAccess : { read, write };
};

export const selectPermissionSet = (accessGroup: PermissionGroup) => permissionSet[accessGroup];

export const hasNoCollectionAccessSelector = (collectionIds: CollectionId[], state: RootState): boolean => {
  const calendarAccesses = collectionIds.map(collectionId => {
    const permissionResourceId = selectCollectionPermissionResourceId(collectionId);
    const collectionAccess = selectCollectionAccess(permissionResourceId)(state);
    return collectionAccess !== PermissionGroup.noAccess;
  });

  // calendarAccesses is [true, false, false ...]
  // We want to return true only if ALL are false (<=> access === noAccess)
  return !calendarAccesses.some(hasAccess => hasAccess === true);
};

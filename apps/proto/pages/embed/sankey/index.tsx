import React, { useMemo, useRef, useEffect, useState } from 'react';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';
import { IncomeStatement, SecuritiesManager } from '@benzinga/securities-manager';
import { formatLongPrice } from '@benzinga/calendars';
import { getGlobalSession } from '../../api/session';
import Plot from 'react-plotly.js';

//const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

interface SankeyNode {
  color: string;
  id: string;
  name: string;
  value?: number;
}

interface SankeyLink {
  color: string;
  source: number;
  target: number;
  value: number;
  label?: string;
}

const COLORS = {
  cost: '#FF6B6B', // Light red color for cost links
  darkGreen: '#02A34C', // Darker green for profit node borders
  darkRed: '#D1013F', // Darker red for cost node borders
  profit: '#9ED6AF', // Light green color for profit links
  revenue: '#A9A9A9', // Gray color for revenue
};

export const RemoveDuplicates = (array: SankeyNode[]) => {
  const uniqueArray: SankeyNode[] = [];
  const hashMap = {};
  for (const obj of array) {
    if (!hashMap[obj.id]) {
      hashMap[obj.id] = true;
      uniqueArray.push(obj);
    }
  }
  return uniqueArray;
};

export default function SankeyChart({ data }: { data: IncomeStatement[] }) {
  const router = useRouter();
  const symbol = (router.query.symbol as string) || 'AAPL';
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ height: 0, width: 0 });

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          height: containerRef.current.clientHeight,
          width: containerRef.current.clientWidth,
        });
      }
    };

    updateDimensions();

    window.addEventListener('resize', updateDimensions);

    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const generateNode = (name: string, id: string, color: string, value?: number): SankeyNode => ({
    color,
    id,
    name: value ? `${name}\n${formatLongPrice(value)}` : name,
    value,
  });

  const generateLink = (source: number, target: number, value: number, color: string): SankeyLink => ({
    color,
    source,
    target,
    value,
  });

  const nodes = useMemo(() => {
    if (!data.length) return [];

    const statement = data[0];
    return RemoveDuplicates([
      // Revenue
      generateNode('Total Revenue', 'Revenue', COLORS.revenue, statement.totalRevenue),

      // Main flows
      generateNode('Gross Profit', 'Gross Profit', COLORS.darkGreen, statement.grossProfit),
      generateNode('Operating Income', 'Operating Income', COLORS.darkGreen, statement.operatingIncome),
      generateNode('Net Income', 'Net Income', COLORS.darkGreen, statement.netIncome),

      // Costs
      generateNode('Cost of Revenue', 'Cost of Revenue', COLORS.darkRed, statement.costOfRevenue),
      generateNode('Operating Expenses', 'Operating Expenses', COLORS.darkRed, statement.operatingExpense),

      // Operating expense breakdown
      generateNode('R&D', 'R&D', COLORS.darkRed, statement.researchAndDevelopment),
      generateNode('SG&A', 'SG&A', COLORS.darkRed, statement.sellingGeneralAndAdministration),
      generateNode('Depreciation & Amortization', 'D&A', COLORS.darkRed, statement.depreciationAmortizationDepletion),
      generateNode('Other Operating Expenses', 'Other OpEx', COLORS.darkRed, statement.otherOperatingExpenses),

      // Non-operating items
      generateNode('Interest Expense', 'Interest', COLORS.darkRed, statement.interestExpense),
      generateNode('Tax Provision', 'Tax', COLORS.darkRed, Math.abs(statement.taxProvision)),
    ]);
  }, [data]);

  const links = useMemo(() => {
    if (!data.length) return [];

    const statement = data[0];
    return [
      // Revenue breakdown
      generateLink(0, 1, statement.grossProfit || 0, COLORS.profit), // Revenue to Gross Profit
      generateLink(0, 4, statement.costOfRevenue || 0, COLORS.cost), // Revenue to Cost of Revenue

      // Gross Profit breakdown
      generateLink(1, 2, statement.operatingIncome || 0, COLORS.profit), // Gross Profit to Operating Income
      generateLink(1, 5, statement.operatingExpense || 0, COLORS.cost), // Gross Profit to Operating Expenses

      // Operating Expenses breakdown
      generateLink(5, 6, statement.researchAndDevelopment || 0, COLORS.cost), // OpEx to R&D
      generateLink(5, 7, statement.sellingGeneralAndAdministration || 0, COLORS.cost), // OpEx to SG&A
      generateLink(5, 8, statement.depreciationAmortizationDepletion || 0, COLORS.cost), // OpEx to D&A
      generateLink(5, 9, statement.otherOperatingExpenses || 0, COLORS.cost), // OpEx to Other

      // Operating Income to Net Income
      generateLink(2, 3, statement.netIncome || 0, COLORS.profit), // Operating Income to Net Income
      generateLink(2, 10, statement.interestExpense || 0, COLORS.cost), // Operating Income to Interest
      generateLink(2, 11, Math.abs(statement.taxProvision || 0), COLORS.cost), // Operating Income to Tax
    ];
  }, [data]);

  const sankeyData = {
    link: {
      color: links.map(l => l.color),
      customdata: links.map(l => formatLongPrice(l.value)),
      hovertemplate: '<span>Flow Value: %{customdata}</span><extra></extra>',
      source: links.map(l => l.source),
      target: links.map(l => l.target),
      value: links.map(l => l.value),
    },
    node: {
      color: nodes.map(n => n.color),
      customdata: nodes.map(n => (n.value ? formatLongPrice(n.value) : '')),
      hovertemplate: '<span style="color:#333333;">%{label}</span><extra></extra>',
      label: nodes.map(n => n.name),
      line: {
        //color: '#333333',
        width: 0,
      },
      pad: 15,
      thickness: 20,
    },
    orientation: 'h' as const,
    type: 'sankey' as const,
  };

  return (
    <div className="w-full h-screen flex items-center justify-center bg-white" ref={containerRef}>
      <Plot
        config={{
          displayModeBar: false,
          responsive: true,
        }}
        data={[sankeyData]}
        layout={{
          autosize: true,
          font: {
            color: '#000',
            family: 'Manrope, sans-serif',
            size: 14,
            weight: 'bold',
          } as any,
          height: dimensions.height * 0.9, // 90% of container height to leave room for margins
          hoverlabel: {
            align: 'left',
            bgcolor: '#ffffff',
            bordercolor: '#cccccc',
            font: {
              color: '#000000',
              family: 'Arial, sans-serif',
              size: 16,
            },
            namelength: -1,
          },
          margin: {
            b: 50,
            l: 20,
            r: 20,
            t: 50,
          },
          paper_bgcolor: '#fff',
          plot_bgcolor: '#fff',
          title: {
            text: `Income Statement Flow - ${symbol}`,
          },
          width: dimensions.width,
        }}
        style={{ height: '100%', width: '100%' }}
        useResizeHandler={true}
      />
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  // const referer = req.headers.referer || req.headers.origin;
  // const protocol = req.headers['x-forwarded-proto'] || 'https';
  // const host = req.headers.host;
  const symbol = (query.symbol as string) || 'AAPL';
  const period = (query.period as '3M' | '12M') || '12M';

  if (!['3M', '12M'].includes(period)) {
    return {
      props: {
        data: [],
      },
    };
  }

  const headerProps = {
    disableOptinMonster: true,
    hideBanner: true,
    hideFooter: true,
    hideNavigationBar: true,
    hideQuoteBar: true,
  };

  const session = getGlobalSession();
  const response = await session.getManager(SecuritiesManager).getFinancials({
    asOf: period === '12M' ? 'LATEST_REPORTED' : undefined,
    period: period,
    symbols: symbol,
  });

  const statements = response.ok?.flatMap(f => f.financials).flatMap(f => f.incomeStatement);

  return {
    props: {
      data: statements,
      headerProps,
    },
  };
};

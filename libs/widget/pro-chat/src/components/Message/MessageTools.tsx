'use client';
import styled, { TC } from '@benzinga/themetron';
import { Dropdown, Menu } from 'antd';

import { ReportedMessage, ReportMessage } from './ReportMessage';
import { Tooltip } from 'antd';
import classnames from 'classnames';
import { Modal } from '@benzinga/core-ui';
import React from 'react';
import { More } from '@benzinga/themed-icons';
import { MenuProps } from 'antd/lib';

enum ToolOptions {
  debug = 'Debug',
  delete = 'Delete',
  edit = 'Edit',
  mute = 'Mute',
  profile = 'Profile',
  quote = 'Quote (Inline)',
  reply = 'Reply (Thread)',
  report = 'Report',
}

interface Props {
  canAdmin: boolean;
  canDebug: boolean;
  canDelete: boolean;
  canEdit: boolean;
  canMute: boolean;
  canQuote: boolean;
  canReply: boolean;
  canReport: boolean;

  onClickDebug(): void;
  onClickDelete(): void;
  onClickEdit(event: Event): void;
  onClickMute(): void;
  onClickProfile(): void;
  onClickQuote(): void;
  onClickReply(event: Event): void;
  onClickReport(report: ReportedMessage): void;
  onUpdateFocus(): void;
}

export const MessageTools: React.FC<Props> = props => {
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [showReportModal, setShowReportModal] = React.useState(false);
  const [tooltipVisible, setTooltipVisible] = React.useState(false);

  const setReportModalVisibility = React.useCallback(
    (showReportModal: boolean) => () => {
      const onUpdateFocus = props.onUpdateFocus;
      onUpdateFocus();
      setShowReportModal(showReportModal);
      setTooltipVisible(false);
    },
    [props.onUpdateFocus],
  );

  const hideModalAndDeleteMessage = React.useCallback(() => {
    setShowDeleteModal(false);
    const onClickDelete = props.onClickDelete;
    onClickDelete();
  }, [props.onClickDelete]);

  const openDeleteModal = React.useCallback(() => setShowDeleteModal(true), []);
  const hideDeleteModal = React.useCallback(() => setShowDeleteModal(false), []);
  const hideTooltipModal = React.useCallback(() => setTooltipVisible(false), []);
  const showTooltipModal = React.useCallback(() => setTooltipVisible(true), []);

  const handleMenuClick = ({ domEvent, key }: any) => {
    const { onClickDebug, onClickEdit, onClickMute, onClickProfile, onClickQuote, onClickReply } = props;
    switch (key) {
      case ToolOptions.reply:
        onClickReply(domEvent);
        break;

      case ToolOptions.edit:
        onClickEdit(domEvent);
        break;

      case ToolOptions.quote:
        onClickQuote();
        break;

      case ToolOptions.delete:
        openDeleteModal();
        break;

      case ToolOptions.mute:
        onClickMute();
        break;

      case ToolOptions.report:
        setReportModalVisibility(true)();
        break;

      case ToolOptions.debug:
        onClickDebug();
        break;

      case ToolOptions.profile:
        onClickProfile();
        break;

      default:
        break;
    }
  };

  const { canAdmin, canDebug, canDelete, canEdit, canMute, canQuote, canReply, canReport, onClickReport } = props;

  const shouldShowTools = canEdit || canDelete || canReport || canDebug || canReply || canQuote;
  if (!shouldShowTools) {
    return null;
  }

  const menuItems = [
    canReply && { key: ToolOptions.reply, label: ToolOptions.reply },
    canQuote && { key: ToolOptions.quote, label: ToolOptions.quote },
    canEdit && { key: ToolOptions.edit, label: ToolOptions.edit },
    canDelete && { key: ToolOptions.delete, label: ToolOptions.delete },
    canMute && { key: ToolOptions.mute, label: ToolOptions.mute },
    canReport && { key: ToolOptions.report, label: ToolOptions.report },
    canDebug && { key: ToolOptions.debug, label: ToolOptions.debug },
    canAdmin && { key: ToolOptions.profile, label: ToolOptions.profile },
  ].filter(Boolean) as MenuProps['items'];

  return (
    <Tooltip open={tooltipVisible} placement="bottomLeft" title="More options">
      <MessageTool>
        <Dropdown
          menu={{
            items: menuItems,
            onClick: handleMenuClick,
            onMouseEnter: hideTooltipModal,
          }}
          placement="bottomRight"
          trigger={['click']}
        >
          <More
            className="TUTORIAL_Chat-MessageTools-Button"
            onMouseEnter={showTooltipModal}
            onMouseLeave={hideTooltipModal}
          />
        </Dropdown>

        <Modal
          fullScreen={true}
          onClose={hideDeleteModal}
          size={{
            alert: true,
          }}
          title="Delete Chat"
          visible={showDeleteModal}
        >
          <div className="Modal-content">Are you sure you want to delete this chat message?</div>
          <div className="Modal-proceed">
            <button className="Button Button--text" onClick={hideDeleteModal}>
              Cancel
            </button>
            <button className={classnames('Button', 'Button--primary')} onClick={hideModalAndDeleteMessage}>
              Delete
            </button>
          </div>
        </Modal>

        {showReportModal && <ReportMessage onClose={setReportModalVisibility(false)} onReport={onClickReport} />}
      </MessageTool>
    </Tooltip>
  );
};

const MessageTool = styled(TC.Row)`
  align-items: center;
  cursor: pointer;
  right: 0;
  transition: opacity 0.2s;
  position: absolute;
`;

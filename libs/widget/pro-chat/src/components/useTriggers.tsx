'use client';
import React, { useContext } from 'react';
import { emojiIndex } from 'emoji-mart';
import { ChatContext, EmoticonItem, MessageInputContextProvider, useMessageInputContext } from 'stream-chat-react';
import UserItem from './Utils/UserItem';
import { UserResponse as User } from 'stream-chat';
import { debounce } from '@benzinga/utils';

export const CustomTriggerProvider: React.FC<React.PropsWithChildren<unknown>> = props => {
  const chatContext = useContext(ChatContext);

  const currentContextValue = useMessageInputContext();
  //  emojiSearchIndex && emojiSearchIndex.search('smile');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const queryMembersdebounced = React.useCallback(
    debounce(async (query: string, onReady?: (users: User[]) => void): Promise<void> => {
      const queryUsers = chatContext?.client.queryUsers;
      if (!queryUsers) return;
      const response = await chatContext?.client?.queryUsers({
        name: { $autocomplete: query },
      });
      if (onReady) onReady(response.users);
    }, 200),
    [chatContext?.client.queryUsers],
  );

  const customTriggers = {
    ':': {
      component: EmoticonItem,
      dataProvider: (q: any, _text: any, onReady: any) => {
        if (q.length === 0 || q.charAt(0).match(/[^a-zA-Z0-9+-]/)) {
          return [];
        }

        const emojis = emojiIndex.search(q) || [];
        const result = emojis.slice(0, 10);
        if (onReady) onReady(result, q);
        return result;
      },
      output: (entity: any) => ({
        caretPosition: 'next',
        key: entity.id,
        text: `${entity.native}`,
      }),
    },
    '@': {
      //Refer https://github.com/GetStream/stream-chat-react/blob/master/src/components/MessageInput/DefaultTriggerProvider.tsx for default user autocomplete.
      callback: (entity: User) => {
        currentContextValue.onSelectUser(entity);
      },
      component: UserItem,
      dataProvider: (query: any, _text: any, onReady: any) => {
        if (!query) return;
        return queryMembersdebounced(query, (data: User[]) => {
          if (onReady) onReady(data, query);
        });
      },
      output: (entity: User) => ({
        caretPosition: 'next',
        key: entity.id,
        text: `@${entity.name || entity.nickname}`,
      }),
    },
  };

  const updatedContextValue = {
    ...currentContextValue,
    autocompleteTriggers: customTriggers,
  };

  return <MessageInputContextProvider value={updatedContextValue as any}>{props.children}</MessageInputContextProvider>;
};

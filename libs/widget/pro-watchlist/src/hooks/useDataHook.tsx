import { SubscriberContainer, UniqueArrayBuffer } from '@benzinga/containers';
import Hooks from '@benzinga/hooks';
import { GridTransaction, useViewportData } from '@benzinga/pro-ui';
import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import { useWatchlistHoldingsQuotesCol } from '@benzinga/watchlist-manager-hooks';
import React from 'react';
import { Note } from '@benzinga/notes-manager';
import { StockSymbol } from '@benzinga/session';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { GridApi } from '@ag-grid-community/core';
import { useBatchQuotesCallback } from '@benzinga/quotes-v3-manager-hooks';
import { Quote } from '@benzinga/quotes-v3-manager';
import { DEFAULT_WATCHLIST_CONFIG } from '../utils';
interface DataProps {
  watchlistId: string;
  symbols: StockSymbol[];
  gridLayout: TableParameters;
  gridApi: React.MutableRefObject<GridApi<any> | null>;
}

type WatchlistGridHolder = HoldingsQuote & { notes?: { notes: Note[]; symbol: StockSymbol } };

export const useDataHook = (props: DataProps) => {
  const symbolSocketLimit = 50;
  const { filterVisibleSymbols, getVisibleData, initOnScrollEnd } = useViewportData();
  const initialVisibleData = React.useMemo(() => {
    const { fields } = getVisibleData(props.gridApi.current);
    const symbols = filterVisibleSymbols(props.gridApi.current, props.symbols);
    return { fields: fields.length > 0 ? fields : DEFAULT_WATCHLIST_CONFIG.table.columns.map(c => c.colId), symbols };
  }, [filterVisibleSymbols, getVisibleData, props.gridApi, props.symbols]);

  const [visibleData, setVisibleData] = React.useState(initialVisibleData);
  const [hasInitialQuotesLoaded, setHasInitialQuotesLoaded] = React.useState(false);
  const [collection] = React.useState(
    () =>
      new SubscriberContainer<WatchlistGridHolder, UniqueArrayBuffer<WatchlistGridHolder>>(
        Infinity,
        new UniqueArrayBuffer<WatchlistGridHolder>((item: WatchlistGridHolder) => item.symbol),
      ),
  );

  const updateTransaction = React.useCallback(
    (quote: HoldingsQuote) => {
      const existingData = collection.getBufferedItems().find(item => item.symbol === quote.symbol);
      if (existingData) {
        const updatedQuote = {
          ...existingData,
          ...quote,
        };
        collection.updateItems([updatedQuote]);
      }
    },
    [collection],
  );

  const updateMultipleTransaction = React.useCallback(
    (quotes: HoldingsQuote[]) => {
      if (!hasInitialQuotesLoaded) {
        const existingItems = collection.getBufferedItems();
        const newItems = [...existingItems];

        quotes.forEach(quote => {
          const existingIndex = newItems.findIndex(item => item.symbol === quote.symbol);
          if (existingIndex >= 0) {
            newItems[existingIndex] = { ...newItems[existingIndex], ...quote };
          } else {
            newItems.push(quote);
          }
        });

        collection.replace(newItems);

        setHasInitialQuotesLoaded(true);
        return;
      }

      const updatedQuotes = quotes.map(quote => {
        const existingData = collection.getBufferedItems().find(item => item.symbol === quote.symbol);
        return {
          ...existingData,
          ...quote,
        };
      });

      collection.updateItems(updatedQuotes);
    },
    [collection, hasInitialQuotesLoaded, setHasInitialQuotesLoaded],
  );

  const [transaction] = React.useState(() => new GridTransaction<HoldingsQuote>());
  Hooks.useSubscriber(collection, event => {
    switch (event.type) {
      case 'update':
        if (props.symbols.length === 0) {
          transaction.flush();
          collection.clear();
          props.gridApi.current?.setRowData([]);
          return;
        }
        transaction.transaction(event);
        break;
    }
  });

  const updateTransactionThrottled = Hooks.useThrottleLeading(updateTransaction, 30);
  const updateMultipleTransactionThrottled = Hooks.useThrottleLeading(updateMultipleTransaction, 30);

  const memoizedFields = React.useMemo(
    () => new Set<keyof HoldingsQuote>(visibleData.fields as Array<keyof HoldingsQuote>),
    [visibleData.fields],
  );

  const memoizedSymbols = React.useMemo(() => visibleData.symbols, [visibleData.symbols]);
  useWatchlistHoldingsQuotesCol(props.watchlistId, memoizedFields, updateTransactionThrottled, memoizedSymbols);

  useBatchQuotesCallback(
    props.symbols,
    new Set(props.gridLayout.columns.map(c => c.colId)) as Set<keyof Quote>,
    updateMultipleTransactionThrottled,
    hasInitialQuotesLoaded,
  );
  React.useEffect(() => {
    collection.replace([]);
    setHasInitialQuotesLoaded(false);
  }, [collection, props.watchlistId]);

  React.useEffect(() => {
    initOnScrollEnd(props.gridApi.current, visibleData => {
      setVisibleData(visibleData);
    });

    return () => {
      if (props.gridApi.current) {
        props.gridApi.current.removeEventListener('bodyScrollEnd', initOnScrollEnd);
      }
    };
  }, [initOnScrollEnd, props.gridApi]);

  React.useEffect(() => {
    const currentItems = collection.getBufferedItems();
    const symbolsSet = new Set(props.symbols);
    const filteredItems = currentItems.filter(item => symbolsSet.has(item.symbol));
    const newItemsForCollection = props.symbols
      .map(s => {
        const existingItem = filteredItems.find(item => item.symbol === s);
        return existingItem ? undefined : ({ symbol: s } as WatchlistGridHolder);
      })
      .filter(s => s !== undefined) as WatchlistGridHolder[];

    if (newItemsForCollection.length === 0) return;
    const updatedItems = [...filteredItems, ...newItemsForCollection];
    collection.replace(updatedItems);
    setVisibleData(prev => {
      const visibleTickers = filterVisibleSymbols(props.gridApi.current, prev.symbols);
      const { fields } = getVisibleData(props.gridApi.current);
      return {
        ...prev,
        fields: fields.length > 0 ? fields : DEFAULT_WATCHLIST_CONFIG.table.columns.map(c => c.colId),
        symbols: Array.from(
          new Set([
            ...visibleTickers,
            ...newItemsForCollection
              .slice(0, newItemsForCollection.length > 50 ? symbolSocketLimit : newItemsForCollection.length) // handling the limit of symbols for socket just in case whole symbols list is passed to websocket at a time
              .map(i => i.symbol),
          ]),
        ),
      };
    });
  }, [props.watchlistId, props.symbols, collection, props.gridApi, getVisibleData, filterVisibleSymbols]);
  return { transaction };
};

import { useCallback } from 'react';
import { DataHook, DataType } from '@benzinga/calendar-manager-hooks';
import { useScannerGridData } from '@benzinga/scanner-manager-hooks';
import { UnpackedArray, ValueOf } from '@benzinga/utils';
import { GridApi } from '@ag-grid-community/core';

const INITIAL_SCANNER_LIMIT = 1000;

export const useScannerData = (gridApiRef: React.RefObject<GridApi | null>, dataHook: DataHook) => {
  const { getScannerGridData } = useScannerGridData();

  const assembleScannerInitialData = useCallback(
    async (incomeItems?: UnpackedArray<ValueOf<DataType>>[]) => {
      try {
        const items = incomeItems ?? dataHook.getItems();

        if (!items?.length) {
          return;
        }

        const symbols = items
          .slice(0, INITIAL_SCANNER_LIMIT)
          .map(item => ('symbol' in item ? item.symbol : item.ticker))
          .filter(Boolean);

        const visibleColumns =
          gridApiRef?.current
            ?.getColumns()
            ?.filter(column => column.isVisible())
            .map(column => ({ colId: column.getColId() })) ?? [];

        if (!symbols.length || !visibleColumns.length) {
          return;
        }

        const result = await getScannerGridData({
          columns: visibleColumns,
          limit: INITIAL_SCANNER_LIMIT,
          symbols,
        });

        if (result?.ok) {
          dataHook.updateItems(result.ok.instruments);
        }
      } catch (error) {
        console.error('Error in getScannerInstruments:', error);
      }
    },
    [dataHook, gridApiRef, getScannerGridData],
  );

  return { assembleScannerInitialData };
};

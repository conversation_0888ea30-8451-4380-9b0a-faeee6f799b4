/* eslint-disable @typescript-eslint/member-ordering */
import { NO_VALUE } from '@benzinga/fission';
import React from 'react';

import { List } from 'antd';
import { isFunction } from 'ramda-adjunct';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HeaderWrapper,
  ItemContent,
  ItemName,
  ListItem,
  StyledChevronIconDown,
  StyledChevronIconUp,
  TableBlock,
} from './styles';
import { TickerDetail } from '@benzinga/quotes-manager';
import { Financials, Ownership } from '@benzinga/securities-manager';
import { QuoteProtos } from '@benzinga/scanner-manager';

export interface KeyDataTableData {
  averages?: TickerDetail;
  financials12M?: Financials;
  financials3M?: Financials;
  ownership?: Ownership;
  scannerData?: QuoteProtos.IQuote;
}

export interface TableRow {
  dataAccessor(data: KeyDataTableData): string | null;
  name: string;
}

export interface KeyDataTable {
  header: string;
  rowDefinitions: TableRow[];
}

interface Props {
  data: KeyDataTableData;
  handleHeaderClick?(table: KeyDataTable): void;
  isHidden?: boolean;
  table: KeyDataTable;
}

export const SymbolDetailsKeyDataTable: React.FC<Props> = ({ data, handleHeaderClick, isHidden, table }) => {
  const { header, rowDefinitions } = table;
  const rowsRender = (item: TableRow) => {
    const content = item.dataAccessor(data) || NO_VALUE;
    return (
      <ListItem key={item.name}>
        <ItemName>{item.name}</ItemName>
        <ItemContent>{content}</ItemContent>
      </ListItem>
    );
  };

  const handleClick = () => {
    if (!isFunction(handleHeaderClick)) {
      return;
    }

    handleHeaderClick(table);
  };

  const Icon = isHidden ? StyledChevronIconUp : StyledChevronIconDown;
  return (
    <TableBlock>
      <HeaderWrapper>
        <Header>
          <HeaderButton onClick={handleClick}>
            {header} <Icon />
          </HeaderButton>
        </Header>
      </HeaderWrapper>

      {!isHidden && <List dataSource={rowDefinitions} renderItem={rowsRender} />}
    </TableBlock>
  );
};

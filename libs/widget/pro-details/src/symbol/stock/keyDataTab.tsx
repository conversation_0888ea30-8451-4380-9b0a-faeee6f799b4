import React from 'react';

import { Div, NO_VALUE, numberPercent, numberShorthand } from '@benzinga/fission';
import styled from '@benzinga/themetron';

import SymbolDetailsKeyData from './keyData';
import { KeyDataTable } from './keyData/Table';
import { useTickerDetails } from '@benzinga/quotes-manager-hooks';
import { useFinancials, useOwnership } from '@benzinga/securities-manager-hooks';
import { NoResults } from '@benzinga/pro-ui';
import { useScannerInstruments } from '@benzinga/scanner-manager-hooks';

const KeyDataTabWrapper = styled(Div)`
  height: 100%;
  padding-top: 12px;
`;

const tableDefinitions: KeyDataTable[] = [
  {
    header: 'Dividends and Shares',
    rowDefinitions: [
      {
        dataAccessor: data => numberShorthand(data.ownership?.sharesOutstanding ?? NO_VALUE),
        name: 'Shares Outstanding',
      },
      {
        dataAccessor: data => numberShorthand(data.ownership?.shareFloat ?? NO_VALUE),
        name: 'Float',
      },
      {
        dataAccessor: data => numberShorthand(data.averages?.sharesShort ?? NO_VALUE),
        name: 'Short Float',
      },
      {
        dataAccessor: data => numberPercent(data.averages?.sharesShortPercentOfFloat ?? NO_VALUE, false),
        name: 'Short % of Float',
      },
      {
        dataAccessor: data => numberShorthand(data.scannerData?.relativeVolume90Day ?? NO_VALUE),
        name: 'RVol 90 Days',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.earningReports[0]?.dividendPerShare ?? NO_VALUE),
        name: 'Dividend',
      },
      {
        dataAccessor: data => numberPercent(data.financials12M?.valuationRatios[0]?.forwardDividendYield ?? NO_VALUE),
        name: 'Dividend Yield (Forward)',
      },
      {
        dataAccessor: data => numberPercent(data.financials12M?.valuationRatios[0]?.trailingDividendYield ?? NO_VALUE),
        name: 'Dividend Yield (TTM)',
      },
      {
        dataAccessor: data => numberPercent(data.financials12M?.valuationRatios[0]?.payoutRatio ?? NO_VALUE),
        name: 'Payout Ratio (TTM)',
      },
    ],
  },
  {
    header: 'Risk Metrics',
    rowDefinitions: [
      {
        dataAccessor: data =>
          data.financials12M?.alphaBeta[0] ? numberShorthand(data.financials12M?.alphaBeta[0]?.beta ?? NO_VALUE) : null,
        name: 'Beta (5Y)',
      },
    ],
  },
  {
    header: 'Income Statement',
    rowDefinitions: [
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.incomeStatement?.totalRevenue ?? NO_VALUE),
        name: 'Revenue (TTM)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.earningReports[0]?.dilutedEps ?? NO_VALUE),
        name: 'EPS Diluted (TTM)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.incomeStatement?.netIncome ?? NO_VALUE),
        name: 'Net Income (TTM)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.financials[0]?.incomeStatement?.ebitda ?? NO_VALUE),
        name: 'EBITDA (TTM)',
      },
    ],
  },
  {
    header: 'Balance Sheet',
    rowDefinitions: [
      {
        dataAccessor: data => numberShorthand(data.financials3M?.financials[0]?.balanceSheet?.totalAssets ?? NO_VALUE),
        name: 'Total Assets (Quarterly)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials3M?.financials[0]?.balanceSheet?.otherShortTermInvestments ?? NO_VALUE),
        name: 'Cash and Short Term Investments (Quarterly)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.valuationRatios[0]?.bookValuePerShare ?? NO_VALUE),
        name: 'Book Value (Per share)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.valuationRatios[0]?.tangibleBookValuePerShare ?? NO_VALUE),
        name: 'Tangible Book Value (Per share)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials3M?.financials[0]?.balanceSheet?.totalLiabilities ?? NO_VALUE),
        name: 'Total Liabilities (Quarterly)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials3M?.financials[0]?.balanceSheet?.longTermDebt ?? NO_VALUE),
        name: 'Total Long Term Debt (Quarterly)',
      },
    ],
  },
  {
    header: 'Cash Flow Statement',
    rowDefinitions: [
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.cashFlowStatement?.financingCashFlow ?? NO_VALUE),
        name: 'Cash from Financing (TTM)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.cashFlowStatement?.investingCashFlow ?? NO_VALUE),
        name: 'Cash from Investing (TTM)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.cashFlowStatement?.operatingCashFlow ?? NO_VALUE),
        name: 'Cash from Operations (TTM)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.cashFlowStatement?.capitalExpenditure ?? NO_VALUE),
        name: 'Capital Expenditure (TTM)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials12M?.financials[0]?.cashFlowStatement?.changesInCash ?? NO_VALUE),
        name: 'Net Change in Cash',
      },
    ],
  },
  {
    header: 'Profitability',
    rowDefinitions: [
      {
        dataAccessor: data => numberPercent(data.financials3M?.operationRatios[0]?.grossMargin ?? NO_VALUE),
        name: 'Gross Profit Margin (Quarterly)',
      },
      {
        dataAccessor: data =>
          numberPercent(data.financials3M?.operationRatios[0]?.normalizedNetProfitMargin ?? NO_VALUE),
        name: 'Profit Margin (Quarterly)',
      },
      {
        dataAccessor: data => numberPercent(data.financials12M?.operationRatios[0]?.ebitdaMargin ?? NO_VALUE),
        name: 'EBITDA Margin (TTM)',
      },
      {
        dataAccessor: data => numberPercent(data.financials12M?.operationRatios[0]?.operationMargin ?? NO_VALUE),
        name: 'Operation Margin (TTM)',
      },
    ],
  },
  {
    header: 'Valuation',
    rowDefinitions: [
      {
        dataAccessor: data => numberShorthand(data.financials12M?.shareClassProfile?.marketCap ?? NO_VALUE),
        name: 'Market Cap (Previous Close)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.shareClassProfile?.enterpriseValue ?? NO_VALUE),
        name: 'Enterprise Value',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.valuationRatios[0]?.peRatio ?? NO_VALUE),
        name: 'PE Ratio (TTM)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.valuationRatios[0]?.pegRatio ?? NO_VALUE),
        name: 'PEG Ratio (TTM)',
      },
      {
        dataAccessor: data => numberPercent(data.financials12M?.valuationRatios[0]?.earningYield ?? NO_VALUE),
        name: 'Earnings Yield (TTM)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.valuationRatios[0]?.psRatio ?? NO_VALUE),
        name: 'PS Ratio (TTM)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.valuationRatios[0]?.evToEbitda ?? NO_VALUE),
        name: 'EV to EBITDA (TTM)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials12M?.valuationRatios[0]?.forwardPeRatio ?? NO_VALUE),
        name: 'PE Ratio (Forward)',
      },
    ],
  },
  {
    header: 'Liquidity and Solvency',
    rowDefinitions: [
      {
        dataAccessor: data => numberShorthand(data.financials3M?.operationRatios[0]?.currentRatio ?? NO_VALUE),
        name: 'Current Ratio (Quarterly)',
      },
      {
        dataAccessor: data => numberShorthand(data.financials3M?.operationRatios[0]?.totalDebtEquityRatio ?? NO_VALUE),
        name: 'Debt to Equity Ratio (Quarterly)',
      },
      {
        dataAccessor: data =>
          numberShorthand(data.financials3M?.financials[0]?.cashFlowStatement?.freeCashFlow ?? NO_VALUE),
        name: 'Free Cash Flow (Quarterly)',
      },
    ],
  },
];

interface Props {
  symbol: string;
}

const KEYDATA_COLUMNS = ['relativeVolume90Day', 'symbol'];

export const SymbolDetailsKeyDataTab: React.FC<Props> = props => {
  const financials3M = useFinancials({ period: '3M', symbols: props.symbol })?.[0];
  const financials12M = useFinancials({ period: '12M', symbols: props.symbol })?.[0];
  const averages = useTickerDetails(props.symbol);
  const ownership = useOwnership(props.symbol)?.ok ?? undefined;
  const symbols = React.useMemo(() => [props.symbol], [props.symbol]);
  const { scannerData } = useScannerInstruments(symbols, KEYDATA_COLUMNS);

  const data = React.useMemo(
    () => ({
      averages,
      financials12M,
      financials3M,
      ownership,
      scannerData: scannerData[0],
    }),
    [averages, financials12M, financials3M, ownership, scannerData],
  );

  return (
    <KeyDataTabWrapper>
      {Object.keys(data).some(k => Boolean(data[k]?.error)) ? (
        <NoResults />
      ) : (
        <SymbolDetailsKeyData data={data} tables={tableDefinitions} />
      )}
    </KeyDataTabWrapper>
  );
};

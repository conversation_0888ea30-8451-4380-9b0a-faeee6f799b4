import React, { useMemo } from 'react';
import { KeyStatRow, Title } from '../shared';
import { StockSymbol } from '@benzinga/session';
import { useScannerInstruments } from '@benzinga/scanner-manager-hooks';
import { PositiveNegativeIndicator } from '@benzinga/pro-ui';
import Styles from './styles.module.scss';

import { QuoteProtos } from '@benzinga/scanner-manager';
import classNames from 'classnames';
import { isEmpty } from '@benzinga/utils';

interface Props {
  clickByTitle?: () => void;
  symbol: StockSymbol;
}

const RANKINGS_COLUMNS = [
  'momentumPercentile',
  'growthPercentile',
  'valuePercentile',
  'qualityPercentile',
  'shortTermTrend',
  'mediumTermTrend',
  'longTermTrend',
];

const Rankings: React.FC<Props> = ({ clickByTitle, symbol }) => {
  const symbols = useMemo(() => {
    return [symbol];
  }, [symbol]);

  const { scannerData } = useScannerInstruments(symbols, RANKINGS_COLUMNS);
  const renderRankings = (quote: QuoteProtos.IQuote) => (
    <KeyStatRow
      key={quote.symbol}
      values={[
        Number(quote.momentumPercentile).toFixed(2),
        Number(quote.growthPercentile).toFixed(2),
        Number(quote.valuePercentile).toFixed(2),
        Number(quote.qualityPercentile).toFixed(2),
        <PositiveNegativeIndicator value={quote.shortTermTrend === 'Y'} />,
        <PositiveNegativeIndicator value={quote.mediumTermTrend === 'Y'} />,
        <PositiveNegativeIndicator value={quote.longTermTrend === 'Y'} />,
      ]}
    />
  );
  const renderRankingsList = React.useMemo(() => scannerData.map(quote => renderRankings(quote)), [scannerData]);

  return (
    <div className="KeyStat KeyStat--comparison">
      <Title clickByTitle={clickByTitle} hasMoreLink={true} name="Rankings" />
      {isEmpty(scannerData) || isEmpty(renderRankingsList) ? (
        <div className="KeyStat-row">No rankings data available for {symbol} at the moment.</div>
      ) : (
        <div className={classNames('KeyStat-table', Styles.RankingsTable)}>
          <div className="KeyStat-row">
            <div className="KeyStat-stat" style={{ width: '50%' }}>
              Momentum
            </div>
            <div className="KeyStat-stat">Growth</div>
            <div className="KeyStat-stat">Value</div>
            <div className="KeyStat-stat">Quality</div>
            <div className={classNames('KeyStat-stat', Styles.IndicatorsCell)}>Short</div>
            <div className={classNames('KeyStat-stat', Styles.IndicatorsCell)}>Medium</div>
            <div className={classNames('KeyStat-stat', Styles.IndicatorsCell)}>Long</div>
          </div>
          {renderRankingsList}
        </div>
      )}
    </div>
  );
};

export default Rankings;

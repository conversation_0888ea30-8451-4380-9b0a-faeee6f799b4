import { KeyStatRow, Title } from '../shared';
import { numberShorthand, numberPercent } from '@benzinga/fission';
import { useDetailedQuote, useTickerDetails } from '@benzinga/quotes-manager-hooks';
import { useScannerInstruments } from '@benzinga/scanner-manager-hooks';
import { StockSymbol } from '@benzinga/session';
import React from 'react';

const KEYDATA_COLUMNS = ['relativeVolume90Day', 'symbol'];

export const useKeyData = (symbol: StockSymbol) => {
  const symbols = React.useMemo(() => [symbol], [symbol]);
  const { scannerData } = useScannerInstruments(symbols, KEYDATA_COLUMNS);
  const averages = useTickerDetails(symbol);
  const quote = useDetailedQuote(symbol)?.ok;

  const keyData = React.useMemo(() => {
    const rvol90d = scannerData[0]?.symbol === symbol ? numberShorthand(scannerData[0]?.relativeVolume90Day) : '--';
    const dayRange = quote?.low && quote?.high ? `${quote.low} - ${quote.high}` : '--';
    const movingAverage = quote?.fiftyDayAveragePrice ? numberShorthand(quote.fiftyDayAveragePrice) : '--';
    const float = quote?.sharesFloat ? numberShorthand(quote.sharesFloat) : '--';
    const sharesShort = averages?.sharesShort ? numberShorthand(averages.sharesShort) : '--';
    const sharesShortPercentOfFloat = averages?.sharesShortPercentOfFloat
      ? numberPercent(averages.sharesShortPercentOfFloat, false)
      : '--';
    const industry = quote?.industry ?? '--';
    const weekRange =
      quote?.fiftyTwoWeekLow && quote?.fiftyTwoWeekHigh ? `${quote.fiftyTwoWeekLow} - ${quote.fiftyTwoWeekHigh}` : '--';

    return {
      dayRange,
      float,
      industry,
      movingAverage,
      rvol90d,
      sharesShort,
      sharesShortPercentOfFloat,
      weekRange,
    };
  }, [scannerData, symbol, quote, averages]);

  return keyData;
};

interface Props {
  clickByTitle?: () => void;
  symbol: StockSymbol;
}

const KeyDataTable: React.FC<Props> = ({ clickByTitle, symbol }) => {
  const { dayRange, float, industry, movingAverage, rvol90d, sharesShort, sharesShortPercentOfFloat, weekRange } =
    useKeyData(symbol);

  return (
    <div className="KeyStat KeyStat--simple keyData--left-table">
      <Title clickByTitle={clickByTitle} hasMoreLink={true} name="Key Data" />

      <div className="KeyStat-table">
        <KeyStatRow stat="50 day Moving Average" values={[movingAverage]} />
        <KeyStatRow stat="Day Range" values={[dayRange]} />
        <KeyStatRow stat="52 week Range" values={[weekRange]} />
        <KeyStatRow stat="Float" values={[float]} />
        <KeyStatRow stat="Short Float" values={[sharesShort]} />
        <KeyStatRow stat="Short % of Float" values={[sharesShortPercentOfFloat]} />
        <KeyStatRow stat="RVol 90 Days" values={[rvol90d]} />
        <KeyStatRow stat="Industry" values={[industry]} />
      </div>
    </div>
  );
};

export default KeyDataTable;

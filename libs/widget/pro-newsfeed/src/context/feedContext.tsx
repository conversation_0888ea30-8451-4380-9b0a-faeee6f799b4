'use client';
import React from 'react';
import { Expression } from '@benzinga/session';
import { useTime } from '@benzinga/time-manager-hooks';
import { SessionContext } from '@benzinga/session-context';
import { NewsFeed, AdvancedNewsManager, NewsfeedFieldType, Story } from '@benzinga/advanced-news-manager';

import { AdvancedNewsfeedContainer } from '../utils/container';
import { NewsfeedInternalSettingsContext } from './internalSettingContext';
import { noop } from '@benzinga/utils';
import { DisplayType } from '@benzinga/news-user-settings';
import { useNewsfeedGlobalSettings } from '../utils/useGlobalSettings';

interface NewsfeedContextProps {
  historicLimit: number;
  newsfeed: NewsFeed;
  newsfeedContainer: AdvancedNewsfeedContainer;
}

interface NewsfeedStoryContextProps {
  closeStoryBody: (story: Story) => void;
  isStoryBody: (story: Story) => boolean;
  openStoryBody: (story: Story) => void;
}
interface Props {
  displayType: DisplayType;
  expression: Expression<NewsfeedFieldType>;
  historicLimit?: number;
}

export const AdvancedNewsfeedContext = React.createContext<NewsfeedContextProps | undefined>(undefined);

export const AdvancedNewsfeedStoryContext = React.createContext<NewsfeedStoryContextProps>({
  closeStoryBody: noop,
  isStoryBody: () => false,
  openStoryBody: noop,
});

export const AdvancedNewsfeedContextProvider: React.FC<React.PropsWithChildren<Props>> = React.memo(props => {
  const { displayType, expression, historicLimit } = props;
  const session = React.useContext(SessionContext);
  const newsfeedSettings = React.useContext(NewsfeedInternalSettingsContext);
  const globalSettings = useNewsfeedGlobalSettings();
  const time = useTime();
  const timeZone = time.timezone;

  const newsfeedValue = React.useMemo<NewsfeedContextProps>(() => {
    const advancedNewsManager = session.getManager(AdvancedNewsManager);
    const fields: NewsfeedFieldType[] = [
      'name',
      'Channels',
      'PartnerURL',
      'Quotes',
      'Sentiment',
      'TeaserText',
      'Tickers',
      'Title',
      'Type',
    ];
    if (displayType === 'Expanded') {
      fields.push('Body');
    }
    const newsfeed = advancedNewsManager.createFeed(
      {
        fields,
        sort: { CreatedAt: -1 },
        where: expression,
      },
      newsfeedSettings.jumpedToDate ?? undefined,
    );
    const newsfeedContainer = new AdvancedNewsfeedContainer(
      newsfeed,
      Infinity,
      timeZone,
      globalSettings.displayDateBanner,
    );
    newsfeed.startFeed();
    return { historicLimit: historicLimit ?? 100, newsfeed, newsfeedContainer };
  }, [
    session,
    displayType,
    expression,
    newsfeedSettings.jumpedToDate,
    timeZone,
    historicLimit,
    globalSettings.displayDateBanner,
  ]);

  // the following to determine if a story body is open or not
  const storyState = React.useRef<Set<Story>>(new Set<Story>());

  const closeStoryBody = React.useCallback((story: Story): void => {
    storyState.current.delete(story);
  }, []);

  const isStoryBody = React.useCallback((story: Story): boolean => {
    return storyState.current.has(story);
  }, []);

  const openStoryBody = React.useCallback((story: Story): void => {
    storyState.current.add(story);
  }, []);

  return (
    <AdvancedNewsfeedContext.Provider value={newsfeedValue}>
      <AdvancedNewsfeedStoryContext.Provider
        value={{
          closeStoryBody,
          isStoryBody,
          openStoryBody,
        }}
      >
        {props.children}
      </AdvancedNewsfeedStoryContext.Provider>
    </AdvancedNewsfeedContext.Provider>
  );
});

export default AdvancedNewsfeedContextProvider;

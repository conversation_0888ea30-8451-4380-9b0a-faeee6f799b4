import React, { useEffect } from 'react';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';

interface Props {
  widgetID?: string;
  nodeID?: string | number;
}

export const useHeadlineImpression = ({ nodeID, widgetID }: Props) => {
  const session = React.useContext(SessionContext);

  const trackHeadlineImpression = React.useCallback(
    (nodeID: string) => {
      const impressionSlug = `${widgetID}-${nodeID}`;

      if (!isGlobalImpressionStored(impressionSlug)) {
        storeGlobalImpression(impressionSlug);
        session.getManager(TrackingManager).trackHeadlineEvent('viewed_inline', {
          nodeID,
        });
      }
    },
    [widgetID, session],
  );

  useEffect(() => {
    if (nodeID && widgetID) {
      trackHeadlineImpression(String(nodeID));
    }
  }, [nodeID, widgetID, trackHeadlineImpression]);

  return { trackHeadlineImpression };
};

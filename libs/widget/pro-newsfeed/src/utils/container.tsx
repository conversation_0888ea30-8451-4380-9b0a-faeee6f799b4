import { NewsFeed, NewsFeedEvents, Story } from '@benzinga/advanced-news-manager';
import { SortedArrayBuffer, DisplayContainer, DisplayContainerEvent, Buffer, ArrayBuffer } from '@benzinga/containers';
import { DateTime } from 'luxon';

type StoryNewsfeedItem = { story: Story; type: 'story' };
type DateNewsfeedItem = { createdAtDate: Date; displayDate: string; type: 'date' };

export type AdvancedNewsfeedItem = StoryNewsfeedItem | DateNewsfeedItem;

export type AdvancedNewsfeedContainerEvent = DisplayContainerEvent<AdvancedNewsfeedItem>;

export class AdvancedNewsfeedContainer extends DisplayContainer<
  AdvancedNewsfeedItem,
  NewsFeedEvents,
  ArrayBuffer<AdvancedNewsfeedItem> | SortedArrayBuffer<AdvancedNewsfeedItem>
> {
  private newsfeed: NewsFeed;
  private setOfDates = new Set<string>();
  private uniqueStory = new Set<Story>(); // this removes duplicate stories. without this story updates will be treated
  // as different stories. #3620 notice how "The Marker in 5 Minutes" is listed 4 times. remember we process stories so
  // that they are the same reference.
  private timeZone = 'Africa/Abidjan';
  private displayTodaysDay = false;

  constructor(newsfeed: NewsFeed, MaxQueueSize = Infinity, timeZone = 'Africa/Abidjan', displayTodaysDay = false) {
    super(
      newsfeed,
      MaxQueueSize,
      new ArrayBuffer<AdvancedNewsfeedItem>(),
      new ArrayBuffer<AdvancedNewsfeedItem>(),
      new SortedArrayBuffer<AdvancedNewsfeedItem>(AdvancedNewsfeedContainer.sort),
    );
    this.newsfeed = newsfeed;
    this.timeZone = timeZone;
    this.displayTodaysDay = displayTodaysDay;
  }

  private static sort = (a: AdvancedNewsfeedItem, b: AdvancedNewsfeedItem): -1 | 0 | 1 => {
    const createdAtSort = (a: AdvancedNewsfeedItem, b: AdvancedNewsfeedItem): -1 | 0 | 1 =>
      AdvancedNewsfeedContainer.getCreatedAt(a) < AdvancedNewsfeedContainer.getCreatedAt(b)
        ? -1
        : AdvancedNewsfeedContainer.getCreatedAt(a) > AdvancedNewsfeedContainer.getCreatedAt(b)
          ? 1
          : 0;
    if (a.type === 'date' || b.type === 'date') {
      return createdAtSort(a, b);
    }
    return createdAtSort(a, b);
  };

  private static getCreatedAt = (item: AdvancedNewsfeedItem): DateTime => {
    switch (item.type) {
      case 'story':
        return item.story.getCreatedAtOffsetDate();
      case 'date':
        return DateTime.fromJSDate(item.createdAtDate);
    }
  };

  public getHistoric = (numStoriesRequested = 100, direction: 'past' | 'future' = 'past') => {
    return this.newsfeed.getHistoric(numStoriesRequested, direction);
  };

  public getDisplayItemsFormattedLength = (displayTodaysDay: boolean): number => {
    return this.getDisplayItemsFormatted(displayTodaysDay).length;
  };

  public getDisplayItemsFormatted = (displayFirstDay: boolean): readonly AdvancedNewsfeedItem[] => {
    const buff = this.getDisplayItems();
    if (displayFirstDay) {
      return buff;
    } else {
      const lastItem = buff[buff.length - 1];

      if (lastItem && lastItem.type === 'date') {
        return buff.slice(0, -1);
      } else {
        return buff;
      }
    }
  };

  protected pushItemsHandle = (
    items: AdvancedNewsfeedItem[],
    buffer: Buffer<AdvancedNewsfeedItem>,
    bufferType: 'live' | 'historic',
    _status: 'live' | 'future' | 'historic',
  ): void => {
    if (!Array.isArray(items) || !buffer) {
      return;
    }

    const now = DateTime.now().setZone(this.timeZone);

    items.forEach(newsfeedItem => {
      if (this.shouldProcessDateSortedItem(newsfeedItem, bufferType)) {
        this.processDateSortedItem(newsfeedItem, buffer, now);
      } else {
        this.addItemToBuffer(newsfeedItem, buffer, bufferType);
      }
    });
  };

  protected onMessage = (event: NewsFeedEvents): void => {
    switch (event.type) {
      case 'news:new_stories':
      case 'news:reconnect_stories':
        this.pushLiveItems(this.processStory(event.stories));
        break;
      case 'news:past_historic_stories':
        this.pushHistoricItems(this.processStory(event.stories));
        break;
      case 'news:future_historic_stories':
        this.pushFutureItems(this.processStory(event.stories));
        break;
      case 'news:close':
        this.clear();
        break;
    }
  };

  protected onClear = (): void => this.uniqueStory.clear();

  private processStory = (stories: Story[]): AdvancedNewsfeedItem[] =>
    stories.reduce<AdvancedNewsfeedItem[]>((acc, story) => {
      story.setCreatedAtOffsetDate(this.timeZone);

      if (this.uniqueStory.has(story)) {
        return acc;
      } else {
        this.uniqueStory.add(story);
      }
      const date = story.getCreatedAtOffsetDate();
      const midnightDate = date.plus({ days: 1 }).startOf('day');
      const displayDate = story.getDisplayDateString();
      if (!this.setOfDates.has(displayDate) && !date.hasSame(DateTime.now().setZone(this.timeZone), 'day')) {
        this.setOfDates.add(displayDate);
        acc.push({
          createdAtDate: midnightDate.toJSDate(),
          displayDate,
          type: 'date',
        });
      }
      acc.push({ story: story, type: 'story' });
      return acc;
    }, []);

  /**
   * Determines if item should be processed with date-based sorting logic
   */
  private shouldProcessDateSortedItem = (item: AdvancedNewsfeedItem, bufferType: 'live' | 'historic'): boolean => {
    // Live story items with valid dates
    if (bufferType === 'live' && item.type === 'story' && item.story?.getCreatedAtOffsetDate()) {
      return true;
    }
    // Date items with valid dates
    if (bufferType === 'live' && item.type === 'date' && item.createdAtDate) {
      return true;
    }

    return false;
  };

  /**
   * Processes items that need date-based sorting and positioning
   */
  private processDateSortedItem = (
    item: AdvancedNewsfeedItem,
    buffer: Buffer<AdvancedNewsfeedItem>,
    now: DateTime,
  ): void => {
    const itemDate = this.getItemDateTime(item);
    const isToday = itemDate.hasSame(now, 'day');

    if (isToday) {
      buffer.push(item);
    } else {
      this.insertItemByDate(item, buffer);
    }
  };

  /**
   * Gets DateTime for any newsfeed item
   */
  private getItemDateTime = (item: AdvancedNewsfeedItem): DateTime => {
    return item.type === 'date'
      ? DateTime.fromJSDate(item.createdAtDate).minus({ days: 1 })
      : item.story.getCreatedAtOffsetDate();
  };

  /**
   * Gets the raw Date for comparison (used for insertion sorting)
   */
  private getItemRawDate = (item: AdvancedNewsfeedItem): Date => {
    return item.type === 'date' ? item.createdAtDate : item.story.getCreatedAtDate();
  };

  /**
   * Inserts item in correct chronological position for non-today items
   */
  private insertItemByDate = (item: AdvancedNewsfeedItem, buffer: Buffer<AdvancedNewsfeedItem>): void => {
    const itemDateToCompare = this.getItemRawDate(item);
    const displayItems = this.getDisplayItems();

    const insertionIndex = displayItems.findIndex(
      bufferItem => bufferItem.type === 'story' && bufferItem.story?.getCreatedAtDate() > itemDateToCompare,
    );

    if (insertionIndex !== -1 && buffer.splice) {
      buffer.splice(insertionIndex, 0, item);
    } else if (buffer.unshift) {
      buffer.unshift(item);
    }
  };

  /**
   * Adds item to buffer based on buffer type (for items not needing date sorting)
   */
  private addItemToBuffer = (
    item: AdvancedNewsfeedItem,
    buffer: Buffer<AdvancedNewsfeedItem>,
    bufferType: 'live' | 'historic',
  ): void => {
    if (bufferType === 'historic') {
      buffer.unshift?.(item);
    } else {
      buffer.push(item);
    }
  };
}

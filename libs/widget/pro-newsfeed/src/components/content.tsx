'use client';
import React from 'react';
import Hooks from '@benzinga/hooks';

import { EventKey } from '@benzinga/fission';
import styled from '@benzinga/themetron';
import { isEmpty, isNil } from 'ramda';

import { isNull } from 'ramda-adjunct';
import { noop } from '@benzinga/utils';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { buildBeatsAndMissesRegex, buildKeyPhrasesRegex, formatKeyword, highlightKeywords } from '../utils/highlight';
import { isLinkClick } from '../utils/domUtils';
import { Story } from '@benzinga/advanced-news-manager';
import { useNewsfeedGlobalSettings } from '../utils/useGlobalSettings';
import { debounce } from '@benzinga/utils';

interface Props {
  content: string;
  iframe?: boolean;
  story?: Story;
  storyBody: boolean;
  wrapLinks?: boolean;
  computeHeight(): void;
  onIframeRendered?(size: number): void;
  onLinkClick?(event: React.MouseEvent<HTMLElement>): void;
}

const wrapHtml = (content: string) => `
  <!DOCTYPE html>
  <html>
    <head>
      <style>
        .standout--positive {
          color: limegreen
        }
        .standout--negative {
          color: red
        }
      </style>
    </head>
    <body>
      ${content}
    </body>
  </html>
`;

const resolveImageOnLoad = (image: HTMLImageElement) => {
  return new Promise<void>(resolve => {
    image.onload = () => {
      resolve();
    };
  });
};

export const NewsfeedContent: React.FC<Props> = props => {
  const debouncedHandleResize = () => debounce(handleResize, 100);
  const { computeHeight, storyBody } = props;
  const content = React.useMemo(() => sanitizeHTML(props.content), [props.content]);

  const [iframe, setIframe] = Hooks.useMountUnmountRef<HTMLIFrameElement>(
    (iframe: HTMLIFrameElement) => {
      const doc = iframe.contentDocument;
      if (doc === null) {
        return;
      }

      iframe.contentWindow?.document.addEventListener('DOMContentLoaded', resize);

      doc.open('text/html', 'replace');
      doc.write(wrapHtml(highlightedHtml));
      doc.close();

      const iframeWindow = iframe.contentWindow;

      if (iframeWindow) {
        iframeWindow.addEventListener(EventKey.resize, debouncedHandleResize);
      }

      if (doc) {
        if (doc.readyState === 'complete') {
          handleLoad();
        } else {
          iframe.addEventListener(EventKey.load, handleLoad);
        }
      }
    },
    (iframe: HTMLIFrameElement) => {
      const iframeWindow = iframe.contentWindow;
      if (iframeWindow) {
        iframeWindow.removeEventListener(EventKey.resize, debouncedHandleResize);
      }

      const doc = iframe.contentDocument;

      if (doc) {
        iframe.removeEventListener(EventKey.load, handleLoad);
        doc.body.removeEventListener(EventKey.click, handleClick);
      }
    },
  );

  const handleClick = (event: MouseEvent) => {
    if (isLinkClick(event) && props.onLinkClick) {
      props.onLinkClick(event);
    }
  };

  const handleLoad = () => {
    if (iframe.current) {
      const doc = iframe.current.contentDocument;

      if (doc) {
        doc.body.addEventListener(EventKey.click, handleClick);

        // Remove existing base tags if they exist
        // (If it does exist there should only be one, this is just to make sure)
        const existingBaseTags = doc.head.getElementsByTagName('base');
        Array.from(existingBaseTags).forEach(baseTag => {
          baseTag.remove();
        });

        // Add base tag with target="_blank" to make links open in a new window or tab
        const baseTag = doc.createElement('base');
        baseTag.target = '_blank';
        doc.head.appendChild(baseTag);
      }
    }

    handleResize();
  };

  const resize = () => {
    const { onIframeRendered } = props;
    if (onIframeRendered) {
      const size = getIframeSize();
      if (size !== null) {
        onIframeRendered(size);
      }
    }
  };

  const getIframeSize = () => {
    if (iframe.current === null) {
      return null;
    }

    const doc = iframe.current.contentDocument;
    if (doc === null) {
      return null;
    }

    const scrollHeight = doc.body.scrollHeight;
    const clientHeight = doc.body.clientHeight;

    const scrollbarVisible = scrollHeight > clientHeight;

    // Adding 50px in an attempt to remove the scrollbar
    // We check if scrollbar is visible before we add 50px to fix a bug in edge where the container infinitely grows
    return scrollbarVisible ? scrollHeight : scrollHeight + 50;
  };

  const handleResize = () => {
    if (props.onIframeRendered) {
      const size = getIframeSize();
      if (!isNull(size)) {
        if (size <= 50) {
          iframe.current?.contentWindow?.document.addEventListener('DOMContentLoaded', resize);
        } else {
          props.onIframeRendered(size);
        }
      }
    }
  };

  const CONTENT = React.useRef<HTMLSpanElement>(null);

  React.useEffect(() => {
    if (storyBody && CONTENT.current) {
      const images = CONTENT.current.getElementsByTagName('img');
      if (isNil(images) || isEmpty(images)) {
        return;
      }
      Promise.all(Array.from(images).map(resolveImageOnLoad)).then(computeHeight).catch(noop);
    }
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    return () => {};
  }, [computeHeight, content, storyBody]);

  const { highlightBeatsMisses, highlightDollarAmounts, highlightPercentages, highlightQuarters } =
    useNewsfeedGlobalSettings();

  const keyPhrasesRegex = React.useMemo(
    () => buildKeyPhrasesRegex(highlightDollarAmounts, highlightPercentages, highlightQuarters),
    [highlightDollarAmounts, highlightPercentages, highlightQuarters],
  );
  const beatsAndMissesRegex = React.useMemo(() => buildBeatsAndMissesRegex(), []);

  const highlightOptions = React.useMemo(() => {
    const response: {
      regex: RegExp | null;
      replace: (match: string) => string;
      test: (story: Story | undefined, content: string) => boolean;
    }[] = [];

    //beats and misses
    response.push({
      regex: beatsAndMissesRegex,
      replace: match => {
        const m1 = match[0];
        return formatKeyword(match, m1 === 'M' || m1 === 'm');
      },
      test: (story: Story | undefined, content: string) => {
        const ct = content.toLowerCase();
        return (
          !props.storyBody &&
          highlightBeatsMisses &&
          (story?.getChannels()?.some(channel => channel.name === 'Earnings') ?? false) &&
          (ct.includes('eps') || ct.includes('sales')) &&
          (ct.includes('beat') || ct.includes('miss'))
        );
      },
    });

    // default case
    response.push({
      regex: keyPhrasesRegex,
      replace: match => {
        const m1 = match[0];
        const m2 = match[1];
        return formatKeyword(match, m1 === '-' || m1 === '(' || (m1 === '$' && (m2 === '-' || m2 === '(')));
      },
      test: () => true,
    });

    return response;
  }, [beatsAndMissesRegex, highlightBeatsMisses, keyPhrasesRegex, props.storyBody]);

  const highlightedHtml = React.useMemo(() => {
    let result = '';
    highlightOptions.some(option => {
      if (option.test(props.story, content)) {
        result = highlightKeywords(content, option.regex, option.replace, props.wrapLinks);
        return true;
      }
      return false;
    });
    return result;
  }, [highlightOptions, props.story, props.wrapLinks, content]);

  if (props.iframe) {
    return (
      <IframeWrapper>
        <IFrame allow="autoplay" ref={setIframe} />
      </IframeWrapper>
    );
  } else {
    return <span dangerouslySetInnerHTML={{ __html: highlightedHtml }} onClick={props.onLinkClick} ref={CONTENT} />;
  }
};

const IFrame = styled.iframe`
  height: 100%;
  width: 100%;
`;

const IframeWrapper = styled.div`
  background-color: white;
  border: 1px solid ${props => props.theme.colors.backgroundActive};
  flex: 1;
  margin: 0.5em 40px;
`;

export default NewsfeedContent;

'use client';
import styled from '@benzinga/themetron';
import React, { useState } from 'react';
import { Filters } from '@benzinga/pro-scanner-widget';
import { SessionContext } from '@benzinga/session-context';
import { FilterObject, QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';

interface SelectOption {
  id: string;
  name: string;
  value: string[];
}

interface Props {
  field: PresetFilter | null;
  filters: FilterObject[];
  isMillerColumns?: boolean;
  marketCapOptions?: SelectOption[];
  priceOptions?: SelectOption[];
  volumeOptions?: SelectOption[];
  onFiltersChange(filters: FilterObject[]): void;
}

export enum PresetFilter {
  dVolume = 'dayVolume',
  mktCap = 'marketCap',
  price = 'price',
}

const NewsfeedScreenerFilters: React.FC<Props> = props => {
  const [filters, setFilters] = useState(props.filters);
  const filterValues: string[][] = React.useMemo(
    () => filters?.map(filter => filter.parameters.map(String)),
    [filters],
  );
  const session = React.useContext(SessionContext);
  const scanner = session.getManager(QuotesV3FieldsManager);

  const { field, marketCapOptions, priceOptions, volumeOptions } = props;
  const dataFiled = field && scanner.getScannerDefFromId(field);
  const dataFields = dataFiled ? [dataFiled] : scanner.getFilterableFields() ?? [];

  const handleFiltersChanged = React.useCallback(
    (filters: FilterObject[]) => {
      const onFiltersChange = props.onFiltersChange;
      setFilters(filters);
      onFiltersChange(filters);
    },
    [props.onFiltersChange],
  );

  const getActiveClass = (filterOptionValue: string[]): string =>
    filterValues.some(filterValue => filterOptionValue.every((v, i) => v === filterValue[i])) ? 'active' : '';

  const buildListItems = (options: SelectOption[]) => {
    return options.map(option => {
      return (
        <CustomisedListItem
          className={getActiveClass(option.value)}
          key={option.id}
          onClick={() => handlePresets(option.value, props.field)}
          value={option.value}
        >
          <ListItemValue field={props.field}>{option.name}</ListItemValue>
        </CustomisedListItem>
      );
    });
  };

  const handlePresets = (value: string[], fieldName: string | null) => {
    if (value.length !== 2) {
      console.error('value.length != 2', { value });
      return;
    }

    if (value[0] === '' && value[1] === '') {
      const filter: FilterObject[] = filters.filter(f => f.field !== fieldName);
      setFilters(filter);
      props.onFiltersChange(filter);
      return;
    }
    const from = value[0];
    const to = value[1];

    const parameters = [from, to];
    let modifiedFilter;

    // FIXME: ScannerProtos.DataField asking for non-static method, toJSON() definition explicitly

    if (props.field === PresetFilter.price) {
      modifiedFilter = 'price';
    } else if (field === PresetFilter.mktCap) {
      modifiedFilter = 'marketCap';
    } else if (field === PresetFilter.dVolume) {
      modifiedFilter = 'dayVolume';
    }

    const filterObj: FilterObject = {
      field: modifiedFilter as any,
      operator: 'bt',
      parameters,
    };

    let modifiedFilters = [...filters];

    if (modifiedFilters.some(filter => filter.field === filterObj.field)) {
      const foundIndex = modifiedFilters.findIndex(x => x.field === filterObj.field);
      modifiedFilters[foundIndex] = filterObj;
    } else {
      modifiedFilters = [...filters, filterObj];
    }

    setFilters(modifiedFilters);
    props.onFiltersChange(modifiedFilters);
  };

  let dropdownOptions;

  switch (field) {
    case PresetFilter.price:
      dropdownOptions = buildListItems(priceOptions as SelectOption[]);
      break;
    case PresetFilter.mktCap:
      dropdownOptions = buildListItems(marketCapOptions as SelectOption[]);
      break;
    case PresetFilter.dVolume:
      dropdownOptions = buildListItems(volumeOptions as SelectOption[]);
      break;
  }

  const listContent = <CustomisedList>{dropdownOptions}</CustomisedList>;
  const customContent = (
    <CustomFilterFieldHeader>
      Custom {field === PresetFilter.price ? 'Price' : field === PresetFilter.mktCap ? 'Market Cap' : 'Volume'}
    </CustomFilterFieldHeader>
  );

  const filterContent = field ? (
    <>
      <DropdownContainer>{listContent}</DropdownContainer>
      <CustomFilterFieldContainer>
        {customContent}
        <Filters dataFields={dataFields} filters={filters} isScreener={false} onFiltersChanged={handleFiltersChanged} />
      </CustomFilterFieldContainer>
    </>
  ) : (
    <Filters
      dataFields={dataFields}
      filters={filters}
      isMillerColumns={props.isMillerColumns}
      isScreener={false}
      onFiltersChanged={handleFiltersChanged}
    />
  );

  return filterContent;
};

const DropdownContainer = styled.div`
  text-align: center;
`;

const CustomFilterFieldContainer = styled.div`
  font-size: 10px;
`;

const CustomFilterFieldHeader = styled.span`
  margin-left: 2px;
`;

const CustomisedList = styled.ul`
  font-size: 11px;
  height: 203px;
  margin-bottom: 0;
  overflow-y: auto;
`;

const CustomisedListItem = styled.li`
  border: 0.01em solid ${props => props.theme.colors.backgroundInactive};
  color: ${props => props.theme.colors.foreground};
  height: 20px;
  padding-left: 5px;
  text-align: left;
  &:hover {
    background: ${props => props.theme.colors.background};
    border: 0.01em solid ${props => props.theme.colors.brand};
    color: ${props => props.theme.colors.brand};
    cursor: pointer;
  }
  &.active {
    background: ${props => props.theme.colors.brand} !important;
    color: ${props => props.theme.colors.accentForeground} !important;
  }
`;

const ListItemValue = styled.p<Pick<Props, 'field'>>`
  margin-top: -2px;
  max-width: ${(props: Pick<Props, 'field'>) =>
    props.field === PresetFilter.price ? '90px' : props.field === PresetFilter.mktCap ? '130px' : '100px'};
  min-width: ${(props: Pick<Props, 'field'>) =>
    props.field === PresetFilter.price ? '59px' : props.field === PresetFilter.mktCap ? '118px' : '52px'};
`;

export default NewsfeedScreenerFilters;

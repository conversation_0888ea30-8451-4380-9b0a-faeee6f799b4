import {
  SubscribableWidgetWrapper,
  WidgetId,
  WidgetInstance,
  WidgetToolsManager,
  WidgetManifest,
  widgetsToSubscribable,
} from '@benzinga/widget-tools';
import { ScreenerFilterOperator, Signal, SignalFeed, SignalType, SignalsManager } from '@benzinga/signals-manager';
import { autoCompleteSymbols, autoCompleteSymbolsUpdateCallback } from '@benzinga/pro-ui';
import { DefaultTableParameters } from '@benzinga/ag-grid-utils';
import { SignalsWidgetIteration, SignalsWidgetAllIterations } from './entities';
import { SignalsWidgetMigrator } from './migrator';
import { SubscribableEventType } from '@benzinga/subscribable';
import { StockSymbol } from '@benzinga/session';
import { processSignalsNotification } from './signalsUtils';
import { Signals } from './signals';
import { Signals as SignalsIcon } from '@benzinga/valentyn-icons';

export const SignalsWidgetManifest: WidgetManifest<'signals', SignalsWidgetIteration, SignalsWidgetAllIterations> = {
  SettingsRender: undefined,
  WidgetRender: Signals,
  daemon: session => {
    const WidgetsWithSoundNotifications = widgetsToSubscribable<
      SignalsWidgetIteration['widgetParameters'],
      SubscribableEventType<SignalFeed> & { widget: WidgetInstance<SignalsWidgetIteration['widgetParameters']> }
    >(
      session,
      SignalsWidgetManifest,
      widget => widget.getParameters().enabledNotifications.length > 0,
      widget => {
        const signalsFeed = session.getManager(SignalsManager).createFeed();

        const update = (symbols: Set<StockSymbol> | undefined) =>
          signalsFeed.setFilters(
            [
              ...widget.getParameters().config.screenerFilters,
              {
                fieldId: 'symbol',
                operator: ScreenerFilterOperator.includes,
                parameter: symbols ? Array.from(symbols) : [],
                type: 'symbol',
              },
            ],
            widget.getParameters().config.selectedSignalTypes,
          );

        const filters = widget.getParameters().config.filters;
        const symbolsSet = autoCompleteSymbols(filters, session);
        update(symbolsSet);
        autoCompleteSymbolsUpdateCallback(session, filters, update, symbolsSet);
        signalsFeed.open();
        return new SubscribableWidgetWrapper(signalsFeed, widget);
      },
    );

    const subscription = WidgetsWithSoundNotifications.subscribe(event => {
      switch (event.type) {
        case 'signals:live_signal':
          processSignalsNotification(
            session,
            event.signal,
            event.widget.getParameters().enabledNotifications,
            event.widget.getWidgetId(),
          );
          break;
      }
    });

    session.getManager(WidgetToolsManager).onDispatchEventToDaemon(SignalsWidgetManifest, event => {
      switch (event.type) {
        case 'play_sound':
          event.signal &&
            event.widgetId &&
            processSignalsNotification(
              session,
              event.signal as Signal,
              (
                session
                  .getManager(WidgetToolsManager)
                  .getWidgetParameters(event.widgetId as WidgetId) as SignalsWidgetIteration['widgetParameters']
              ).enabledNotifications,
              event.widgetId as WidgetId,
            );
          break;
      }
    });
    session.getManager(WidgetToolsManager).onShutdown(() => subscription.unsubscribe());
  },
  defaultGlobalParameters: {},
  defaultWidgetParameters: {
    config: {
      filters: [],
      screenerFilters: [],
      selectedSignalTypes: [SignalType.spike_up, SignalType.spike_down],
      sendGroup: null,
    },
    enabledNotifications: [],
    flightMode: false,
    table: {
      ...DefaultTableParameters,
      columns: [
        {
          colId: 'symbol',
          hide: false,
          width: 90,
        },
        {
          colId: 'signalType',
          hide: false,
        },
        {
          colId: 'date',
          hide: false,
        },
        {
          colId: 'session-count',
          hide: false,
        },
        {
          colId: 'type',
          hide: false,
        },
        {
          colId: 'description',
          hide: false,
          width: 500,
        },
      ],
    },
  },
  description: 'Signals',
  icon: SignalsIcon,
  id: 'signals',
  initWidgetParameters: widgetParameters => {
    const parameters = widgetParameters ?? {
      version: SignalsWidgetManifest.version,
      widgetParameters: SignalsWidgetManifest.defaultWidgetParameters,
    };

    const update = (
      parameters: Exclude<typeof widgetParameters, undefined>,
    ): SignalsWidgetIteration['widgetParameters'] => {
      switch (parameters.version) {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
          return SignalsWidgetManifest.defaultWidgetParameters;
        case 6:
          return {
            ...parameters.widgetParameters,
            config: {
              ...parameters.widgetParameters.config,
              filters: parameters.widgetParameters.config?.filters ?? [],
              screenerFilters: parameters.widgetParameters.config?.screenerFilters ?? [],
              selectedSignalTypes:
                parameters.widgetParameters.config?.selectedSignalTypes &&
                parameters.widgetParameters.config?.selectedSignalTypes.length > 0
                  ? parameters.widgetParameters.config?.selectedSignalTypes
                  : [SignalType.spike_up, SignalType.spike_down],
              sendGroup: parameters.widgetParameters.config?.sendGroup ?? null,
            },
            enabledNotifications: parameters.widgetParameters.enabledNotifications ?? [],
            table: parameters.widgetParameters.table ?? SignalsWidgetManifest.defaultWidgetParameters.table,
          };
      }
    };

    return update(parameters);
  },
  menuItem: true,
  migrator: SignalsWidgetMigrator,
  name: 'Signals',
  permission: { action: 'bzpro/widget/use', resource: 'signals' },
  state: 'production',
  version: 6,
};

export const findNearestDot = (marks, value: number, type = 'min' || 'max') => {
  const values = Object.values(marks);
  const convertedTargetValue = fromShorthand(String(value)) ?? 0;

  return values.reduce<number>(
    (closest, curr, index) => {
      const currValue = fromShorthand(String(curr)) ?? 0;
      const currentDiff = Math.abs(convertedTargetValue - currValue);
      const closestValue = fromShorthand(String(values[closest] ?? '')) ?? 0;
      const closestDiff = Math.abs(convertedTargetValue - closestValue);

      if (type === 'min' && currValue > convertedTargetValue) {
        return closest;
      }
      if (type === 'max' && currValue < convertedTargetValue) {
        return closest;
      }

      return currentDiff < closestDiff ? index : closest;
    },
    type === 'min' ? 0 : values.length - 1,
  );
};

export const paramToValue = (param: string) => {
  const lessThanSign = '\u003c';
  const greaterThanSign = '\u003e';
  switch (param) {
    case '':
    case '__CUSTOM':
    case greaterThanSign:
    case lessThanSign:
      return '';
    default:
      return fromShorthand(param);
  }
};

export const infinityParamToValue = (param: string) => {
  switch (param) {
    case 'Infinity':
    case '-Infinity':
      return '';
    default:
      return toShorthand(fromShorthand(param));
  }
};

export function fromShorthand(shorthand: string): number | undefined {
  if (shorthand === '') {
    return undefined;
  } else if (shorthand === 'Infinity') {
    return Infinity;
  } else if (shorthand === '-Infinity') {
    return -Infinity;
  } else {
    // Clean the string up, remove commas and whitespace
    shorthand = shorthand.toLowerCase().replaceAll(/[, ]+/g, '');

    let ch = shorthand.length > 0 ? shorthand[shorthand.length - 1] : undefined;

    //if last character is not a digit, remove it for integer parsing
    if (ch != null && !(ch[0] >= '0' && ch[0] <= '9')) {
      shorthand = shorthand.substring(0, shorthand.length - 1);
    } else if (ch != null && ch[0] >= '0' && ch[0] <= '9') {
      ch = undefined;
    }

    //strict number conversion from string
    const value = Number(shorthand);

    if (isNaN(value)) return NaN;
    switch (ch) {
      case 'b':
        return value * 1e9;
      case 't':
        return value * 1e12;
      case 'm':
        return value * 1e6;
      case 'k':
        return value * 1e3;
      case undefined:
        return value;
      default:
        return NaN;
    }
  }
}

/**
 * Converts a number to shorthand string, i.e., 1000 -> 1K
 */
export const toShorthand = (value: number | undefined): string => {
  if (value === undefined) return '';
  switch (true) {
    case value === 0:
      return '0';
    case value === Infinity:
      return 'Infinity';
    case value === -Infinity:
      return '-Infinity';
    case String(value) === '<' || String(value) === '>':
      return String(value);
    case !value:
      return '';
    case value < 1e3:
      return String(value);
    case value < 1e6:
      return toString(value / 1e3, 'K');
    case value < 1e9:
      return toString(value / 1e6, 'M');
    case value < 1e12:
      return toString(value / 1e9, 'B');
    default:
      return toString(value / 1e12, 'T');
  }
};

const toString = (value: number, suffix: string) =>
  `${value
    .toFixed(2)
    .toString()
    .replaceAll(/\.?0+$/g, '')}${suffix}`;

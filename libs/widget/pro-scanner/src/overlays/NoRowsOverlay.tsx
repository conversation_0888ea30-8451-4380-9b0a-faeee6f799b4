import styled from 'styled-components';

interface NoRowsOverlayProps {
  icon?: React.ReactNode;
  message: string;
}

export const NoRowsOverlay: React.FC<NoRowsOverlayProps> = props => {
  return (
    <OverlayContainer>
      {props.icon}
      {props.message}
    </OverlayContainer>
  );
};
const OverlayContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 8px;
  text-align: center;
  background-color: ${({ theme }) => theme.colors.backgroundInactive};
`;

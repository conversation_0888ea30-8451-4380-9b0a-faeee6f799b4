import { getTimeDisplayFormat } from '@benzinga/date-utils';
import Hooks from '@benzinga/hooks';
import { ScannerFeed } from '@benzinga/scanner-manager';
import { SubscribableEventType } from '@benzinga/subscribable';
import { useTime } from '@benzinga/time-manager-hooks';
import { DateTime } from 'luxon';
import React from 'react';
import styled, { css } from 'styled-components';
import { useScannerFeedContext } from '../ScannerContext';
import { LoadingOutlined } from '@ant-design/icons';

export const Status: React.FC<{ paused: boolean; isScreener?: boolean }> = props => {
  const { timeFormat, timezone } = useTime();
  const feed = useScannerFeedContext();

  const [event, setEvent] = React.useState<SubscribableEventType<ScannerFeed> | null>(null);

  Hooks.useSubscriber(props.paused ? undefined : feed, event => {
    switch (event.type) {
      case 'data_update':
      case 'error':
        setEvent(event);
        break;
    }
  });

  if (event === null) {
    return null;
  }
  switch (event.type) {
    case 'error': {
      return (
        <ErrorWrapper>
          <LoadingOutlined spin style={{ fontSize: 14 }} /> {event.error.message}
        </ErrorWrapper>
      );
    }
    case 'data_update': {
      const format = getTimeDisplayFormat({ timeFormat });
      const dateTime = DateTime.fromJSDate(new Date());
      const dateTimeWithTimezone = dateTime?.setZone(timezone);
      const formatted = dateTimeWithTimezone?.isValid
        ? dateTimeWithTimezone.toFormat(format)
        : dateTime?.toFormat(format);
      return (
        <StatusDiv isScreener={props.isScreener}>
          {`Showing ${event.rows.length} of ${event.totalInstruments}, `}
          {formatted !== 'Invalid DateTime' && `Last updated: ${formatted}`}
        </StatusDiv>
      );
    }
    default:
      return null;
  }
};

const Dot = styled.div`
  color: green;
  display: inline-block;
`;

const ErrorWrapper = styled.div`
  border: 1px solid red;
  padding: 5px;
`;

const StatusDiv = styled.div<{ isError?: boolean; isScreener?: boolean }>`
  font-size: smaller;
  white-space: nowrap;

  ${props =>
    props.isScreener
      ? css`
          color: black;
        `
      : ''}

  ${props =>
    props.isError
      ? css`
          background: red !important;
          color: #fff;
        `
      : ''}
  ${Dot} {
    ${props =>
      props.isError
        ? css`
            color: red;
          `
        : ''}
  }
`;

import { createSymbolSearchItem, isGptSearchItem } from '@benzinga/search-modules';
import { GptSearchManager } from '@benzinga/benzinga-gpt-search-manager';
import { useScannerColumnableDefs } from '@benzinga/scanner-manager-hooks';
import ScannerGrid from './ScannerGrid';
import FiltersBar from './Toolbar/FiltersBar';
import { objectDeepEqual } from '@benzinga/utils';
import Toolbar from './Toolbar/Toolbar';
import Hooks from '@benzinga/hooks';
import { SendLinkContext, PermissionOverlay } from '@benzinga/pro-ui';
import { QuoteProtos, ScannerProtos } from '@benzinga/scanner-manager';
import { LoggingManager } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { useWidgetParameters } from '@benzinga/widget-tools';
import React from 'react';
import { ScannerToolbarDiv } from './Scanner';
import { useScannerFeed, ScannerFeedContextProvider } from './ScannerContext';
import { ScannerWidgetManifest } from './widget';
import styled, { css } from 'styled-components';
import { LoadingOutlined } from '@ant-design/icons';
import { Status } from './overlays/Status';
import Split from 'react-split';
import { Spinner } from '@benzinga/core-ui';
import { message } from 'antd';

interface ScannerViewProps {
  hideToolbar?: boolean;
  hideFooter?: boolean;
  isScreener?: boolean;
}

/**
 * Main scanner widget. Minus a settings button, which is handled from ScannerWidget.
 */
export const ScannerView: React.FC<ScannerViewProps> = props => {
  const session = React.useContext(SessionContext);
  const [messageApi, contextHolder] = message.useMessage();
  const { parameters, setParameters, setter } = useWidgetParameters(ScannerWidgetManifest);
  const [gptLoadingState, setGptLoadingState] = React.useState<{ loading: boolean; message: string }>({
    loading: false,
    message: '',
  });
  const feed = useScannerFeed(parameters.config);
  const [paused, setPaused] = React.useState(false);

  Hooks.useSubscriber(paused ? undefined : feed, event => {
    switch (event.type) {
      case 'error':
        session.getManager(LoggingManager).log(
          'info',
          {
            category: 'scanner',
            message: event.error?.message,
          },
          ['toast'],
        );
        break;
    }
  });

  const colDefs = useScannerColumnableDefs();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleRefreshClick = React.useCallback(
    (params: { key: string }) => {
      const setConfig = setter.config;
      const updateInterval = parseInt(params.key);
      let newRefreshInterval = -1;
      if (updateInterval === -2) {
        newRefreshInterval = -2;
      } else if (updateInterval > 0) {
        newRefreshInterval = updateInterval;
      } else {
        newRefreshInterval = -1;
      }
      setConfig(old => ({ ...old, refreshInterval: newRefreshInterval }));
    },
    [setter.config],
  );

  const pauseResumeClicked = React.useCallback(() => {
    if (paused) {
      setPaused(false);
    } else {
      setPaused(true);
    }
  }, [paused]);

  const removeGptSearchModuleItem = React.useCallback(() => {
    setter.config(old => ({
      ...old,
      tags: parameters.config.tags.filter(tag => !isGptSearchItem(tag)),
    }));
  }, [parameters.config.tags, setter]);

  const sendLink = React.useContext(SendLinkContext);

  const dataFieldClicked = React.useCallback(
    (dataFieldName: string, instrument: QuoteProtos.Quote) => {
      if (dataFieldName === 'symbol') {
        const onSymbolClick = sendLink.onSymbolClick;
        onSymbolClick(instrument.symbol);
      }
    },
    [sendLink.onSymbolClick],
  );

  const policy = props.isScreener
    ? { action: 'com/ad-block', resource: 'light' }
    : { action: 'bzpro/scanner/open', resource: '#' };

  const permission = [
    policy,
    parameters.config.filters.every(
      e =>
        e.field === 'subtype' &&
        e.operator === 'in' &&
        e.parameters.every(p => ['COMMON_SHARE', 'ADR', 'ETF'].includes(p)),
    ) &&
      parameters.config.tableParameters.columns.every(e =>
        ['symbol', 'changePercent', 'price', 'change', 'marketCap', 'dayVolume'].includes(e.colId),
      ) &&
      [60, -1].includes(parameters.config.refreshInterval),
  ];

  const DefinedScannerGrid = React.useMemo(
    () =>
      gptLoadingState.loading ? (
        <Spinner text={gptLoadingState.message} />
      ) : (
        <ScannerGrid
          isScreener={props.isScreener}
          key={'scanner-grid'}
          onDataFieldClicked={dataFieldClicked}
          paused={paused}
        />
      ),
    [dataFieldClicked, gptLoadingState, paused, props.isScreener],
  );

  const prompt = React.useMemo(() => {
    const promptsList = (parameters.config.tags ?? []).filter(isGptSearchItem) ?? [];
    return promptsList[promptsList.length - 1]?.data.prompt ?? '';
  }, [parameters.config.tags]);

  const fetchGptPromptResponse = React.useCallback(async () => {
    if (!prompt) return;

    try {
      setGptLoadingState({ loading: true, message: 'Fetching GPT response ...' });

      const gptSearchManager = session.getManager(GptSearchManager);
      const gptResponse = await gptSearchManager.getScannerConfig({
        prev_config: {
          fields: parameters.config.tableParameters.columns.map(col => col.colId),
          filters: parameters.config.filters,
          limit: parameters.config.limit,
          sortDir: ScannerProtos.SortDir[parameters.config.sortDir] as unknown as ScannerProtos.SortDir,
          sortField: parameters.config.sortField,
        },
        prompt,
      });

      const completions = gptResponse.ok?.completions;
      if (!completions || gptResponse.err) {
        messageApi.error({
          content: 'Failed to fetch GPT response',
        });
        removeGptSearchModuleItem();
        return;
      }

      setGptLoadingState({ loading: true, message: 'Updating your scanner config ...' });
      await new Promise(resolve => setTimeout(resolve, 0));

      const configDiff = objectDeepEqual(
        { ...parameters.config, columns: parameters.config.tableParameters.columns },
        { ...completions, columns: completions.fields?.map(col => ({ name: col, width: col.width })) },
      );

      if (configDiff) {
        return;
      }

      setGptLoadingState({ loading: true, message: 'Almost there ...' });
      await new Promise(resolve => setTimeout(resolve, 0));

      setParameters(old => ({
        ...old,
        columnHeaderGrouping: completions.columnHeaderGrouping ?? old.columnHeaderGrouping,
        config: {
          ...old,
          ...completions,
          sortDir:
            completions.sortDir !== undefined
              ? (ScannerProtos.SortDir[completions.sortDir] as unknown as ScannerProtos.SortDir)
              : parameters.config.sortDir,
          tableParameters: {
            columns: completions.fields?.map(col => ({ colId: col, hide: false, name: col, width: 120 })),
          },
          tags: parameters.config.tags.filter(tag => !isGptSearchItem(tag)).map(tag => createSymbolSearchItem(tag.id)),
        },
      }));
    } catch (error) {
      console.error('Error in GPT processing:', error);
      messageApi.error({
        content: 'An unexpected error occurred',
      });
    } finally {
      setGptLoadingState({ loading: false, message: '' });
    }
  }, [prompt, session, parameters.config, setParameters, messageApi, removeGptSearchModuleItem]);

  React.useEffect(() => {
    !gptLoadingState.loading && fetchGptPromptResponse();
  }, [fetchGptPromptResponse, gptLoadingState.loading]);

  if (!colDefs) {
    return (
      <div className="scanner">
        <LoadingOutlined spin style={{ fontSize: 14 }} /> Loading...
      </div>
    );
  }

  return (
    <ScannerFeedContextProvider feed={feed}>
      {contextHolder}
      <ScannerStyler isScreener={props.isScreener}>
        {!parameters.flightMode && !props.hideToolbar && (
          <ScannerToolbarDiv>
            <Toolbar
              isScreener={props.isScreener}
              onPauseClick={pauseResumeClicked}
              onRefresh={handleRefreshClick}
              paused={paused}
            />
          </ScannerToolbarDiv>
        )}

        {props.isScreener ? (
          <StyledSplit isScreener={props.isScreener}>
            <FiltersBar isScreener={props.isScreener} />
            {gptLoadingState.loading ? <>Updating your scanner config</> : DefinedScannerGrid}
          </StyledSplit>
        ) : !parameters.flightMode ? (
          !parameters.filtersMenuCollapsed ? (
            <StyledSplit direction="vertical" gutterSize={6}>
              <FiltersBar isScreener={props.isScreener} />
              <PermissionOverlay permissionsOr={permission}>{DefinedScannerGrid}</PermissionOverlay>
            </StyledSplit>
          ) : (
            <>
              <FiltersBar isScreener={props.isScreener} />
              <PermissionOverlay permissionsOr={permission}>{DefinedScannerGrid}</PermissionOverlay>
            </>
          )
        ) : (
          <PermissionOverlay permissionsOr={permission}>{DefinedScannerGrid}</PermissionOverlay>
        )}

        {!props.hideFooter && (
          <PermissionOverlay permissionsOr={permission}>
            <StatusHolderDiv>
              <Status isScreener={props.isScreener} paused={paused} />
            </StatusHolderDiv>
          </PermissionOverlay>
        )}
      </ScannerStyler>
    </ScannerFeedContextProvider>
  );
};

const ScannerStyler = styled.div<{ isScreener?: boolean }>`
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;

  .ag-cell-label-container .number {
    text-align: right !important;
    overflow: hidden !important;
  }

  .ag-cell-label-container .string {
    text-align: left !important;
    overflow: hidden !important;
  }

  .ag-cell.number {
    text-align: right;
  }

  .sort-desc {
    color: #fff;
    background: #11463b;
  }

  .sort-asc {
    color: #fff;
    background: #4b1c19;
  }

  .sort-desc-abs {
    color: #fff;
    background: #141742;
  }

  ${props =>
    props.isScreener
      ? css`
          .ag-cell-range-selected {
            background-color: white !important;
          }

          .bz-ag-grid-header:has(.bz-ag-grid-header-desc-abs) {
            color: white !important;
          }

          .bz-ag-grid-header:has(.bz-ag-grid-header-asc) {
            color: white !important;
          }

          .bz-ag-grid-header:has(.bz-ag-grid-header-desc) {
            color: white !important;
          }
        `
      : ''}
`;

const StatusHolderDiv = styled.div`
  padding: 4px;
  word-wrap: nowrap;
  overflow: hidden;
  background-color: ${props => props.theme.colors.backgroundActive};
`;

const StyledSplit = styled(Split)<{ isScreener?: boolean }>`
  flex-grow: 100;
  flex-shrink: 100;
  height: 10%;

  .gutter {
    cursor: ns-resize;
  }

  ${props =>
    props.isScreener
      ? css`
          display: flex;
          flex-direction: column;
          font-family: proxima-nova, helvetica, arial, sans-serif;
          font-size: 12px;
          .ant-slider-mark-text {
            font-size: 12px;
            font-family: proxima-nova, helvetica, arial, sans-serif;
          }
          .ant-slider-with-marks {
            margin-right: 10px;
          }
          .filter_summary,
          .remove {
            align-self: center;
          }
        `
      : ''}
`;

'use client';

import React, { Suspense, useEffect, useRef, useState, ComponentType, LazyExoticComponent } from 'react';
import { useIntersectionObserver } from '../hooks';

interface ImportOnVisibilityProps {
  [key: string]: any;
  component: () => Promise<{ default: ComponentType<any> }>;
  fallback?: React.ReactNode;
}

export const ImportOnVisibility: React.FC<ImportOnVisibilityProps> = ({
  component,
  fallback = <div>Loading...</div>,
  ...props
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const entry = useIntersectionObserver(containerRef as React.RefObject<Element>, {
    freezeOnceVisible: true,
    threshold: 0.1,
  });

  const isVisible = !!entry?.isIntersecting;

  const [LazyComponent, setLazyComponent] = useState<LazyExoticComponent<ComponentType<any>> | null>(null);

  useEffect(() => {
    if (isVisible && !LazyComponent) {
      const load = async () => {
        const loaded = await component();
        setLazyComponent(() => React.lazy(() => Promise.resolve(loaded)));
      };
      load();
    }
  }, [isVisible, component, LazyComponent]);

  return (
    <div ref={containerRef}>
      {LazyComponent && (
        <Suspense fallback={fallback}>
          <LazyComponent {...props} />
        </Suspense>
      )}
    </div>
  );
};

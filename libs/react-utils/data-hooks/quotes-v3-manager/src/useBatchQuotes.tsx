import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import { Quote } from '@benzinga/quotes-v3-manager';
import { DEFAULT_SCANNER_CONFIG, ScannerConfig } from '@benzinga/scanner-config-manager';
import { ScannerManager } from '@benzinga/scanner-manager';
import { SessionContext } from '@benzinga/session-context';
import React from 'react';

export const useBatchQuotesCallback = (
  symbols: string[],
  fields: Set<keyof Quote>,
  callback: (quotes: HoldingsQuote[]) => void,
  hasInitialQuotesLoaded = false,
  config?: Partial<ScannerConfig>,
) => {
  const session = React.useContext(SessionContext);
  const limit = 700;
  const fetchQuotes = React.useCallback(
    async (startIndex, endIndex) => {
      const quotes = await session.getManager(ScannerManager).getInstruments({
        ...DEFAULT_SCANNER_CONFIG,
        filters: [
          {
            field: 'symbol',
            operator: 'in',
            parameters: (symbols && symbols.slice(startIndex, endIndex)) ?? [],
          },
        ],
        limit,
        tableParameters: {
          ...DEFAULT_SCANNER_CONFIG.tableParameters,
          columns: [...Array.from(fields)].map(c => ({ colId: c })),
        },
        ...config,
      });

      if (quotes.err) {
        return;
      }

      return quotes.ok.instruments;
    },
    [session, symbols, fields, config],
  );

  const InitialQuoteFetch = React.useCallback(async () => {
    if (!symbols) return;

    const batchedQuotes: HoldingsQuote[] = [];

    for (let i = 0; i <= symbols.length; i += limit) {
      const quotes = await fetchQuotes(i, i + limit);
      if (quotes) {
        batchedQuotes.push(...(quotes as HoldingsQuote[]));
      }
    }

    if (batchedQuotes.length > 0) {
      callback(batchedQuotes);
    }
  }, [symbols, fetchQuotes, callback, limit]);

  React.useEffect(() => {
    !hasInitialQuotesLoaded && InitialQuoteFetch();
  }, [InitialQuoteFetch, hasInitialQuotesLoaded]);
};

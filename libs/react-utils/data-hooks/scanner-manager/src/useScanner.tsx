import React, { useEffect, useCallback, useState, useMemo } from 'react';
import { SessionContext } from '@benzinga/session-context';
import { <PERSON>annerManager, QuoteProtos, ScannerProtos, ScannerFeedEvent } from '@benzinga/scanner-manager';
import { DEFAULT_SCANNER_CONFIG, ScannerConfig, ScannerSource } from '@benzinga/scanner-config-manager';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import { SubscriptionType } from '@benzinga/subscribable';

interface useScannerInstruments {
  scannerData: QuoteProtos.IQuote[];
}

export const useScannerInstruments = (
  symbols: string[],
  columns: string[],
  filters?: FilterObject[],
  config?: Partial<ScannerConfig>,
): useScannerInstruments => {
  const [scannerData, setScannerData] = useState<QuoteProtos.IQuote[]>([]);
  const session = React.useContext(SessionContext);
  const feedSubscriptionRef = React.useRef<SubscriptionType<ScannerFeedEvent, unknown> | undefined>(undefined);

  const scannerConfig = useMemo(() => {
    return {
      ...DEFAULT_SCANNER_CONFIG,
      filters: [{ field: 'symbol', operator: 'in', parameters: symbols }, ...(filters ?? [])],
      limit: 100,
      refreshInterval: 10,
      sortDir: ScannerProtos.SortDir.ASC,
      source: 'stocks' as ScannerSource,
      tableParameters: {
        ...DEFAULT_SCANNER_CONFIG.tableParameters,
        columns: columns.map(col => ({
          colId: col,
        })),
      },
      ...config,
    };
  }, [columns, config, filters, symbols]);

  const fetchInstruments = useCallback(async () => {
    try {
      const data = await session.getManager(ScannerManager).getInstruments(scannerConfig);
      setScannerData(data?.ok?.instruments ?? []);
    } catch (error) {
      console.error('useScannerInstruments ERROR:', error);
    }
  }, [scannerConfig, session]);

  const initFeed = useCallback(async () => {
    if (feedSubscriptionRef.current) {
      feedSubscriptionRef.current.unsubscribe();
    }

    const feed = await session.getManager(ScannerManager).getFeed(scannerConfig);
    feedSubscriptionRef.current = feed.subscribe(event => {
      if (event.type === 'data_update') {
        const { rows } = event;
        setScannerData(rows ?? []);
      }
    });
  }, [session, scannerConfig]);

  useEffect(() => {
    if (symbols?.length && scannerConfig?.refreshInterval) {
      setTimeout(() => {
        initFeed();
      }, 3000);
    }
  }, [symbols, initFeed, scannerConfig?.refreshInterval]);

  useEffect(() => {
    if (symbols?.length) {
      fetchInstruments();
    }
  }, [symbols, fetchInstruments]);

  return {
    scannerData,
  };
};

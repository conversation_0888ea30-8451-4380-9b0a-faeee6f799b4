import { DataField, TargetSource } from '@benzinga/quotes-v3-fields-manager';
import { useCallback } from 'react';

interface useFilterSource {
  isFilterMatchSource: (filter?: DataField) => boolean;
}

export const useScannerFilterSource = (source: TargetSource = TargetSource.All): useFilterSource => {
  const isFilterMatchSource = useCallback(
    (filter?: DataField): boolean => {
      if (!filter) {
        return true;
      }

      if (filter.sourceSupport === TargetSource.All || !filter.sourceSupport) {
        return true;
      }

      return filter.sourceSupport === source;
    },
    [source],
  );

  return {
    isFilterMatchSource,
  };
};

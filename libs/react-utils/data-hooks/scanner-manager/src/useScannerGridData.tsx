import React from 'react';
import { SessionContext } from '@benzinga/session-context';
import { ScannerManager, ScannerProtos } from '@benzinga/scanner-manager';
import { DEFAULT_SCANNER_CONFIG } from '@benzinga/scanner-config-manager';
import { SafeError, SafePromise } from '@benzinga/safe-await';

interface getScannerGridDataParams {
  columns: { colId: string }[];
  limit?: number;
  symbols: string[];
}

interface UseScannerGridDataI {
  getScannerGridData: (params: getScannerGridDataParams) => SafePromise<ScannerProtos.IQueryResponse>;
}

export const useScannerGridData = (): UseScannerGridDataI => {
  const session = React.useContext(SessionContext);

  const getScannerGridData = React.useCallback(
    async ({ columns, limit = 1000, symbols }: getScannerGridDataParams): SafePromise<ScannerProtos.IQueryResponse> => {
      try {
        const result = await session.getManager(ScannerManager).getInstruments({
          ...DEFAULT_SCANNER_CONFIG,
          filters: [
            {
              field: 'symbol',
              operator: 'in',
              parameters: [...new Set(symbols)],
            },
          ],
          limit: limit,
          tableParameters: {
            ...DEFAULT_SCANNER_CONFIG.tableParameters,
            columns: columns,
          },
        });

        if (result?.ok) {
          return { ok: result.ok };
        } else {
          return { err: new SafeError('Failed to fetch scanner instruments', 'ERROR_TYPE') };
        }
      } catch (error: unknown) {
        if (error instanceof Error) {
          return { err: new SafeError(error.message, 'ERROR_TYPE') };
        }
        return { err: new SafeError('An unknown error occurred', 'ERROR_TYPE') };
      }
    },
    [session],
  );

  return { getScannerGridData };
};

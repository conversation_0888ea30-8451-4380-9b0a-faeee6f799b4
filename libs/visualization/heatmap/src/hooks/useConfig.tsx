import Hooks from '@benzinga/hooks';
import { ThemeContext } from '@benzinga/themetron';
import { formatLarge } from '@benzinga/utils';
import { formatNumberWithSuffix } from '@benzinga/visualization-utils';
import { WidgetContext } from '@benzinga/widget-tools';
import { EChartsCoreOption } from 'echarts';
import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { FaInfoCircle } from 'react-icons/fa';
import { IconType } from 'react-icons/lib';
import { TooltipProps } from '../heatmap';
import { useLogo } from '@benzinga/quotes-manager-hooks';
import { Logo } from '@benzinga/quotes-manager';

export const useConfig = (args, symbols, data, chartRef) => {
  const widgetContext = React.useContext(WidgetContext);
  const theme = React.useContext(ThemeContext);
  const [limitGroupChildren, setLimitGroupChildren] = React.useState(2);
  const [tooltip, setTooltip] = React.useState<TooltipProps>({ children: [], id: '', name: '', x: 0, y: 0 });
  const setDebouncedTooltip = Hooks.useDebounce(setTooltip, 100);
  const [logosMap, setLogosMap] = React.useState<Record<string, string>>({});

  const handleLogosUpdate = React.useCallback((newLogos: Logo[]) => {
    if (newLogos && newLogos.length > 0) {
      const logosMap = newLogos.reduce(
        (acc, logo) => {
          if (logo && logo.search_key && logo.files['mark_dark']) {
            acc[logo.search_key] = logo.files['mark_dark'];
          }
          return acc;
        },
        {} as Record<string, string>,
      );
      setLogosMap(logosMap);
    }
  }, []);

  useLogo(
    React.useMemo(() => symbols ?? [], [symbols]),
    {
      fields: 'mark_dark',
    },
    handleLogosUpdate,
  );

  const tooltipCoordinates = React.useCallback(
    (_pos, params, _dom, rect, _size) => {
      const [plotHeight, plotWidth] = [widgetContext.getClientHeight() ?? 0, widgetContext.getClientWidth() ?? 0];
      setLimitGroupChildren(plotWidth > 1000 && plotHeight > 400 ? 10 : plotWidth > 1000 && plotHeight > 300 ? 5 : 2);
      const [tooltipWidth, tooltipHeight] = params.data.children ? [500, 500] : [300, 300];
      const offset = 25;
      let tooltipX = rect.x + rect.width + offset;
      let tooltipY = rect.y;
      if (tooltipX + tooltipWidth + offset > plotWidth) {
        tooltipX = Math.max(0, rect.x - tooltipWidth - offset);
      }
      if (tooltipY + tooltipHeight > plotHeight) {
        tooltipY = Math.max(0, plotHeight - tooltipHeight);
      }
      if (tooltipX > plotWidth / 2) {
        tooltipX = tooltipX - tooltipWidth - offset;
      }
      if (tooltipY > plotHeight / 2) {
        tooltipY = tooltipY - tooltipHeight;
      }
      return [tooltipX, tooltipY];
    },
    [widgetContext],
  );

  const visualFormatter = React.useCallback(
    (value: number) => {
      const formattedValue = formatNumberWithSuffix(value, args.traces[0].color);
      return value < 0 ? `-${formattedValue}` : formattedValue;
    },
    [args.traces],
  );
  const iconToDataURL = React.useCallback((Icon: IconType, color = 'black'): string => {
    const svg = renderToStaticMarkup(React.createElement(Icon, { color }));
    const svgBlob = new Blob([svg], { type: 'image/svg+xml;charset=utf-8' });
    return URL.createObjectURL(svgBlob);
  }, []);

  const levels = React.useMemo(
    () => [
      {
        itemStyle: {
          borderColor: theme.colors.border,
        },
        upperLabel: {
          show: false,
        },
      },
      {
        emphasis: {
          itemStyle: {
            borderColor: theme.colors.buttonPrimaryBackgroundHover,
          },
        },
        itemStyle: {
          borderColor: theme.colors.border,
          borderWidth: 5,
        },
        upperLabel: {
          borderWidth: 2,
          color: theme.colors.foreground,
          ellipsis: '...',
          fontWeight: 'bold',
          overflow: 'truncate',
          show: true,
        },
      },
      {
        emphasis: {
          itemStyle: {
            borderColor: theme.colors.buttonPrimaryBackgroundHover,
            borderWidth: 3,
          },
        },
        itemStyle: {
          borderColor: theme.colors.border,
          borderWidth: 1,
          gapWidth: 1,
        },
      },
    ],
    [theme.colors.buttonPrimaryBackgroundHover, theme.colors.border, theme.colors.foreground],
  );

  const toolboxHoverText = React.useCallback(params => {
    switch (params.name) {
      case 'saveAsImage':
        return 'Save';
      case 'restore':
        return 'Refresh';
      case 'myInfoBtn':
        return `Note: Use mouse wheel to zoom in and out. Drag zoomed map to pan it & clicking on the ticker node opens up the details widget with that ticker`;
    }
    return '';
  }, []);

  const options: EChartsCoreOption = React.useMemo(
    () => ({
      series: [
        {
          bottom: '5%',
          breadcrumb: {
            emphasis: {
              itemStyle: {
                color: theme.colors.buttonPrimaryBackgroundHover,
              },
            },
            itemStyle: {
              borderWidth: 2,
              color: theme.colors.backgroundInactive,
            },
            left: '1%',
            show: true,
            top: '2%',
          },
          data: data.data.map(item => {
            if (item.children && item.children.length > 0) {
              return {
                ...item,
                children: item.children.map(child => {
                  const logo = logosMap[child.name];
                  const sizeValue = child.value?.[0] ?? 0;
                  const colorValue = child.value?.[1];
                  const formattedColorValue = colorValue !== undefined ? formatLarge(colorValue) : '--';

                  const LARGE_THRESHOLD = 1e12;
                  const MEDIUM_THRESHOLD = 1e11;
                  const TEXT_THRESHOLD = 5e9;
                  const MIN_LOGO_SIZE = 12;

                  let computedLabelConfig;

                  if (logo) {
                    let logoSize = MIN_LOGO_SIZE;
                    let nameSize = 10;
                    let valueSize = 9;
                    const showText = sizeValue >= TEXT_THRESHOLD;

                    if (sizeValue >= LARGE_THRESHOLD) {
                      logoSize = 35;
                      nameSize = 14;
                      valueSize = 12;
                    } else if (sizeValue >= MEDIUM_THRESHOLD) {
                      logoSize = 25;
                      nameSize = 12;
                      valueSize = 10;
                    } else if (showText) {
                      logoSize = 18;
                    }

                    computedLabelConfig = {
                      align: 'center',
                      formatter: showText ? `{logo|}\n{name|${child.name}}\n{value|${formattedColorValue}}` : '{logo|}',
                      rich: {
                        logo: {
                          align: 'center',
                          backgroundColor: { image: logo },
                          height: logoSize,
                          width: logoSize,
                        },
                        ...(showText && {
                          name: {
                            align: 'center',
                            color: theme.colors.foreground,
                            ellipsis: '...',
                            fontSize: nameSize,
                            fontWeight: 'bold',
                            overflow: 'truncate',
                            padding: [2, 0, 0, 0],
                          },
                          value: {
                            align: 'center',
                            color: theme.colors.foreground,
                            ellipsis: '...',
                            fontSize: valueSize,
                            fontStyle: 'italic',
                            overflow: 'truncate',
                            padding: [0, 0, 2, 0],
                          },
                        }),
                      },
                      show: true,
                      verticalAlign: 'middle',
                    };
                  } else {
                    if (sizeValue >= TEXT_THRESHOLD) {
                      let nameSize = 11;
                      let valueSize = 10;
                      if (sizeValue >= LARGE_THRESHOLD) {
                        nameSize = 15;
                        valueSize = 13;
                      } else if (sizeValue >= MEDIUM_THRESHOLD) {
                        nameSize = 13;
                        valueSize = 11;
                      }

                      computedLabelConfig = {
                        align: 'center',
                        formatter: `{name|${child.name}}\n{value|${formattedColorValue}}`,
                        rich: {
                          name: {
                            align: 'center',
                            color: theme.colors.foreground,
                            ellipsis: '...',
                            fontSize: nameSize,
                            fontWeight: 'bold',
                            overflow: 'truncate',
                            padding: [2, 0, 0, 0],
                          },
                          value: {
                            align: 'center',
                            color: theme.colors.foreground,
                            ellipsis: '...',
                            fontSize: valueSize,
                            fontStyle: 'italic',
                            overflow: 'truncate',
                            padding: [0, 0, 2, 0],
                          },
                        },
                        show: true,
                        verticalAlign: 'middle',
                      };
                    } else {
                      computedLabelConfig = { show: false };
                    }
                  }

                  return {
                    ...child,
                    label: computedLabelConfig,
                  };
                }),
              };
            }
            return item;
          }),
          emphasis: {
            focus: 'series',
          },
          height: '88%',
          levels,
          name: 'Heatmap',
          roam: true,
          type: 'treemap',
          upperLabel: {
            color: theme.colors.foreground,
            formatter: params => {
              return `${params.name} - ${formatNumberWithSuffix(params.value[0], args.traces[0].display)}`;
            },
            show: true,
          },
          width: '98%',
        },
      ],
      textStyle: {
        ellipsis: '...',
        fontFamily: 'Nimbus Sans, helvetica, arial, sans-serif',
        overflow: 'truncate',
      },
      toolbox: {
        feature: {
          myInfoBtn: {
            icon: `image://${iconToDataURL(FaInfoCircle, theme.colors.foregroundActive)}`,
            iconStyle: {
              borderColor: theme.colors.foregroundActive,
              borderWidth: 2,
            },
            onclick: () => undefined,
            title: '',
          },
          restore: {
            iconStyle: {
              borderColor: theme.colors.foregroundActive,
              borderWidth: 2,
            },
            onclick: function () {
              if (chartRef.current) {
                const chartInstance = chartRef.current.getEchartsInstance();
                chartInstance.dispatchAction({
                  seriesIndex: 0,
                  type: 'treemapRootToNode',
                });
              }
            },
            show: true,
            title: '',
          },
          saveAsImage: {
            iconStyle: {
              borderColor: theme.colors.foregroundActive,
              borderWidth: 2,
            },
            show: true,
            title: '',
            type: 'png',
          },
        },
        itemGap: 20,
        itemSize: 13,
        right: 10,
        show: true,
        tooltip: {
          backgroundColor: theme.colors.backgroundActive,
          extraCssText: `
    max-width: 150px;
    display: flex;
    flex-direction: column;
    white-space: normal;
    word-wrap: break-word;
    padding: 5px;
    box-sizing: border-box;
  `,
          formatter: params => '<div>' + toolboxHoverText(params) + '</div>',
          show: true,
          textStyle: {
            color: theme.colors.brandForeground,
            fontSize: 12,
          },
        },
        top: '1%',
      },
      tooltip: {
        border: 0,
        formatter: (params: any) => {
          const id = `heatMap_widget_${params.name}`;
          setDebouncedTooltip(prevState => ({
            ...prevState,
            children: params.data.children,
            id,
            name: params.name ?? '',
          }));
          return `<div class="${id}"></div>`;
        },
        padding: 0,
        position: tooltipCoordinates,
      },

      visualMap: {
        bottom: 'bottom',
        dimension: 1,
        formatter: visualFormatter,
        inRange: {
          color: ['#f63538', '#414554', '#30cc5a'],
        },
        itemHeight: '300%',
        itemWidth: 13,
        max: isNaN(data.max) ? 100 : data.max,
        min: isNaN(data.min) ? 0 : data.min,
        orient: 'horizontal',
        right: 'center',
        text: ['High', 'Low'],
        textStyle: {
          color: theme.colors.foreground,
          fontStyle: 'italic',
        },
        type: 'continuous',
      },
    }),
    [
      args.traces,
      chartRef,
      data.data,
      data.max,
      data.min,
      iconToDataURL,
      levels,
      logosMap,
      setDebouncedTooltip,
      theme.colors.backgroundActive,
      theme.colors.backgroundInactive,
      theme.colors.brandForeground,
      theme.colors.buttonPrimaryBackgroundHover,
      theme.colors.foreground,
      theme.colors.foregroundActive,
      toolboxHoverText,
      tooltipCoordinates,
      visualFormatter,
    ],
  );

  return React.useMemo(() => ({ limitGroupChildren, options, tooltip }), [limitGroupChildren, options, tooltip]);
};

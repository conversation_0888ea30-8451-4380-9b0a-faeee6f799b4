import React from 'react';
import {
  BzSankeyData,
  BzWaterfallData,
  DataTypes,
  ExtendedBalanceSheet,
  ExtendedCashFlow,
  ExtendedIncomeStatement,
} from '../entity';
import { useMultiSymbolFinancials } from '@benzinga/securities-manager-hooks';
import { Financials, FinancialsPeriod } from '@benzinga/securities-manager';
import { StockSymbol } from '@benzinga/session';
import { BaseTrace } from '@benzinga/visualization-utils';

export const useFinancials = (symbols: string[] | undefined, traces: (DataTypes & BaseTrace)[]) => {
  const updatedFinancialsConfig = React.useMemo(() => {
    const isSankeyOrWaterfall = traces.some(d => d.type === 'waterfall' || d.type === 'sankey');
    const sankeyOrWaterFallTrace = traces.find(d => d.type === 'waterfall' || d.type === 'sankey') as
      | BzWaterfallData
      | BzSankeyData;

    if (isSankeyOrWaterfall && sankeyOrWaterFallTrace?.period === '12M') {
      return {
        asOf: 'LATEST_REPORTED' as const,
        period: '12M' as const,
      };
    }

    return {
      period:
        isSankeyOrWaterfall && sankeyOrWaterFallTrace?.period
          ? (sankeyOrWaterFallTrace.period as FinancialsPeriod)
          : '3M',
    };
  }, [traces]);
  const financialData = useMultiSymbolFinancials(symbols ?? ['AAPL'], updatedFinancialsConfig);
  const financials = React.useMemo(
    () =>
      Object.entries(financialData)
        .filter((item): item is [StockSymbol, Financials[]] => !!item[1])
        .map(([symbol, f]) => ({
          data: f
            .map(f => f.financials)
            .filter(data => !!data)
            .flat(),
          symbol,
        })),
    [financialData],
  );

  const incomeStatements = React.useMemo(() => {
    const symbolSet = new Set();
    const uniqueStatements: ExtendedIncomeStatement[] = [];

    financials.forEach(financial => {
      financial.data.forEach(f => {
        if (f.incomeStatement && f.incomeStatement.totalRevenue !== undefined && !symbolSet.has(financial.symbol)) {
          symbolSet.add(financial.symbol);
          uniqueStatements.push({ ...f.incomeStatement, symbol: financial.symbol });
        }
      });
    });

    return uniqueStatements;
  }, [financials]);

  const cashFlowStatements = React.useMemo(() => {
    const symbolSet = new Set();
    const uniqueStatements: ExtendedCashFlow[] = [];

    financials.forEach(financial => {
      financial.data.forEach(f => {
        if (
          f.cashFlowStatement &&
          f.cashFlowStatement.operatingCashFlow !== undefined &&
          !symbolSet.has(financial.symbol)
        ) {
          symbolSet.add(financial.symbol);
          uniqueStatements.push({ ...f.cashFlowStatement, symbol: financial.symbol });
        }
      });
    });

    return uniqueStatements;
  }, [financials]);

  const balanceSheetStatements = React.useMemo(() => {
    const symbolSet = new Set();
    const uniqueStatements: ExtendedBalanceSheet[] = [];
    financials.forEach(financial => {
      financial.data.forEach(f => {
        if (f.balanceSheet && f.balanceSheet.totalAssets !== undefined && !symbolSet.has(financial.symbol)) {
          symbolSet.add(financial.symbol);
          uniqueStatements.push({ ...f.balanceSheet, symbol: financial.symbol });
        }
      });
    });
    return uniqueStatements;
  }, [financials]);

  return { balanceSheetStatements, cashFlowStatements, incomeStatements };
};

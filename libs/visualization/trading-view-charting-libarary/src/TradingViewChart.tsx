'use client';
import React from 'react';
import { Root, createRoot } from 'react-dom/client';
import TradingViewAPI from './TradingViewAPI';
import { AuthenticationManager, PersistStorageKey, UUID } from '@benzinga/session';
import { SessionContext, SessionContextProvider } from '@benzinga/session-context';
import { CUSTOM_TIMEZONES, DEFAULT_CHART_SYMBOL, DELAY_THRESHOLD, SYNC_INTERVAL, chartColor } from './chartUtils';
import styled, { ThemeContext, ThemeProvider } from '@benzinga/themetron';
import { ProContext, WorkspaceContext } from '@benzinga/pro-tools';
import { QuotesManager } from '@benzinga/quotes-manager';
import { arrayDeepEqual, noop } from '@benzinga/utils';
import Hooks from '@benzinga/hooks';
import { WidgetContext } from '@benzinga/widget-tools';
import { useTime } from '@benzinga/time-manager-hooks';
import * as TradingView from 'charting-library';
import { TimeZoneName } from '@vvo/tzdb';
import { LoggingManager } from '@benzinga/session';
import { ChartInfo } from './chartEntity';
import {
  ChartConfigManager,
  ChartContent,
  ChartExtendedContent,
  TradingViewConfig,
} from '@benzinga/chart-config-manager';
import { useChartInitialize } from './useChartInitialize';
import { Modal, Spinner } from '@benzinga/core-ui';
import { CHART_VERSION, Layouts } from './layout/layouts';
import { Rename } from './layout/rename';
import { StyledComponents } from '@benzinga/themetron';
import { SubscribableHolder } from '@benzinga/subscribable';
import { SavedTooltip } from './layout/saveTooltip';
import { DrawingTooltip } from './layout/drawingTooltip';
import { DrawingLayouts } from './layout/drawingLayouts';
import { safeDelay } from '@benzinga/safe-await';
import { LayoutSave } from './layout/layoutSave';

export const DEFAULT_TIMEZONE: TimeZoneName = 'America/New_York';

type CurrentModal =
  | 'select layout'
  | 'rename layout'
  | 'save layout'
  | 'copy layout'
  | 'copy drawing'
  | 'merge drawing'
  | 'new drawing'
  | 'new layout'
  | 'rename drawing'
  | 'select drawing'
  | 'on save'
  | '';

interface Props {
  symbol: string;
  symbolChanged?: (symbol: string) => void;

  chartInfo: ChartInfo;
  setChartInfo: (chartInfo: Partial<ChartInfo>) => void;

  defaultDrawingCanvasId?: string;
  defaultChartId?: string;
  settings?: TradingView.ISettingsAdapter | undefined;
}

const TradingViewChartInternal: React.FC<
  Props & {
    onLastBarUpdate: (time: number) => void;
    isWidgetPositionChanging: boolean;
  }
> = React.memo(props => {
  const time = useTime();
  const theme = React.useContext(ThemeContext);
  const widget = React.useContext(WidgetContext);
  const session = React.useContext(SessionContext);
  const [chartButton, setChartButton] = React.useState<
    { save: HTMLElement; drawing: HTMLElement } | { save: null; drawing: null }
  >({
    drawing: null,
    save: null,
  });
  const chart = React.useRef<TradingView.IChartingLibraryWidget | undefined>(undefined);
  const [chartSocket, setChartSocket] = React.useState<TradingViewAPI | null>(null);
  const initialized = React.useRef<boolean>(false);
  const settingsAdapter = props.settings;
  const settingsAdapterRef = React.useRef(settingsAdapter);
  settingsAdapterRef.current = settingsAdapter;

  const chartInfo = React.useRef(props.chartInfo);
  chartInfo.current = props.chartInfo;

  const [currentModal, setCurrentModal] = React.useState<CurrentModal>('');

  const onCloseModal = React.useCallback(() => {
    setCurrentModal('');
  }, []);

  const symbolRef = React.useRef(props.symbol);
  symbolRef.current = props.symbol;

  const [iframeElement, updateIframeElement] = React.useState<HTMLDivElement | null>();
  const setIframeElement = React.useCallback((frame: HTMLDivElement | null) => {
    setChartButton({
      drawing: null,
      save: null,
    });
    if (frame === null) {
      initialized.current = false;
    }
    updateIframeElement(frame);
  }, []);

  const setLastBarUpdate = React.useCallback(() => {
    const onLastBarUpdate = props.onLastBarUpdate;
    onLastBarUpdate(Date.now());
  }, [props.onLastBarUpdate]);

  const createDataFeed = React.useCallback(() => {
    const config = {
      addr:
        typeof window['env']?.CHART_SOCK === 'string'
          ? window['env']?.CHART_SOCK
          : 'wss://pro-charts.benzinga.com/quotes/socket',
      auth: {
        iamKey: session.getManager(AuthenticationManager).getBenzingaToken(),
      },
    };
    setChartSocket(TradingViewAPI.getInstance(config, setLastBarUpdate, session.getSession(), widget.widgetId ?? ''));
  }, [session, setLastBarUpdate, widget.widgetId]);

  const updateChartInfo = React.useCallback(
    (content: TradingViewConfig) => {
      const setChartInfo = props.setChartInfo;
      setChartInfo({
        ...chartInfo.current,
        content: chartInfo.current.content
          ? {
              ...content,
              favorite: chartInfo.current.content?.favorite,
              guid: chartInfo.current.content?.guid,
              hidden: chartInfo.current.content?.hidden,
              name: chartInfo.current.content?.name,
              resolution: chartInfo.current.content?.resolution,
              sideBarHidden: chartInfo.current.content?.sideBarHidden,
              timestamp: chartInfo.current.content?.timestamp,
            }
          : content,
      });
    },
    [props.setChartInfo],
  );

  const onAutoSaveNeeded = React.useCallback(() => {
    chart.current?.save(chState => {
      updateChartInfo(chState as TradingViewConfig);
    });
  }, [updateChartInfo]);

  const getLineTools = React.useCallback(
    (a: TradingViewConfig, studyId: string) =>
      a.charts
        .flatMap(
          c =>
            c.panes
              .find(p =>
                studyId === 'MainSeries'
                  ? p.sources.some(s => s.type === studyId)
                  : p.sources.some(s => s.metaInfo?.id === studyId),
              )
              ?.sources.filter(s => s.type.startsWith('LineTool')) ?? [],
        )
        .map(a => ({ ...a, ownerSource: null })) ?? [],
    [],
  );

  const getStudyIdsFromPanel = React.useCallback(
    (sources: TradingViewConfig['charts'][0]['panes'][0]['sources']) =>
      sources.find(s => s.type === 'MainSeries')?.type ?? sources.find(s => s.type === 'Study')?.metaInfo?.id,
    [],
  );

  const getStudyIdsPerPanel = React.useCallback(
    (content: TradingViewConfig) =>
      content.charts.flatMap<string>(c =>
        c.panes.flatMap<string>(p => {
          const id = getStudyIdsFromPanel(p.sources);
          return id ? [id] : [];
        }),
      ),
    [getStudyIdsFromPanel],
  );

  const compareDrawing = React.useCallback(
    (content: TradingViewConfig | null, drawing: TradingViewConfig | null) =>
      content === null || drawing === null
        ? false
        : getStudyIdsPerPanel(content).every(studyId =>
            arrayDeepEqual(getLineTools(drawing, studyId), getLineTools(content, studyId)),
          ),
    [getLineTools, getStudyIdsPerPanel],
  );

  const updatedDrawing = React.useRef(false);
  const debouncedSend = Hooks.useDebounce(
    React.useCallback(
      (uuid: UUID, oldContent: ChartExtendedContent, newContent: TradingViewConfig) => {
        updatedDrawing.current = true;

        session.getManager(ChartConfigManager).updateChartData(uuid, {
          ...oldContent,
          content: newContent,
          drawing: true,
          guid: uuid,
          symbol: '',
        });
      },
      [session],
    ),
    1000,
  );

  const updatingDrawing = React.useRef(false);

  const onDrawingEvent = React.useCallback(async () => {
    const drawing = chartInfo.current.drawings;
    if (drawing && initialized.current && updatingDrawing.current === false) {
      await safeDelay(250);
      chart.current?.save(async chState => {
        const currentValue = session.getManager(ChartConfigManager).getCachedChartData(drawing);
        if (currentValue) {
          const content = chState as TradingViewConfig;
          if (!compareDrawing(content, currentValue.content)) {
            debouncedSend(drawing, currentValue, content);
          }
        } else {
          session.getManager(LoggingManager).log(
            'error',
            {
              category: 'chart',
              message: 'could not find drawing config on server',
            },
            ['console', 'toast'],
          );
        }

        updateChartInfo(chState as TradingViewConfig);
      });
    } else if (initialized.current) {
      onAutoSaveNeeded();
    }
    updatingDrawing.current = false;
  }, [compareDrawing, debouncedSend, onAutoSaveNeeded, session, updateChartInfo]);

  const updateDrawings = React.useCallback(
    (chartDrawing: TradingViewConfig | null, currentChart: TradingViewConfig, keepDrawings?: boolean) => {
      const newChart: TradingViewConfig['charts'] = currentChart?.charts.map(c => {
        const panes = c.panes.map(p => {
          const studyId = getStudyIdsFromPanel(p.sources);
          if (studyId) {
            return {
              ...p,
              sources: [
                ...(keepDrawings ? p.sources : p.sources.filter(s => !s.type.startsWith('LineTool'))),
                ...(chartDrawing
                  ? getLineTools(chartDrawing, studyId).map(a => ({
                      ...a,
                      ownerSource: p.mainSourceId,
                    }))
                  : []),
              ],
            };
          }
          return { ...p };
        });
        return { ...c, panes };
      });
      const currentRange = chart.current?.activeChart().getVisibleRange();
      updatingDrawing.current = true;
      chart.current?.load({ ...currentChart, charts: newChart });
      currentRange && chart.current?.activeChart().setVisibleRange(currentRange);
    },
    [getLineTools, getStudyIdsFromPanel],
  );

  Hooks.useSubscriber(session.getManager(ChartConfigManager), event => {
    switch (event.type) {
      case 'chart-config:post-chart-updated': {
        if (initialized.current && chart.current && props.chartInfo.drawings === event.chart.guid) {
          chart.current?.save(async chState => {
            if (updatedDrawing.current || compareDrawing(chState as TradingViewConfig, event.chart.content)) {
              updatedDrawing.current = false;
            } else {
              updateDrawings(event.chart.content, chState as TradingViewConfig);
            }
          });
        }
        break;
      }
    }
  });

  const iframeElementRef = React.useRef<HTMLDivElement | null | undefined>(null);
  iframeElementRef.current = iframeElement;

  const handleChartReady = React.useCallback(async () => {
    const activeChart = chart.current?.activeChart();
    const setChartInfo = props.setChartInfo;
    if (!activeChart || !chart.current || initialized.current) {
      return;
    }

    if (!chart.current?.symbolInterval().symbol.endsWith(`:${symbolRef.current}`)) {
      activeChart.setSymbol(symbolRef.current);
    }

    activeChart.onSymbolChanged().subscribe(null, () => {
      const symbolChanged = props.symbolChanged;
      if (symbolChanged && chart.current) {
        symbolChanged(chart.current.symbolInterval().symbol);
      }
    });

    chart.current.subscribe('onAutoSaveNeeded', onAutoSaveNeeded);
    chart.current.subscribe('drawing', onDrawingEvent);
    chart.current.subscribe('drawing_event', onDrawingEvent);
    chart.current.subscribe('chart_load_requested', () => console.log('chart_load_requested'));
    chart.current.subscribe('chart_loaded', () => console.log('chart_loaded'));
    chart.current.subscribe('toggle_sidebar', isHidden => {
      setChartInfo({ ...chartInfo.current, sideBarHidden: isHidden });
      onAutoSaveNeeded();
    });

    chart.current.subscribe('chart_load_requested', savedData => {
      const { short_name } = savedData as ChartExtendedContent;
      if (short_name !== symbolRef.current) {
        activeChart.setSymbol(symbolRef.current, noop);
      }
    });

    activeChart.onIntervalChanged().subscribe(null, interval => {
      setChartInfo({ ...chartInfo.current, interval });
      onAutoSaveNeeded();
    });

    activeChart.onVisibleRangeChanged().subscribe(null, range => {
      setChartInfo({ ...chartInfo.current, visibleRange: range });
      if (initialized.current) {
        onAutoSaveNeeded();
      }
    });

    activeChart.onDataLoaded().subscribe(null, noop);

    activeChart.dataReady(noop);
    setTimeout(() => {
      const fastForward = iframeElementRef.current?.getElementsByClassName('control-bar__btn--back-present');
      if (fastForward?.[0]) {
        (fastForward[0] as HTMLDivElement).click();
      }
    }, 200);

    initialized.current = true;
  }, [props.setChartInfo, props.symbolChanged, onAutoSaveNeeded, onDrawingEvent]);

  const loadChart = React.useCallback(
    async (chartInfo: ChartInfo) => {
      if (chartInfo.content) {
        if (chartInfo.drawings) {
          const currentValue = (await session.getManager(ChartConfigManager).getChartData(chartInfo.drawings)).ok;
          if (currentValue) {
            updateDrawings(currentValue.content, chartInfo.content);
          } else {
            chart.current?.load(chartInfo.content);
          }
        } else {
          chart.current?.load(chartInfo.content);
        }
      } else if (chartInfo.drawings) {
        const currentValue = (await session.getManager(ChartConfigManager).getChartData(chartInfo.drawings)).ok;
        if (currentValue?.content) {
          chart.current?.load(currentValue.content);
        }
      }
    },
    [session, updateDrawings],
  );

  const renderChart = React.useCallback(
    async (dataFeed: TradingViewAPI, chartInfo: ChartInfo) => {
      if (!iframeElement || initialized.current === true) {
        return;
      }
      initialized.current = false;
      const disabledFeatures: TradingView.ChartingLibraryFeatureset[] = [
        'header_saveload',
        'header_screenshot',
        'header_symbol_search',
        'symbol_search_hot_key',
      ];

      const enabledFeatures: TradingView.ChartingLibraryFeatureset[] = ['header_compare', 'move_logo_to_main_pane'];
      if (chartInfo.sideBarHidden) {
        enabledFeatures.push('hide_left_toolbar_by_default');
      }

      const overrides = {
        'mainSeriesProperties.areaStyle.color1': chartColor.up,
        'mainSeriesProperties.areaStyle.color2': chartColor.down,
        'mainSeriesProperties.barStyle.downColor': chartColor.down,
        'mainSeriesProperties.barStyle.upColor': chartColor.up,
        'mainSeriesProperties.candleStyle.downColor': chartColor.down,
        'mainSeriesProperties.candleStyle.upColor': chartColor.up,
        'mainSeriesProperties.haStyle.downColor': chartColor.down,
        'mainSeriesProperties.haStyle.upColor': chartColor.up,
        'mainSeriesProperties.hollowCandleStyle.downColor': chartColor.down,
        'mainSeriesProperties.hollowCandleStyle.upColor': chartColor.up,
        'mainSeriesProperties.sessionId': 'extended',
        'mainSeriesProperties.style': 1,
        'paneProperties.background': chartColor.bzBlack,
      };

      /**
       * Construct the widget options.
       * https://github.com/tradingview/charting_library/wiki/Widget-Constructor
       */
      const cfg: TradingView.ChartingLibraryWidgetOptions = {
        auto_save_delay: 0.5,
        autosize: true,
        // charts_storage_url: 'https://data-api-pro.benzinga.com/rest/v2/chart',
        container: iframeElement,
        customFormatters: {
          dateFormatter: {
            format: (date: Date) =>
              `${['Sun', 'Mon', 'Tue', 'Wen', 'Thu', 'Fri', 'Sat'][date.getUTCDay()]} ${date.getUTCDate()} ${
                ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][date.getUTCMonth()]
              } ${date.getUTCFullYear()}`,
          } as any,
          timeFormatter: {
            format: (date: Date) => `${date.getUTCHours()}:${date.getUTCMinutes()}`,
          } as any,
        },
        datafeed: dataFeed,
        debug: session.getManager(LoggingManager).getVerbosity() === 'debug',
        disabled_features: disabledFeatures,
        enabled_features: enabledFeatures,
        interval: chartInfo.interval as TradingView.ChartingLibraryWidgetOptions['interval'], // interval.current as TradingView.ChartingLibraryWidgetOptions['interval'],
        library_path: '/charting_library_v24_4/',
        locale: 'en',
        overrides,
        settings_adapter: settingsAdapterRef.current,
        symbol: symbolRef.current || DEFAULT_CHART_SYMBOL,
        theme: theme.name === 'light' ? 'Light' : 'Dark',
        timeframe: chartInfo?.visibleRange,
        timezone: (CUSTOM_TIMEZONES.includes(time.timezone as TradingView.CustomTimezones)
          ? time.timezone
          : DEFAULT_TIMEZONE) as TradingView.CustomTimezones,
        // toolbar_bg: getChartColor(),
      };

      chart.current = new TradingView.widget(cfg);
      chart.current.onChartReady(handleChartReady);

      await chart.current.headerReady();

      const drawing = chart.current.createButton({
        align: 'right',
        useTradingViewStyle: false,
      });
      const save = chart.current.createButton({
        align: 'right',
        useTradingViewStyle: false,
      });

      setChartButton({
        drawing,
        save,
      });
      loadChart(chartInfo);
    },
    [iframeElement, session, theme.name, time.timezone, handleChartReady, loadChart],
  );

  const setDrawing = React.useCallback(
    (data: ChartExtendedContent) => {
      const setChartInfo = props.setChartInfo;
      chartInfo.current.drawings = data.guid;
      setChartInfo({ ...props.chartInfo, drawings: data.guid });
      updateDrawings(data.content, props.chartInfo.content as TradingViewConfig);
      setCurrentModal('');
    },
    [props.chartInfo, props.setChartInfo, updateDrawings],
  );

  const setChartInfo = React.useCallback(
    (chartInfo: Partial<ChartInfo>) => {
      chartInfo?.content && chart.current?.load(chartInfo?.content);
      const setChartInfo = props.setChartInfo;
      setChartInfo(chartInfo);
      setCurrentModal('');
    },
    [props.setChartInfo],
  );

  const chartConfigManager = session.getManager(ChartConfigManager);
  const onShowRenameSaveClick = React.useCallback(
    (name: string | undefined) => {
      const run = async () => {
        switch (currentModal) {
          case 'rename layout':
          case 'rename drawing':
            {
              const rename = async (guid: UUID) => {
                if (name) {
                  const data = await chartConfigManager.getChartData(guid ?? null);
                  if (data.err) {
                    (data.err.data as Response).json().then(error =>
                      session.getManager(LoggingManager).log(
                        'error',
                        {
                          category: 'chart',
                          data: data.err,
                          message: error.detail,
                        },
                        ['console', 'toast'],
                      ),
                    );
                  } else if (data.ok) {
                    await chartConfigManager.updateChartData(guid, {
                      ...data.ok,
                      drawing: currentModal === 'rename drawing',
                      name,
                      timestamp: Date.now(),
                      version: CHART_VERSION,
                    });
                  } else {
                    session.getManager(LoggingManager).log(
                      'error',
                      {
                        category: 'chart',
                        data: data.err,
                        message: 'could not rename chart',
                      },
                      ['console', 'toast'],
                    );
                  }
                }
              };
              const guid = currentModal === 'rename drawing' ? props.chartInfo.drawings : props.chartInfo.guid;
              if (guid) await rename(guid);
            }
            break;
          case 'copy layout':
          case 'copy drawing':
            if (name) {
              const { ...chart } = props.chartInfo;
              const config = {
                ...chart,
                content: chart.content as ChartContent,
                drawing: currentModal === 'copy drawing',
                name,
                timestamp: Date.now() / 1000,
                version: CHART_VERSION,
              };
              const data = await chartConfigManager.newChartData(config);
              if (data.err) {
                (data.err.data as Response).json().then(data =>
                  session.getManager(LoggingManager).log(
                    'error',
                    {
                      category: 'chart',
                      data: data.err,
                      message: data.detail,
                    },
                    ['console', 'toast'],
                  ),
                );
              } else if (data.ok?.guid) {
                if (currentModal === 'copy drawing') {
                  setDrawing({
                    ...config,
                    guid: data.ok.guid,
                  });
                } else {
                  setChartInfo({
                    ...config,
                    content: null,
                    guid: data.ok.guid,
                  });
                }
              }
            }
            break;
          case 'new drawing':
          case 'new layout':
            if (name) {
              const { drawings, ...chart } = props.chartInfo;
              const config = {
                ...chart,
                content: null,
                drawing: currentModal === 'new drawing',
                name,
                timestamp: Date.now() / 1000,
                version: CHART_VERSION,
              };
              const data = await chartConfigManager.newChartData(config);

              if (data.err) {
                (data.err.data as Response).json().then(data =>
                  session.getManager(LoggingManager).log(
                    'error',
                    {
                      category: 'chart',
                      data: data.err,
                      message: data.detail,
                    },
                    ['console', 'toast'],
                  ),
                );
              } else if (data.ok?.guid) {
                if (currentModal === 'new drawing') {
                  setDrawing({
                    ...config,
                    guid: data.ok.guid,
                  });
                } else {
                  setChartInfo({
                    ...config,
                    drawings,
                    guid: data.ok.guid,
                  });
                }
              }
            }
            break;
          default:
            break;
        }
        setCurrentModal('');
      };
      run();
    },
    [chartConfigManager, currentModal, props.chartInfo, session, setChartInfo, setDrawing],
  );

  const chartInfoHolder = React.useRef(new SubscribableHolder(props.chartInfo));
  chartInfoHolder.current.update(props.chartInfo);
  // const chartInfoHolder = React.useRef(new SubscribableHolder(props.chartInfo));

  const chartLayoutReactRoot = React.useRef<{ save: Root; drawing: Root } | { save: null; drawing: null }>({
    drawing: null,
    save: null,
  });
  React.useEffect(() => {
    if (chartButton.save) {
      chartLayoutReactRoot.current = {
        drawing: createRoot(chartButton.drawing as HTMLElement),
        save: createRoot(chartButton.save as HTMLElement),
      };
    }
    return () => {
      chartLayoutReactRoot.current.save?.unmount();
      chartLayoutReactRoot.current.drawing?.unmount();
    };
  }, [chartButton]);

  const onSaveClick = React.useCallback(async () => {
    const chart = chartInfoHolder.current.getValue();
    if (chart.name && chart.content) {
      const result = await chartConfigManager.updateChartData(chart.guid, {
        ...chart,
        content: chart.content as ChartContent,
        version: CHART_VERSION,
      });
      if (result.err) {
        (result.err.data as Response).json().then(error =>
          session.getManager(LoggingManager).log(
            'error',
            {
              category: 'chart',
              data: result.err,
              message: error.detail,
            },
            ['console'],
          ),
        );
      }
    } else {
      setCurrentModal('save layout');
    }
  }, [chartConfigManager, session]);

  React.useEffect(() => {
    if (chartButton.save && iframeElementRef.current) {
      const onSaveModalClick = () => {
        setCurrentModal('select layout');
      };

      const onSaveClick = () => {
        setCurrentModal('on save');
      };

      const onRename = () => {
        setCurrentModal('rename layout');
      };

      const onMakeNew = () => {
        setCurrentModal('new layout');
      };

      const onMakeCopy = () => {
        setCurrentModal('copy layout');
      };

      const onReload = async () => {
        const setChartInfo = props.setChartInfo;
        setChartInfo({
          ...chartInfoHolder.current.getValue(),
          content: null,
        });
      };

      chartLayoutReactRoot.current.save?.render(
        <ThemeProvider theme={theme}>
          <SessionContextProvider session={session.getSession()}>
            <StyledComponents.StyleSheetManager
              target={iframeElementRef.current.getElementsByTagName('iframe')[0]?.contentDocument?.head}
            >
              <SavedTooltip
                chartHolder={chartInfoHolder.current}
                onLoadLayouts={onSaveModalClick}
                onMakeCopy={onMakeCopy}
                onMakeNew={onMakeNew}
                onReload={onReload}
                onRename={onRename}
                onSaveClick={onSaveClick}
              />
            </StyledComponents.StyleSheetManager>
          </SessionContextProvider>
        </ThemeProvider>,
      );
    }
  }, [chartButton.save, chartConfigManager, props.setChartInfo, session, theme]);

  React.useEffect(() => {
    if (chartButton.drawing && iframeElementRef.current) {
      const onLoadLayouts = () => {
        setCurrentModal('select drawing');
      };

      const onRename = () => {
        setCurrentModal('rename drawing');
      };

      const onMakeNew = () => {
        setCurrentModal('new drawing');
      };

      const onMakeCopy = () => {
        setCurrentModal('copy drawing');
      };

      const onMergeDrawings = () => {
        setCurrentModal('merge drawing');
      };

      const onSelectLocal = () => {
        const setChartInfo = props.setChartInfo;
        setChartInfo({ ...chartInfoHolder.current.getValue(), drawings: null });
      };

      chartLayoutReactRoot.current.drawing?.render(
        <ThemeProvider theme={theme}>
          <SessionContextProvider session={session.getSession()}>
            <StyledComponents.StyleSheetManager
              target={iframeElementRef.current.getElementsByTagName('iframe')[0]?.contentDocument?.head}
            >
              <DrawingTooltip
                chartHolder={chartInfoHolder.current}
                onLoadLayouts={onLoadLayouts}
                onMakeCopy={onMakeCopy}
                onMakeNew={onMakeNew}
                onMergeDrawings={onMergeDrawings}
                onRename={onRename}
                onSelectLocal={onSelectLocal}
              />
            </StyledComponents.StyleSheetManager>
          </SessionContextProvider>
        </ThemeProvider>,
      );
    }
  }, [chartButton.drawing, chartConfigManager, props.setChartInfo, session, theme]);

  React.useEffect(() => {
    createDataFeed();
  }, [createDataFeed]);

  React.useEffect(() => {
    if (iframeElement && chartSocket) {
      renderChart(chartSocket, chartInfo.current);
    }

    return () => {
      chart.current?.remove();
      chart.current = undefined;
      initialized.current = false;
    };
  }, [chartSocket, iframeElement, renderChart]);

  const prevSymbol = Hooks.usePrevious(props.symbol);
  React.useEffect(() => {
    if (!chart.current) {
      return;
    } else if (!initialized.current) {
      chart.current.onChartReady(handleChartReady);
    } else if (prevSymbol !== props.symbol) {
      chart.current.activeChart().setSymbol(props.symbol);
    }
  }, [handleChartReady, prevSymbol, props.symbol]);

  React.useEffect(() => {
    if (props.isWidgetPositionChanging && iframeElementRef.current) {
      const iframe = iframeElementRef.current.getElementsByTagName('iframe')[0];
      iframe.style.pointerEvents = `none`;
      return () => {
        iframe.style.pointerEvents = 'auto';
      };
    }
    return noop;
  });

  const mergeDrawing = React.useCallback(
    (data: ChartExtendedContent) => {
      if (initialized.current && chart.current) {
        chart.current?.save(async chState => {
          updateDrawings(data.content, chState as TradingViewConfig);
        });
      }
      setCurrentModal('');
    },
    [updateDrawings],
  );

  const onLayoutSaveClick = React.useCallback(
    (type: 'overwrite' | 'new') => {
      if (type === 'overwrite') {
        onSaveClick();
        setCurrentModal('');
      } else {
        setCurrentModal('copy layout');
      }
    },
    [onSaveClick],
  );

  return (
    <>
      <Modal
        hideTitle={true}
        onClose={onCloseModal}
        size={{
          manual: {
            height: '100%',
            maxHeight: '500px',
            maxWidth: '400px',
            width: '100%',
          },
        }}
        visible={currentModal === 'select layout'}
      >
        <Layouts onClose={onCloseModal} onConfigSelected={setChartInfo} />
      </Modal>

      <Modal
        hideTitle={true}
        onClose={onCloseModal}
        size={{
          manual: {
            height: '100%',
            maxHeight: '500px',
            maxWidth: '400px',
            width: '100%',
          },
        }}
        visible={currentModal === 'select drawing'}
      >
        <DrawingLayouts onClose={onCloseModal} onConfigSelected={setDrawing} title="Select Drawing" />
      </Modal>

      <Modal
        hideTitle={true}
        onClose={onCloseModal}
        size={{
          manual: {
            height: '100%',
            maxHeight: '215px',
            maxWidth: '400px',
            width: '100%',
          },
        }}
        visible={[
          'rename layout',
          'rename drawing',
          'copy layout',
          'copy drawing',
          'new layout',
          'new drawing',
        ].includes(currentModal)}
      >
        <Rename
          onClose={onCloseModal}
          onRename={onShowRenameSaveClick}
          type={
            currentModal as
              | 'rename layout'
              | 'copy layout'
              | 'copy drawing'
              | 'rename drawing'
              | 'new layout'
              | 'new drawing'
          }
        />
      </Modal>

      <Modal
        hideTitle={true}
        onClose={onCloseModal}
        size={React.useMemo(
          () => ({
            manual: {
              height: '100%',
              maxHeight: '500px',
              maxWidth: '400px',
              width: '100%',
            },
          }),
          [],
        )}
        visible={currentModal === 'merge drawing'}
      >
        <DrawingLayouts onClose={onCloseModal} onConfigSelected={mergeDrawing} title="Merge Drawing To" />
      </Modal>

      <Modal
        hideTitle={true}
        onClose={onCloseModal}
        size={React.useMemo(
          () => ({
            manual: {
              height: '100%',
              maxHeight: '150px',
              maxWidth: '400px',
              width: '100%',
            },
          }),
          [],
        )}
        visible={'on save' === currentModal}
      >
        <LayoutSave onClose={onCloseModal} onOk={onLayoutSaveClick} />
      </Modal>

      <IframeHolder className={widget.widgetId} key={widget.widgetId} ref={setIframeElement} />
    </>
  );
});

export const TradingViewChart: React.FC<Props> = props => {
  const { chartInfo, isLoading } = useChartInitialize(props.chartInfo);
  const session = React.useContext(SessionContext);

  const [display, setDisplay] = React.useState(true);
  const workspaceContext = React.useContext(WorkspaceContext);

  React.useEffect(() => {
    if (workspaceContext.configWidgetsLocationsChanged) {
      setDisplay(false);
      setTimeout(() => setDisplay(true), 100);
    }
  }, [workspaceContext.configWidgetsLocationsChanged]);

  const syncInterval = React.useRef<NodeJS.Timeout | undefined>(undefined);
  const lastBarUpdate = React.useRef<number | null>(null);
  const setSyncInterval = React.useCallback(
    (symbol: string) => {
      if (syncInterval.current) {
        clearInterval(syncInterval.current);
      }
      lastBarUpdate.current = null;
      syncInterval.current = setInterval(() => {
        const hasActiveFeed = session.getManager(QuotesManager).getStore().hasQuoteFeed(symbol);
        if (hasActiveFeed) {
          const quote = session.getManager(QuotesManager).getCachedQuote(symbol);
          if (
            lastBarUpdate.current &&
            quote?.lastTradeTime &&
            Date.parse(quote?.lastTradeTime) - lastBarUpdate.current > DELAY_THRESHOLD
          ) {
            setDisplay(false);
            setTimeout(() => setDisplay(true), 100);
          }
        }
      }, SYNC_INTERVAL);
    },
    [session],
  );

  const onLastBarUpdate = React.useCallback((time: number | null) => {
    lastBarUpdate.current = time;
  }, []);

  React.useEffect(() => {
    return () => syncInterval.current && clearInterval(syncInterval.current);
  }, []);

  const prevSymbol = Hooks.usePrevious(props.symbol);
  React.useEffect(() => {
    if (prevSymbol !== props.symbol) {
      setSyncInterval(props.symbol);
    }
  }, [prevSymbol, setSyncInterval, props.symbol]);

  return isLoading || !display ? (
    <SpinnerContent>
      <Spinner />
    </SpinnerContent>
  ) : (
    <TradingViewChartInternal
      {...props}
      chartInfo={chartInfo}
      isWidgetPositionChanging={workspaceContext.isWidgetPositionChanging}
      onLastBarUpdate={onLastBarUpdate}
    />
  );
};

const IframeHolder = styled.div`
  flex: 1;
  flex-direction: column;
`;

const SpinnerContent = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  flex: 1;
`;

'use client';

import React, { ReactNode } from 'react';
import { Button } from '../Button';
import { Spinner } from '../Spinner';

export interface FeedButtonProps {
  children?: ReactNode;
  loadMore: () => void;
}

export const FeedButton = ({ children, loadMore }: FeedButtonProps) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const isVisible = useOnScreen(ref);

  React.useEffect(() => {
    if (isVisible) {
      loadMore();
    }
  }, [loadMore, isVisible]);

  return (
    <div className="pb-4 text-center" ref={ref}>
      {children ? (
        <Button className="px-8" onClick={loadMore}>
          {children}
        </Button>
      ) : (
        <Spinner />
      )}
    </div>
  );
};

export default function useOnScreen(ref: React.RefObject<HTMLDivElement | null>): boolean {
  const [isIntersecting, setIntersecting] = React.useState(false);

  React.useEffect(() => {
    const observer = new IntersectionObserver(([entry]: IntersectionObserverEntry[]) =>
      setIntersecting(entry.isIntersecting),
    );
    if (ref.current) {
      observer.observe(ref.current);
    }
    // Remove the observer as soon as the component is unmounted
    return () => {
      observer.disconnect();
    };
  }, [ref]);

  return isIntersecting;
}

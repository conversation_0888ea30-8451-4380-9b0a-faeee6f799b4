'use client';
import React, { useEffect, useState } from 'react';
import styled from '@benzinga/themetron';

const InsiderTooltip = (): JSX.Element => {
  const [tooltipArr, setTooltipArr] = useState([]);
  const [renderTooltip, setRenderTooltip] = useState(false);
  const [positionX, setPositionX] = useState(0);
  const [positionY, setPositionY] = useState(0);
  const [height, setHeight] = useState(36);

  useEffect(() => {
    const showTooltip = (ev: any) => {
      const { cellRect, tooltipData, tooltipHeight, type } = ev.data;
      if (type === 'show') {
        setTooltipArr(tooltipData);
        setHeight(tooltipHeight);
        setPositionX(cellRect.x);
        setPositionY(cellRect.y);
        setRenderTooltip(true);
      } else {
        setTooltipArr([]);
        setRenderTooltip(false);
      }
    };

    window.addEventListener('renderTooltip', showTooltip);
    return () => {
      window.removeEventListener('renderTooltip', showTooltip);
    };
  }, []);

  return renderTooltip ? (
    <TooltipRendererDiv tooltipHeight={height} tooltipLeftPosition={positionX} tooltipTopPosition={positionY}>
      <TooltipContainer>
        {tooltipArr.map(line => {
          return <span key={line}>{line}</span>;
        })}
      </TooltipContainer>
      <TooltipArrow />
    </TooltipRendererDiv>
  ) : (
    <div></div>
  );
};

export default InsiderTooltip;

export const TooltipRendererDiv = styled.div<{
  tooltipHeight: number;
  tooltipTopPosition: number;
  tooltipLeftPosition: number;
}>`
  top: ${props => props.tooltipTopPosition - props.tooltipHeight}px;
  left: ${props => props.tooltipLeftPosition - 50}px;
  position: fixed;
  z-index: 999999999;
  width: 210px;
  height: ${props => props.tooltipHeight}px;
  background-color: #2e79f6;
  padding: 8px;
  border: 1px solid #2e79f6;
  color: white;
`;

export const TooltipArrow = styled.div`
  content: '';
  position: absolute;
  bottom: -5px;
  left: 35%;
  border-top: 5px solid #2e79f6;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  border-bottom: none;
`;

export const TooltipContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-weight: normal;
  font-size: small;
  white-space: nowrap;
`;

export default class InsiderCustomTooltip {
  eGui;
  init(params) {
    const eGui = (this.eGui = document.createElement('div'));
    const color = params.color || '#2e79f6';
    eGui.style['background-color'] = color;
    const html = `
      <div class="flex flex-col text-white text-sm p-2 max-w-sm">
        ${params.value}
      </div>
    `;
    eGui.innerHTML = html;
  }

  getGui() {
    return this.eGui;
  }
}

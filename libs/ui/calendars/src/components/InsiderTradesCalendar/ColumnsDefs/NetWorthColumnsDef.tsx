import type { ColDef, ColGroupDef } from '@ag-grid-community/core';
import dayjs from 'dayjs';
import numeral from 'numeral';
import relativeTime from 'dayjs/plugin/relativeTime';

import { headerInfoTooltipDef, formatLongQuantity, getValueBgClass } from '../../lib/utils';
import { getBuySellLabel, getDerivativeTooltips, filingTradeTypes } from './ColumnsDefV2';

dayjs.extend(relativeTime);

const formTypeTooltip = formType => {
  switch (formType) {
    case '3':
      return "Form 3's are filed when an individual becomes an insider of a publicly-traded company. This includes when a person becomes an officer, director, or beneficial owner of more than 10% of any class of the company's equity securities. The Form 3 must be filed within 10 days of the event that caused the individual to become an insider.";
    case '4':
      return "Form 4's are required by the SEC for officers, directors, and major shareholders of publicly traded companies to report their transactions in the company's securities to prevent insider trading. These filings are typically due within two business days of the transaction.";
    case '5':
      return 'Form 5 - Annual Statement of Changes in Beneficial Ownership';
    default:
      return ``;
  }
};

const defaultFilterParams = {
  newRowsAction: 'keep',
};

export default [
  {
    cellClass: 'insider-cell',
    field: 'form_type',
    headerName: 'Form Type',
    initialWidth: 100,
    tooltipValueGetter: params => {
      const formType = params.data.form_type;
      if (formType) {
        return formTypeTooltip(formType);
      }
      return '';
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => {
      return 'Form ' + value;
    },
  },
  {
    cellClass: ({ value }) => getValueBgClass(value),
    field: 'traded_percentage',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Δ Own',
    initialWidth: 60,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivativePercent = data.derivative?.transactions?.traded_percentage;
      const nonDerivativePercent = data.non_derivative?.transactions?.traded_percentage;
      let tooltipLines = '';
      if (derivativePercent && derivativePercent !== 0) {
        tooltipLines += `<div class="flex flex-col"><b>Derivative</b> <div>Percentage: ${numeral(derivativePercent / 100).format('0,0.00%')}</div></div>`;
      }
      if (nonDerivativePercent && nonDerivativePercent !== 0) {
        tooltipLines += `<div class="flex flex-col"><b>Non Derivative</b> <div>Percentage: ${numeral(nonDerivativePercent / 100).format('0,0.00%')}</div></div>`;
      }
      return tooltipLines;
    },
    tooltipWidth: 180,
    valueFormatter: ({ value }) => numeral(value / 100).format('0,0.00%'),
    ...headerInfoTooltipDef('The percentage change in the shares held'),
  },
  {
    field: 'traded_shares',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Traded Shares',
    initialWidth: 80,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivative = data.derivative?.transactions;
      const non_derivative = data.non_derivative?.transactions;
      let tooltipLines = '';
      if (derivative) {
        const tooltips = getDerivativeTooltips({ key: 'shares', transactions: derivative });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Derivative Shares:</b> ${tooltips}</div>`;
        }
      }
      if (non_derivative) {
        const tooltips = getDerivativeTooltips({ key: 'shares', transactions: non_derivative });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Non Derivative Shares:</b> ${tooltips}</div>`;
        }
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => formatLongQuantity(value),
    ...headerInfoTooltipDef('The total shares acquired/disposed'),
  },
  {
    field: 'traded_share_price',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Price',
    initialWidth: 60,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivative = data.derivative?.transactions;
      const non_derivative = data.non_derivative?.transactions;
      let tooltipLines = '';
      if (derivative) {
        const tooltips = getDerivativeTooltips({ key: 'average_share_price', transactions: derivative, type: 'price' });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Derivative Avg Prices:</b> ${tooltips}</div>`;
        }
      }
      if (non_derivative) {
        const tooltips = getDerivativeTooltips({
          key: 'average_share_price',
          transactions: non_derivative,
          type: 'price',
        });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Non Derivative Avg Prices:</b> ${tooltips}</div>`;
        }
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => numeral(value).format('$0,0.00'),
    ...headerInfoTooltipDef('The average price of acquired/disposed shares'),
  },
  {
    field: 'traded_value',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Size ($)',
    initialWidth: 80,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivative = data.derivative?.transactions;
      const non_derivative = data.non_derivative?.transactions;
      let tooltipLines = '';
      if (derivative) {
        const tooltips = getDerivativeTooltips({ key: 'value', transactions: derivative, type: 'price' });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Derivative Value:</b> ${tooltips}</div>`;
        }
      }
      if (non_derivative) {
        const tooltips = getDerivativeTooltips({ key: 'value', transactions: non_derivative, type: 'price' });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Non Derivative Value:</b> ${tooltips}</div>`;
        }
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => numeral(value).format('$0.00a').toUpperCase(),
    ...headerInfoTooltipDef('The total dollar value of the shares traded'),
  },
  {
    field: 'last_filing_date',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Filing Date',
    initialWidth: 100,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      return dayjs(params.data.last_filing_date).format('h:mm:ss A');
    },
    valueFormatter: ({ value }) => dayjs(value).format('MMM DD, YYYY'),
    width: 120,
    ...headerInfoTooltipDef('The date on which this insider trade was filed'),
  },
  {
    field: 'remaining_shares_in_filing',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Own',
    initialWidth: 100,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      const shares = params.data.non_derivative?.transactions?.remaining_shares || 0;
      const tradePrice = params.data.traded_share_price || 0;
      if (shares !== 0 && tradePrice !== 0) {
        return `
          <span>${numeral(shares * tradePrice)
            .format('$0.00a')
            .toUpperCase()} @ $${tradePrice} per share</span>
        `;
      }
      return '';
    },
    tooltipWidth: 140,
    valueFormatter: ({ value }) => numeral(value).format('0.00a').toUpperCase(),
    ...headerInfoTooltipDef('The remaining shares still owned by the insider'),
  },
  {
    field: 'trade_codes',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Type',
    initialWidth: 120,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      let tooltips = '<div className="flex flex-col">';
      const types = params.data?.trade_codes || [];
      for (const code of types) {
        tooltips += `<div className="flex flex-row" key={code}>
            ${filingTradeTypes[code] ? code + ' - ' + filingTradeTypes[code] : code}
          </div>`;
      }
      return tooltips + '</div>';
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => {
      return getBuySellLabel(value);
    },
    valueGetter: params => {
      const tradeTypes = params.data.trade_codes || [];
      const tradedPercentage = params.data.traded_percentage || 0;
      return { percent: tradedPercentage, types: tradeTypes };
    },
    ...headerInfoTooltipDef('Insider buy or sell, a breakdown of the type of transaction this filing reported'),
  },
  {
    cellRenderer: params => {
      return params.value ? `Scheduled` : null;
    },
    field: 'is_10b5',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Is Scheduled',
    initialWidth: 60,
    ...headerInfoTooltipDef(
      "The transactions were done as part of the insider's 10b5-1 trading plan. This usually indicates that this filing is not a signal of insider sentiment.",
    ),
  },
  {
    field: 'first_trade_date',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Trade Dates',
    initialWidth: 120,
    valueFormatter: ({ data }) => {
      const firstDate = data?.first_trade_date;
      const lastDate = data?.last_trade_date;
      if (firstDate && firstDate === lastDate) {
        return dayjs(firstDate).format('MMM DD');
      } else if (firstDate && lastDate && firstDate !== lastDate) {
        return dayjs(firstDate).format('MMM DD') + ' - ' + dayjs(lastDate).format('MMM DD');
      } else if (firstDate || lastDate) {
        return dayjs(firstDate || lastDate).format('MMM DD');
      } else {
        return '—';
      }
    },
    ...headerInfoTooltipDef('A date range for transactions reported in this filing'),
  },
] as (ColDef | ColGroupDef)[];

import type { ColDef, ColGroupDef } from '@ag-grid-community/core';
import formatDate from 'date-fns/format';
import parseISO from 'date-fns/parseISO';

import {
  dateFilterParams,
  headerInfoTooltipDef,
  formatLongPrice,
  formatLongQuantity,
  formatNumber,
} from '../../lib/utils';

const defaultFilterParams = {
  newRowsAction: 'keep',
};

const filingTradeTypes = {
  a: 'Grant or Award',
  c: 'Derivative Conversion',
  d: 'Disposition to Issuer',
  e: 'Derivative Expiration',
  f: 'Tax',
  g: 'Gift',
  h: 'Derivative Expiration',
  i: 'Acquisition or Disposition',
  j: 'Acquisition or Disposition',
  k: 'Equity Swap',
  l: 'Acquisition',
  m: 'Option Exercise',
  o: 'Exercise of Out-of-the-Money Derivatives',
  p: 'Purchase',
  s: 'Sell',
  u: 'Disposition',
  v: 'Voluntarily Reported',
  w: 'Acquisition or Disposition',
  x: 'Exercise of In-the-Money or At-the-Money Derivatives',
  z: 'Voting Trust',
};

export default [
  {
    cellClass: 'ticker-cell',
    field: 'last_filing_date',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      ...dateFilterParams,
      buttons: ['clear'],
    },
    filterValueGetter: ({ data }) => {
      return data?.last_filing_date ?? '';
    },
    headerName: 'Filing Date',
    initialWidth: 100,
    title: 'Insider Trade Date',
    valueFormatter: ({ value }) => formatDate(parseISO(value), 'MMM dd, yyyy'),
    ...headerInfoTooltipDef('The date on which this insider trade was filed'),
  },
  {
    cellClass: 'ticker-cell company-name-cell',
    cellRenderer: 'insiderNetworthLink',
    field: 'insider_names',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Insider Names',
    initialWidth: 150,
    title: 'Insider Names',
    ...headerInfoTooltipDef('All the names of insiders included'),
  },
  {
    cellClass: 'ticker-cell company-name-cell',
    field: 'insider_titles_unique',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Insider Title',
    initialWidth: 200,
    title: 'Insider Title',
    tooltipField: 'insider name',
    ...headerInfoTooltipDef('A list of titles for the insiders included'),
  },
  {
    cellClass: 'insider-cell',
    cellRenderer: 'insiderTooltipCell',
    comparator: (valueA, valueB) => valueA.fieldVal - valueB.fieldVal,
    field: 'traded_shares',
    filter: 'agNumberColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    filterValueGetter: ({ data }) => {
      return data.traded_shares;
    },
    headerName: 'Qty.',
    initialWidth: 80,
    title: 'Insider Shares Traded',
    valueGetter: ({ data }) => {
      const acquired = data.non_derivative?.trades?.acquired?.traded_shares;
      const disposed = data.non_derivative?.trades?.disposed?.traded_shares;
      const tooltipLines: string[] = [];
      if (acquired !== 0) {
        tooltipLines.push(`Acquired Shares: ${acquired}`);
      }
      if (disposed !== 0) {
        tooltipLines.push(`Disposed Shares: ${disposed}`);
      }
      return {
        field: formatLongQuantity(data.traded_shares),
        fieldVal: data.traded_shares,
        tooltipLines: tooltipLines,
      };
    },
    ...headerInfoTooltipDef('The total shares acquired/disposed'),
  },
  {
    cellClass: 'insider-cell',
    cellRenderer: 'insiderTooltipCell',
    comparator: (valueA, valueB) => valueA.fieldVal - valueB.fieldVal,
    field: 'traded_share_price',
    filter: 'agNumberColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    filterValueGetter: ({ data }) => {
      return data.traded_share_price;
    },
    headerName: 'Price',
    initialWidth: 60,
    title: 'Insider Trade Price',
    valueGetter: ({ data }) => {
      const acquired = data.non_derivative?.trades?.acquired?.traded_share_price;
      const disposed = data.non_derivative?.trades?.disposed?.traded_share_price;
      const tooltipLines: string[] = [];
      if (acquired !== 0) {
        tooltipLines.push(`Acquired Price: ${formatLongPrice(acquired)}`);
      }
      if (disposed !== 0) {
        tooltipLines.push(`Disposed Price: ${formatLongPrice(disposed)}`);
      }
      return {
        field: formatLongPrice(data.traded_share_price),
        fieldVal: data.traded_share_price,
        tooltipLines: tooltipLines,
      };
    },
    ...headerInfoTooltipDef('The average price of acquired/disposed shares'),
  },
  {
    cellClass: 'insider-cell',
    cellRenderer: 'insiderTooltipCell',
    comparator: (valueA, valueB) => valueA.fieldVal - valueB.fieldVal,
    field: 'traded_value',
    filter: 'agNumberColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    filterValueGetter: ({ data }) => {
      return data.traded_value;
    },
    headerName: 'Size ($)',
    initialWidth: 80,
    title: 'Insider Trade Size ($)',
    valueGetter: ({ data }) => {
      const acquired = data.non_derivative?.trades?.acquired?.traded_value;
      const disposed = data.non_derivative?.trades?.disposed?.traded_value;
      const tooltipLines: string[] = [];
      if (acquired !== 0) {
        tooltipLines.push(`Acquired Value: ${formatLongPrice(acquired)}`);
      }
      if (disposed !== 0) {
        tooltipLines.push(`Disposed Value: ${formatLongPrice(disposed)}`);
      }
      return {
        field: formatLongPrice(data.traded_value),
        fieldVal: data.traded_value,
        tooltipLines: tooltipLines,
      };
    },
    ...headerInfoTooltipDef('The total dollar value of the shares traded'),
  },
  {
    cellClass: 'insider-cell',
    cellRenderer: 'insiderTooltipCell',
    comparator: (valueA, valueB) => {
      if (valueA === valueB) return 0;
      return valueA.field > valueB.field ? 1 : -1;
    },
    field: 'trade_status',
    filter: 'agSetColumnFilter',
    filterParams: {
      buttons: ['clear'],
      ...defaultFilterParams,
    },
    filterValueGetter: ({ data }) => {
      return data.trade_status ?? filingTradeTypes[data.trade_types[0]].toUpperCase();
    },
    headerName: 'Type',
    initialWidth: 120,
    title: 'Insider Buy or Sell',
    valueGetter: ({ data }) => {
      const tooltipLines = data?.trade_types?.map((t: any) => {
        const desc = filingTradeTypes[t];
        return `${t.toUpperCase()} - ${desc}`;
      });
      return {
        field: data.trade_status ?? filingTradeTypes[data.trade_types[0]].toUpperCase(),
        tooltipLines: tooltipLines,
      };
    },
    ...headerInfoTooltipDef('Insider buy or sell, a breakdown of the type of transaction this filing reported'),
  },
  {
    cellClass: 'insider-cell',
    cellRenderer: 'insiderTooltipCell',
    comparator: (valueA, valueB) => valueA.fieldVal - valueB.fieldVal,
    field: 'traded_percentage_string',
    filter: 'agNumberColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    filterValueGetter: ({ data }) => {
      return data.traded_percentage;
    },
    headerName: 'Δ Own',
    initialWidth: 60,
    title: 'Insider Shares Change',
    valueGetter: ({ data }) => {
      if (data.traded_percentage === 9999) {
        return {
          field: '∞',
          fieldVal: data.traded_percentage,
          tooltipLines: ['This is a new position. This ', 'insider had no previous shares. '],
        };
      }

      const acquired = data.non_derivative?.trades?.acquired?.traded_percentage;
      const disposed = data.non_derivative?.trades?.disposed?.traded_percentage;
      const tooltipLines: string[] = [];
      if (acquired !== 0) {
        tooltipLines.push(`Acquired Percentage: ${formatNumber(acquired)}%`);
      }
      if (disposed !== 0) {
        tooltipLines.push(`Disposed Percentage: ${formatNumber(disposed)}%`);
      }
      return {
        field: formatNumber(data.traded_percentage) + '%',
        fieldVal: data.traded_percentage,
        tooltipLines: tooltipLines,
      };
    },
    ...headerInfoTooltipDef('The percentage change in the shares held'),
  },
  {
    cellClass: 'insider-cell',
    cellRenderer: 'insiderTooltipCell',
    comparator: (valueA, valueB) => valueA.fieldVal - valueB.fieldVal,
    field: 'remaining_shares',
    filter: 'agNumberColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    filterValueGetter: ({ data }) => {
      return data.remaining_shares;
    },
    headerName: 'Own',
    initialWidth: 100,
    title: 'Insider Shares Owned',
    valueGetter: ({ data }) => {
      const totalVal = formatLongPrice(data.remaining_shares * data.traded_share_price);
      return {
        field: formatLongQuantity(data.remaining_shares),
        fieldVal: data.remaining_shares,
        tooltipLines: [`${totalVal} @ ${formatLongPrice(data.traded_share_price)} per share`],
      };
    },
    ...headerInfoTooltipDef('The remaining shares still owned by the insider'),
  },
  {
    cellClass: 'ticker-cell',
    field: 'first_trade_date',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Trade Dates',
    initialWidth: 120,
    title: 'Insider Trade Date Range',
    valueGetter: ({ data }) => {
      const start = data.first_trade_date ? formatDate(parseISO(data?.first_trade_date), 'MMM dd') : undefined;
      const end = data?.last_trade_date ? formatDate(parseISO(data?.last_trade_date), 'MMM dd') : undefined;

      if (start && end !== start) {
        return start + ' - ' + end;
      }
      return start ?? '-';
    },
    ...headerInfoTooltipDef('A date range for transactions reported in this filing'),
  },
  {
    cellClass: 'ticker-cell',
    cellRenderer: 'secFilingLink',
    field: 'html_url',
    filter: false,
    headerName: 'SEC Filing',
    initialWidth: 70,
    sortable: false,
    suppressAutoSize: true,
    suppressMenu: true,
    suppressSizeToFit: true,
    ...headerInfoTooltipDef('The SEC filing for this insider trade'),
  },
] as (ColDef | ColGroupDef)[];

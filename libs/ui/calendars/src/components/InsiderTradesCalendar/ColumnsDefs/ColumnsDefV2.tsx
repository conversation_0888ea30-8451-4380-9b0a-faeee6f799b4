import type { ColDef, ColGroupDef } from '@ag-grid-community/core';
import dayjs from 'dayjs';
import numeral from 'numeral';
import relativeTime from 'dayjs/plugin/relativeTime';
import intersection from 'lodash/intersection';

import { headerInfoTooltipDef, formatLongPrice, formatLongQuantity } from '../../lib/utils';

dayjs.extend(relativeTime);

export const getBuySellLabel = ({ percent, types }) => {
  if (types.includes('p') && (percent > 0 || !percent)) {
    return 'Purchase';
  } else if (types.includes('s') && percent < 0) {
    return 'Sale';
  } else if (intersection(types, ['m', 'c', 'o', 'x', 'e', 'h', 'k']).length > 0) {
    if (types.includes('p')) {
      return 'Buy-Options';
    } else if (types.includes('s')) {
      return 'Sell-Options';
    } else {
      return 'Options';
    }
  } else if (intersection(types, ['w', 'j', 'i', 'l', 'u', 'd']).length > 0) {
    if (percent > 0) {
      return 'Acquisition';
    } else if (percent < 0) {
      return 'Disposition';
    } else {
      return 'Acq/Dis';
    }
  } else if (intersection(types, ['g', 'a']).length > 0) {
    return 'Grant';
  } else if (types.includes('f')) {
    return 'Tax';
  } else {
    return types.join(' / ');
  }
};

export const getDerivativeTooltips = ({ key, transactions, type = '' }) => {
  const acquiredKey = `trade_acquired_${key}`;
  const disposedKey = `trade_disposed_${key}`;
  const formatNumber = val => numeral(val).format('0,0');
  const formatKey = type === 'price' ? formatLongPrice : formatNumber;
  let tooltipLines = '';
  if (transactions?.[acquiredKey] && transactions[acquiredKey] !== 0) {
    tooltipLines += `<span>Acquired: ${formatKey(transactions[acquiredKey])}</span>`;
  }
  if (transactions?.[disposedKey] && transactions[disposedKey] !== 0) {
    tooltipLines += `<span>Disposed: ${formatKey(transactions[disposedKey])}</span>`;
  }
  return tooltipLines;
};

const defaultFilterParams = {
  newRowsAction: 'keep',
};

export const filingTradeTypes = {
  a: 'Grant or Award',
  c: 'Derivative Conversion',
  d: 'Disposition to Issuer',
  e: 'Derivative Expiration',
  f: 'Tax',
  g: 'Gift',
  h: 'Derivative Expiration',
  i: 'Acquisition or Disposition',
  j: 'Acquisition or Disposition',
  k: 'Equity Swap',
  l: 'Acquisition',
  m: 'Option Exercise',
  o: 'Exercise of Out-of-the-Money Derivatives',
  p: 'Purchase',
  s: 'Sell',
  u: 'Disposition',
  v: 'Voluntarily Reported',
  w: 'Acquisition or Disposition',
  x: 'Exercise of In-the-Money or At-the-Money Derivatives',
  z: 'Voting Trust',
};

export default [
  {
    cellClass: 'ticker-cell',
    cellRenderer: 'insiderTickerCikLink',
    field: 'company_ticker',
    headerName: 'Symbol',
    initialWidth: 100,
  },
  {
    field: 'company_name',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Company Name',
    initialWidth: 150,
    tooltipValueGetter(params) {
      return params.data.company_name || '';
    },
    tooltipWidth: 200,
  },
  {
    field: 'traded_shares',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Traded Shares',
    initialWidth: 80,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivative = data.derivative?.transactions;
      const non_derivative = data.non_derivative?.transactions;
      let tooltipLines = '';
      if (derivative) {
        const tooltips = getDerivativeTooltips({ key: 'shares', transactions: derivative });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Derivative Shares:</b> ${tooltips}</div>`;
        }
      }
      if (non_derivative) {
        const tooltips = getDerivativeTooltips({ key: 'shares', transactions: non_derivative });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Non Derivative Shares:</b> ${tooltips}</div>`;
        }
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => formatLongQuantity(value),
    ...headerInfoTooltipDef('The total shares acquired/disposed'),
  },
  {
    field: 'traded_share_price',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Price',
    initialWidth: 60,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivative = data.derivative?.transactions;
      const non_derivative = data.non_derivative?.transactions;
      let tooltipLines = '';
      if (derivative) {
        const tooltips = getDerivativeTooltips({ key: 'average_share_price', transactions: derivative, type: 'price' });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Derivative Avg Prices:</b> ${tooltips}</div>`;
        }
      }
      if (non_derivative) {
        const tooltips = getDerivativeTooltips({
          key: 'average_share_price',
          transactions: non_derivative,
          type: 'price',
        });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Non Derivative Avg Prices:</b> ${tooltips}</div>`;
        }
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => numeral(value).format('$0,0.00'),
    ...headerInfoTooltipDef('The average price of acquired/disposed shares'),
  },
  {
    field: 'traded_value',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Size ($)',
    initialWidth: 80,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivative = data.derivative?.transactions;
      const non_derivative = data.non_derivative?.transactions;
      let tooltipLines = '';
      if (derivative) {
        const tooltips = getDerivativeTooltips({ key: 'value', transactions: derivative, type: 'price' });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Derivative Value:</b> ${tooltips}</div>`;
        }
      }
      if (non_derivative) {
        const tooltips = getDerivativeTooltips({ key: 'value', transactions: non_derivative, type: 'price' });
        if (tooltips.length > 0) {
          tooltipLines += `<div class="flex flex-col"><b>Non Derivative Value:</b> ${tooltips}</div>`;
        }
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => numeral(value).format('$0.00a').toUpperCase(),
    ...headerInfoTooltipDef('The total dollar value of the shares traded'),
  },
  {
    field: 'last_filing_date',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Filing Date',
    initialWidth: 100,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      return dayjs(params.data.last_filing_date).format('h:mm:ss A');
    },
    valueFormatter: ({ value }) => dayjs(value).format('MMM DD, YYYY'),
    width: 120,
    ...headerInfoTooltipDef('The date on which this insider trade was filed'),
  },
  {
    field: 'trade_codes',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Type',
    initialWidth: 120,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      let tooltips = '<div className="flex flex-col">';
      const types = params.data?.trade_codes || [];
      for (const code of types) {
        tooltips += `<div className="flex flex-row" key={code}>
            ${filingTradeTypes[code] ? code + ' - ' + filingTradeTypes[code] : code}
          </div>`;
      }
      return tooltips + '</div>';
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => {
      return getBuySellLabel(value);
    },
    valueGetter: params => {
      const tradeTypes = params.data.trade_codes || [];
      const tradedPercentage = params.data.traded_percentage || 0;
      return { percent: tradedPercentage, types: tradeTypes };
    },
    ...headerInfoTooltipDef('Insider buy or sell, a breakdown of the type of transaction this filing reported'),
  },
  {
    field: 'traded_percentage',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Δ Own',
    initialWidth: 60,
    tooltipShowDelay: 0,
    tooltipValueGetter: ({ data }) => {
      const derivativePercent = data.derivative?.transactions?.traded_percentage;
      const nonDerivativePercent = data.non_derivative?.transactions?.traded_percentage;
      let tooltipLines = '';
      if (derivativePercent && derivativePercent !== 0) {
        tooltipLines += `<div class="flex flex-col"><b>Derivative</b> <div>Percentage: ${numeral(derivativePercent / 100).format('0,0.00%')}</div></div>`;
      }
      if (nonDerivativePercent && nonDerivativePercent !== 0) {
        tooltipLines += `<div class="flex flex-col"><b>Non Derivative</b> <div>Percentage: ${numeral(nonDerivativePercent / 100).format('0,0.00%')}</div></div>`;
      }
      return tooltipLines;
    },
    tooltipWidth: 180,
    valueFormatter: ({ value }) => numeral(value / 100).format('0,0.00%'),
    ...headerInfoTooltipDef('The percentage change in the shares held'),
  },
  {
    field: 'remaining_shares_in_filing',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Own',
    initialWidth: 100,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      const shares = params.data.non_derivative?.transactions?.remaining_shares || 0;
      const tradePrice = params.data.traded_share_price || 0;
      if (shares !== 0 && tradePrice !== 0) {
        return `
          <span>${numeral(shares * tradePrice)
            .format('$0.00a')
            .toUpperCase()} @ $${tradePrice} per share</span>
        `;
      }
      return '';
    },
    tooltipWidth: 140,
    valueFormatter: ({ value }) => numeral(value).format('0.00a').toUpperCase(),
    ...headerInfoTooltipDef('The remaining shares still owned by the insider'),
  },
  {
    cellRenderer: 'insiderNetworthLink',
    field: 'insiders',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Insider Names',
    initialWidth: 150,
    tooltipShowDelay: 0,
    tooltipValueGetter: params => {
      const names = params.data.insiders.filter(i => i['name']);
      let tooltipLines = '';
      for (const insider of names) {
        tooltipLines += `<span class="flex flex-row" key=${insider.cik}>
            ${insider.name} ${insider.titles.length > 0 ? '- ' + insider.titles.join(', ') : ''}
          </span>`;
      }
      return tooltipLines;
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => {
      const values = value
        .filter(i => i['name'])
        .map((itm: any) => {
          return itm['name'];
        });

      const uniqueValues = [...new Set(values)];
      return uniqueValues.join(', ');
    },
    ...headerInfoTooltipDef('All the names of insiders included'),
  },
  {
    field: 'insiders',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Insider Titles',
    initialWidth: 150,
    tooltipValueGetter: ({ data }) => {
      const tooltipLines = '<div className="flex flex-col">';
      const titles = data.insiders
        .filter(i => i['titles'] && i['titles'].length > 0)
        .map((itm: any) => {
          if (itm['titles'] instanceof Array) {
            return itm['titles'].filter(i => i && i.length > 0).join(', ');
          }
          return `<span>${itm['titles']}</span>`;
        });
      return titles.length > 0 ? tooltipLines + titles.join('') + '</div>' : '';
    },
    tooltipWidth: 200,
    valueFormatter: ({ value }) => {
      const values = value
        .filter(i => i['titles'] && i['titles'].length > 0)
        .map((itm: any) => {
          if (itm['titles'] instanceof Array) {
            return itm['titles'].filter(i => i && i.length > 0).join(', ');
          }
          return itm['titles'];
        });

      const uniqueValues = [...new Set(values)];
      return uniqueValues.join(', ');
    },
    ...headerInfoTooltipDef('A list of titles for the insiders included'),
  },
  {
    field: 'trade_dates',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Trade Dates',
    initialWidth: 120,
    tooltipValueGetter(params) {
      const dates = params.data?.trade_dates || [];
      if (dates.length === 0) return '';
      const formattedDates = dates.map(date => dayjs(date).format('MMM DD YYYY')).join(', ');
      return `<div class="flex flex-col">${formattedDates}</div>`;
    },
    valueFormatter: ({ value }) => {
      if (value instanceof Array) {
        if (value.length > 1 && value[0] !== value[value.length - 1]) {
          const sortedDates = value.sort((a, b) => {
            return dayjs(a).isAfter(dayjs(b)) ? 1 : -1;
          });

          return (
            dayjs(sortedDates[0]).format('MMM DD') + ' - ' + dayjs(sortedDates[sortedDates.length - 1]).format('MMM DD')
          );
        } else {
          return dayjs(value[0]).format('MMM DD');
        }
      } else {
        return dayjs(value).format('MMM DD');
      }
    },
    ...headerInfoTooltipDef('A date range for transactions reported in this filing'),
  },
  {
    cellRenderer: params => {
      return params.value ? `Scheduled` : null;
    },
    field: 'is_10b5',
    filter: 'agSetColumnFilter',
    filterParams: {
      ...defaultFilterParams,
      buttons: ['clear'],
      defaultToNothingSelected: true,
    },
    headerName: 'Is Scheduled',
    initialWidth: 60,
    ...headerInfoTooltipDef(
      "The transactions were done as part of the insider's 10b5-1 trading plan. This usually indicates that this filing is not a signal of insider sentiment.",
    ),
  },
  {
    cellClass: 'ticker-cell',
    cellRenderer: 'secFilingLink',
    field: 'index_url',
    filter: false,
    headerName: 'SEC Filing',
    initialWidth: 70,
    sortable: false,
    suppressAutoSize: true,
    suppressMenu: true,
    suppressSizeToFit: true,
    tooltipWidth: 200,
    ...headerInfoTooltipDef('The SEC filing for this insider trade'),
  },
] as (ColDef | ColGroupDef)[];

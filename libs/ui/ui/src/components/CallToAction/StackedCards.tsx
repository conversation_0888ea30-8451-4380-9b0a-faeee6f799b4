'use client';
import React from 'react';
import { Text } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { BzImage } from '@benzinga/image';
import styled from '@benzinga/themetron';
import { CallToActionButton } from './index';
import type { ProductBasedCTAProps, ProductBasedCTALayout } from './ProductBasedCTA';
import { extractImageSize } from '@benzinga/image';
import { StackedCardHeader } from './StackedCardHeader';

export interface StackedCardsProps {
  groups?: ProductBasedCTAProps[];
  layout?: ProductBasedCTALayout;
  social_proofing?: string;
  heading?: string;
}

export const StackedCards: React.FC<StackedCardsProps> = ({ groups, heading, layout, social_proofing }) => {
  return (
    <StackedCardsWrapper className={`p-5 ${layout}`}>
      <StackedCardHeader heading={heading} social_proofing={social_proofing} />
      {groups &&
        groups.map((campaign, index) => {
          const image = extractImageSize(campaign?.image);
          return (
            <div
              className="call-to-action-container flex flex-col sm:flex-row gap-4 w-full justify-between items-center"
              key={index}
            >
              <div className="sm:w-1/3 w-full promo-image-wrap flex items-center justify-center">
                {image?.url && (
                  <div className="image-wrapper">
                    <BzImage alt={campaign.title} height={75} src={image?.url} width={120} />
                  </div>
                )}
              </div>
              <div className="sm:w-2/3 w-full flex justify-center call-to-action-copy">
                <div>
                  <div className="title">{campaign.title}</div>
                  {campaign?.description && (
                    <Text
                      className="call-to-action-description"
                      dangerouslySetInnerHTML={{ __html: campaign?.description }}
                      lines={5}
                    />
                  )}
                  <SmartLink href={campaign?.url}>
                    <CallToActionButton text={campaign?.button_text} variant="flat-blue" />
                  </SmartLink>
                </div>
              </div>
            </div>
          );
        })}
    </StackedCardsWrapper>
  );
};

const StackedCardsWrapper = styled.div`
  border: 2px solid #e0e4e9;
  border-radius: 6px;
  .call-to-action-container {
    border-bottom: 2px solid #e0e4e9;
    padding: 2rem 0;
    line-height: 1.4;
    .image-wrapper {
      margin-bottom: 0;
    }
    .title {
      font-size: 1rem;
      font-weight: 600;
      text-align: center;
      margin-bottom: 1rem;
    }
    .call-to-action-description {
      text-align: center;
      margin-bottom: 0.5rem;
    }
    .cta-button {
      width: 300px;
      margin: 0 auto;
    }
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }
`;

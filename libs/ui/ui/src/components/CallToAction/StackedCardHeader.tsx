'use client';
import React from 'react';
import { sanitizeHTML } from '@benzinga/frontend-utils';

export interface StackedCardHeaderProps {
  social_proofing?: string;
  heading?: string;
}

export const StackedCardHeader: React.FC<StackedCardHeaderProps> = ({ heading, social_proofing }) => {
  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        {heading && <div className="font-semibold">{heading}</div>}
        {social_proofing && (
          <div className="flex items-end justify-end">
            <div
              className="text-right text-sm bg-gray-200 rounded px-2 py-1"
              dangerouslySetInnerHTML={{ __html: sanitizeHTML(social_proofing) }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

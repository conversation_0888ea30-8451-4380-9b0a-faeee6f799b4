import styled from '@benzinga/themetron';
import React from 'react';
interface TimeNotifyModeBtnProps {
  selected: string;
  value: string;
  title: string;
  onClick(event: React.MouseEvent<HTMLButtonElement>): void;
}

export const NotificationModeBtn: React.FC<TimeNotifyModeBtnProps> = props => {
  return (
    <Wrapper onClick={props.onClick} selected={props.selected} value={props.value}>
      {props.title}
    </Wrapper>
  );
};

const Wrapper = styled.button<{ selected: string }>`
  background-color: ${props =>
    props.value === props.selected ? props.theme.colors.brand : props.theme.colors.backgroundActive};
  padding: 1.05em 1.5em;
  border-radius: 4px;

  display: flex;
  &:hover {
    background: ${props => props.theme.colors.backgroundInactive};
    color: ${props => props.theme.colors.foregroundActive};
  }
  color: ${props =>
    props.selected === props.value ? props.theme.colors.foregroundActive : props.theme.colors.foregroundInactive};
  white-space: nowrap;
  font-family: 'Nimbus Sans D OT';
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  height: fit-content;

  &:focus {
    border: none;
    outline: none;
  }
`;

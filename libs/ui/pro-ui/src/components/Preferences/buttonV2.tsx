import styled, { css } from '@benzinga/themetron';
import React from 'react';
interface TimeNotifyModeBtnProps {
  selected: string;
  value: string;
  title: string;
  anyBtnHovered: { isHovered: boolean; value: string };
  onClick(event: React.MouseEvent<HTMLButtonElement>): void;
  onMouseEnter?(event: React.MouseEvent<HTMLButtonElement>): void;
  onMouseLeave?(): void;
}

export const TimeNotifyModeBtn: React.FC<TimeNotifyModeBtnProps> = props => {
  return (
    <Wrapper
      anyBtnHovered={props.anyBtnHovered}
      onClick={props.onClick}
      onMouseEnter={props.onMouseEnter}
      onMouseLeave={props.onMouseLeave}
      selected={props.selected}
      value={props.value}
    >
      {props.title}
    </Wrapper>
  );
};

const Wrapper = styled.button<{ selected: string; anyBtnHovered: { isHovered: boolean; value: string } }>`
  background-color: ${props =>
    props.value === props.selected ? props.theme.colors.brand : props.theme.colors.backgroundActive};
  padding: 1em 1.5em;
  height: auto;
  width: 100%;
  display: flex;
  &:hover,
  :active {
    background: ${props => props.theme.colors.backgroundInactive};
    color: ${props => props.theme.colors.foregroundActive};
  }
  white-space: nowrap;
  color: ${props =>
    props.value === props.selected ? props.theme.colors.foregroundActive : props.theme.colors.foregroundInactive};
  font-family: 'Nimbus Sans D OT';
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  height: fit-content;

  opacity: ${props =>
    props.anyBtnHovered.isHovered === true &&
    props.value === props.selected &&
    props.anyBtnHovered.value !== props.selected
      ? '0.5'
      : '1'};
`;

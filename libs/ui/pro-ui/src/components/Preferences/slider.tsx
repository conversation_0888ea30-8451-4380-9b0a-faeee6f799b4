import styled from '@benzinga/themetron';
import React from 'react';
import { Slider } from 'antd';
import SliderKnobSrc from './SliderKnob.png';
import { StaticImageData } from 'next/image';
interface SliderProps {
  value: number;
  min: number;
  max: number;
  onChange(value: number | number[]): void;
}

export const TestSlider: React.FC<SliderProps> = props => {
  return (
    <CustomSlider
      className="horizontal-slider"
      knobImage={SliderKnobSrc}
      max={100}
      min={0}
      onChange={props.onChange}
      step={1}
      tooltip={{
        open: false,
      }}
      value={props.value}
    />
  );
};

const getImageUrl = (image: string | StaticImageData) => {
  const imageUrl = `url(${image})`;
  return imageUrl;
};

const CustomSlider = styled(Slider)<{ knobImage: string | StaticImageData }>`
  margin: 0px;
  .ant-slider-handle {
    /* background-image: ${props => getImageUrl(props.knobImage)} !important; */
    background-size: cover !important;
    background-color: ${props => props.theme.colors.backgroundInactive};
    border-radius: 9px;
    width: 18px !important;
    height: 18px !important;
    position: absolute;
    top: -3px;
    left: -5px;
    transform: translate(-50%, -50%);

    &::before,
    &::after,
    &:hover,
    &:focus,
    &:focus::before,
    &:focus::after,
    &:hover::before,
    &:hover::after {
      /* background-color: transparent; */
      box-shadow: none;
      width: 0px;
      height: 0px;
    }
  }

  .ant-slider-handle svg {
    fill: ${props => props.theme.colors.brand};
  }

  .ant-slider-tooltip,
  .ant-slider-tooltip:focus {
    display: none;
  }

  .ant-slider-rail,
  .ant-slider-track {
    border-radius: 3px; /* Adjust the border radius value as needed */
    &:hover {
      .ant-slider-handle {
        display: none;
      }
    }
  }
`;

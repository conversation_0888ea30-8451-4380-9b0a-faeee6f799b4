import { SessionContext } from '@benzinga/session-context';
import React from 'react';

import { GptSearchManager } from '@benzinga/benzinga-gpt-search-manager';
import {
  GptSearchItemProps,
  GptSearchItemVersion,
  GptSearchModuleId,
  SearchItem,
  createGptSearchItem,
  isGptSearchItem,
} from '@benzinga/search-modules';
import {
  SearchModule,
  AutocompleteModule,
  ActionModule,
  TagModule,
  ClipboardModule,
  isNoCommaString,
  QueryScore,
} from './Modules';
import { SearchContext, SearchContextType } from '../context';
import { ResultHead, ResultDescription } from '../Expression';
import styled from 'styled-components';
import { FaRobot } from 'react-icons/fa';
import { defaultGptPrompts } from '../utils';

export interface GptSearchModule extends SearchModule<GptSearchItemProps> {
  id: typeof GptSearchModuleId;
  autocomplete: AutocompleteModule<GptSearchItemProps>;
}

const createGptSearchClipboardModule = (): ClipboardModule => {
  return {
    onCopyJSON: token => {
      if (!isGptSearchItem(token)) return undefined;
      return [token];
    },
    onCopyString: token => {
      if (!isGptSearchItem(token)) return undefined;
      return isNoCommaString(token.data.prompt) ? [token.data.prompt] : undefined;
    },
    onPasteJSON: token => {
      if (!isGptSearchItem(token)) return undefined;
      return GptSearchModuleItemLoader(token);
    },
  };
};

const createGptSearchTagModule = (): TagModule => {
  return {
    render: item => {
      if (!isGptSearchItem(item)) return undefined;
      return {
        icon: <GptSearchIcon />,
        text: item.data.prompt,
        type: 'text',
      };
    },
    tagInputText: item => {
      if (!isGptSearchItem(item)) return undefined;
      return item.data.prompt;
    },
  };
};

const GptSearchModuleItemLoader = (item: SearchItem) => {
  switch (item.version) {
    case GptSearchItemVersion:
    default:
      return item as SearchItem<GptSearchItemProps>;
  }
};
const createGptSearchActionModule = (): ActionModule => {
  return {
    dropdown: () => {
      return [];
    },
  };
};

const PromptHistory: (prompt: string) => React.FC<{ isSelected: boolean; isLocked?: boolean }> =
  (prompt: string) => props => {
    return (
      <>
        <ResultHead isSelected={props.isSelected}>{'Prompt'}</ResultHead>
        <ResultDescription isSelected={props.isSelected}>{prompt}</ResultDescription>
      </>
    );
  };

const createGptSearchAutocompleteModule = (
  gptSearchManager: GptSearchManager,
  _search: SearchContextType,
): AutocompleteModule<GptSearchItemProps> => {
  return {
    dropdown: (item: SearchItem) => {
      if (!isGptSearchItem(item)) return undefined;
      return {
        component: PromptHistory(item.data.prompt ?? 'No Prompt History Found'),
        id: item.id,
        locked: false,
        searchItem: item,
        type: 'AutocompleteDropdown',
        version: GptSearchItemVersion,
      };
    },
    onSelect: item => {
      if (!isGptSearchItem(item)) return undefined;
      return item;
    },
    query: (text: string, limit: number, selectedItems?: SearchItem[]) => {
      const moduleSelectedItems = selectedItems?.filter(isGptSearchItem) || [];
      const data = gptSearchManager.getCachedData();
      return {
        cancel: () => [],
        promise: new Promise(resolve => {
          (async () => {
            if (text === '') {
              const list = [...defaultGptPrompts, ...data]
                .map<[SearchItem<GptSearchItemProps>, QueryScore]>(d => {
                  return [createGptSearchItem(d.prompt), QueryScore.StartsWithMatch];
                })
                .filter(
                  item => !moduleSelectedItems.some(selectedItem => selectedItem.data.prompt === item[0].data.prompt),
                )
                .slice(0, limit);

              return resolve({
                canceled: false,
                ok: list,
              });
            }

            const previousPrompts = data.filter(d => d.prompt.toLowerCase().includes(text.toLowerCase()));
            const list = previousPrompts.map<[SearchItem<GptSearchItemProps>, QueryScore]>(d => {
              return [createGptSearchItem(d.prompt), QueryScore.StartsWithMatch];
            });

            const currentPrompt = [text].map<[SearchItem<GptSearchItemProps>, QueryScore]>(d => [
              createGptSearchItem(d),
              QueryScore.StartsWithMatch,
            ]);

            return resolve({
              canceled: false,
              ok: [...currentPrompt, ...list]
                .filter(
                  item =>
                    !moduleSelectedItems.some(selectedItem => selectedItem.data.prompt.includes(item[0].data.prompt)),
                )
                .slice(0, limit),
            });
          })();
        }),
      };
    },
  };
};

export const createGptSearchModules = (
  gptSearchManager: GptSearchManager,
  search: SearchContextType,
): GptSearchModule => {
  return {
    action: createGptSearchActionModule(),
    autocomplete: createGptSearchAutocompleteModule(gptSearchManager, search),
    clipboard: createGptSearchClipboardModule(),
    id: GptSearchModuleId,
    loader: GptSearchModuleItemLoader,
    state: 'beta',
    tag: createGptSearchTagModule(),
  };
};

export const useGptSearchModules = () => {
  const session = React.useContext(SessionContext);
  const search = React.useContext(SearchContext);
  const gptSearchManager = session.getManager(GptSearchManager);
  return React.useMemo(() => createGptSearchModules(gptSearchManager, search), [gptSearchManager, search]);
};

const GptSearchIcon = styled(FaRobot)`
  path {
    padding-top: 10px;
    height: 10px !important;
    width: 10px !important;
  }
`;

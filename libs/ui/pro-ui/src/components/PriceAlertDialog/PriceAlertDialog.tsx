'use client';
import React from 'react';

import styled from '@benzinga/themetron';
import { LoggingManager, StockSymbol } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { SafeType, SafePromise } from '@benzinga/safe-await';

import { InputNumber, Select } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { Crossing, PriceAlerts, PriceAlertsManager } from '@benzinga/price-alert-manager';
import { useQuoteSubscription } from '@benzinga/quotes-manager-hooks';
import { DateTime } from 'luxon';
import { NBSP, PerformanceColor } from '../Ticker';
import BigNumber from 'bignumber.js';
import { getCurrentSymbolAlerts, negativeValueWarning } from './utils';
import Hooks from '@benzinga/hooks';
import { Edit, Close, Up, AlarmSmall, CheckmarkAlert, Delete } from '@benzinga/themed-icons';
import { isString } from '@benzinga/utils';

export interface PriceAlertDialogProps {
  allowEdit: boolean;
  onClose: () => void;
  symbol: StockSymbol;
}

interface State {
  price: number | undefined | null;
  alerts: Partial<PriceAlerts.Alert>[];
  crossing: number;
}

export const PriceAlertDialog = React.memo<PriceAlertDialogProps>(props => {
  const textboxRef = React.useRef<any>(null); // i had to use any because antd does not export TextArea Type
  const session = React.useContext(SessionContext);
  const priceAlertManager = session.getManager(PriceAlertsManager);
  const { quote } = useQuoteSubscription(props.symbol);

  const [state, setState] = React.useState<State>(() => ({
    alerts: getCurrentSymbolAlerts(priceAlertManager.getStoredPriceAlerts() ?? [], props.symbol),
    crossing: Crossing.UP,
    price: undefined,
  }));

  React.useEffect(() => {
    if (textboxRef.current) {
      textboxRef.current.focus();
    }
  }, []);

  Hooks.useSubscriber(priceAlertManager, event => {
    if (event.type === 'price_alerts_updated') {
      setState(oldState => ({
        ...oldState,
        alerts: getCurrentSymbolAlerts(event.categoriesAlerts, props.symbol),
      }));
    }
  });

  const updatePriceAlert = React.useCallback(
    async (change?: PriceAlertChange): SafePromise<Partial<PriceAlerts.PostAlertResponse>> => {
      let priceAlertResponse: SafeType<Partial<PriceAlerts.PostAlertResponse> | undefined> = { ok: undefined };
      if (change) {
        switch (change.type) {
          case 'delete':
            // eslint-disable-next-line no-case-declarations
            const response = await priceAlertManager.deletePriceAlert(change.alert.uuid ?? '');
            if (response.ok) {
              session
                .getManager(LoggingManager)
                .log(
                  'log',
                  { category: 'Price Alert', message: `Price Alert successfully deleted for: ${props.symbol}` },
                  ['toast'],
                );
            } else {
              session
                .getManager(LoggingManager)
                .log('error', { category: 'Price Alert', message: `Price Alert delete error for: ${props.symbol}` }, [
                  'toast',
                ]);
            }
            break;
          case 'edit':
            priceAlertResponse = await priceAlertManager.updatePriceAlert({
              alert: {
                priceAlert: {
                  crossing: change.crossing,
                  price: Number(change.price),
                },
                symbol: props.symbol,
                uuid: change.alert.uuid,
              },
            });
            if (priceAlertResponse.ok) {
              session
                .getManager(LoggingManager)
                .log(
                  'log',
                  { category: 'Price Alert', message: `Price Alert successfully updated for ${props.symbol}` },
                  ['toast'],
                );
            } else {
              session
                .getManager(LoggingManager)
                .log(
                  'error',
                  { category: 'Price Alert', message: `Error occurred while updated price alerts ${props.symbol}` },
                  ['toast'],
                );
            }
            break;
          case 'new':
            priceAlertResponse = await priceAlertManager.addPriceAlert({
              alert: {
                priceAlert: {
                  crossing: state.crossing,
                  price: Number(change.price),
                },
                symbol: props.symbol,
              },
            });
            if (priceAlertResponse.ok) {
              session
                .getManager(LoggingManager)
                .log('log', { category: 'Price Alert', message: `Price Alert successfully set for ${props.symbol}` }, [
                  'toast',
                ]);
            } else {
              session
                .getManager(LoggingManager)
                .log(
                  'error',
                  { category: 'Price Alert', message: `Error occurred while saving price alerts ${props.symbol}` },
                  ['toast'],
                );
            }
            break;
        }
      }
      return priceAlertResponse as SafeType<Partial<PriceAlerts.PostAlertResponse>>;
    },
    [priceAlertManager, props.symbol, session, state.crossing],
  );

  const onNewAlertChange = React.useCallback(
    (value: number | null) => {
      if (value !== null) {
        if (value > 0) {
          setState(oldState => ({ ...oldState, price: value }));
          return;
        }
        negativeValueWarning(session.getManager(LoggingManager));
      }
      setState(oldState => ({ ...oldState, price: null }));
    },
    [session],
  );

  const onNewAlertChangeDebounced = Hooks.useDebounce(onNewAlertChange, 300);

  const onChangeCrossing = (value: number) => {
    setState(oldState => ({ ...oldState, crossing: value }));
  };

  const onClose = () => {
    props.onClose();
  };

  const onNewAlert = async () => {
    setState(oldState => {
      if (oldState.price && oldState.price > 0) {
        const newItem: PriceAlertChange = {
          price: oldState.price,
          symbol: props.symbol,
          type: 'new',
        };

        const newState = {
          ...oldState,
          crossing: Crossing.UP,
          price: undefined,
        };

        updatePriceAlert(newItem);
        return newState;
      } else {
        return {
          ...oldState,
          placeholder: 'Price Alert cannot be empty',
        };
      }
    });
  };

  return (
    <AlertsView>
      <ColumnContainer>
        <AlertsHeader>
          <RowContainer>
            <div>
              Price Alert for {props.symbol} -{' '}
              {quote && quote.lastTradePrice ? new BigNumber(quote.lastTradePrice).toFormat(2) : '\xa0'}
              {NBSP}
              {quote && (
                <>
                  <PerformanceColor hasArrow stat={quote.change} /> {NBSP}
                  <PerformanceColor isPercentage stat={quote.percentChange} />
                </>
              )}
            </div>
            {props.allowEdit && (
              <PointerCursor>
                <CloseOutlined onClick={onClose} title="Close" />
              </PointerCursor>
            )}
          </RowContainer>
        </AlertsHeader>
        {props.allowEdit && (
          <AlertsAdd>
            <AlertAddBorder>
              <div style={{ marginBottom: '3px', marginLeft: '3px' }}>Alert me when</div>
              <RowContainer>
                <InputNumber
                  onChange={onNewAlertChangeDebounced}
                  placeholder={'Price'}
                  ref={textboxRef}
                  style={{ width: '50%' }}
                  value={state.price}
                />
                <Select
                  defaultValue={Crossing.UP}
                  onChange={onChangeCrossing}
                  options={[
                    {
                      label: 'Above or equal to (>=)',
                      value: Crossing.UP,
                    },
                    {
                      label: 'Below or equal to (<=)',
                      value: Crossing.DOWN,
                    },
                  ]}
                  style={{ marginLeft: `5px`, marginTop: `1px`, width: 200 }}
                />
                <AddButtonContainer>
                  <AddButton onClick={onNewAlert}>Add</AddButton>
                </AddButtonContainer>
              </RowContainer>
            </AlertAddBorder>
          </AlertsAdd>
        )}
        <AlertBody $isEdit={props.allowEdit}>
          {state.alerts.map((alert, index) => (
            <>
              <PriceAlertComponent alert={alert} allowEdit={props.allowEdit} key={index} onChange={updatePriceAlert} />
              <Line />
            </>
          ))}
        </AlertBody>
      </ColumnContainer>
    </AlertsView>
  );
});

type PriceAlertChange = EditPriceAlertChange | DeletePriceAlertChange | NewPriceAlertChange;

interface EditPriceAlertChange {
  alert: Partial<PriceAlerts.Alert>;
  price: number | undefined;
  crossing: number;
  type: 'edit';
}

interface DeletePriceAlertChange {
  alert: Partial<PriceAlerts.Alert>;
  price: number | undefined;
  type: 'delete';
}

interface NewPriceAlertChange {
  symbol: StockSymbol;
  price: number | undefined;
  type: 'new';
}

interface PriceAlertProps {
  allowEdit: boolean;
  alert: Partial<PriceAlerts.Alert>;
  onChange: (alert: PriceAlertChange) => void;
}

const PriceAlertComponent: React.FC<PriceAlertProps> = props => {
  const textboxRef = React.useRef<any>(null); // i had to use any because antd does not export TextArea Type
  const ogPrice = React.useRef(props.alert.priceAlert?.price ?? 0);
  const [price, setPrice] = React.useState<number>(props.alert.priceAlert?.price ?? 0);
  const [crossing, setCrossing] = React.useState<number>(props.alert.priceAlert?.crossing ?? Crossing.UP);
  const [editingMode, setEditingMode] = React.useState(false);
  const dateCreated = DateTime.fromISO(props.alert.createdDate ?? '').toFormat('ff');
  const dateTriggered = DateTime.fromISO(props.alert.triggeredDate ?? '').toFormat('ff');
  const priceText = props.alert.priceAlert?.price;

  React.useEffect(() => {
    setPrice(priceText ?? 0);
    ogPrice.current = priceText ?? 0;
  }, [priceText]);

  React.useEffect(() => {
    if (editingMode) {
      textboxRef.current.focus();
    }
  }, [editingMode]);

  const onTextChange = (val: number | null) => {
    if (val) {
      setPrice(val);
    }
  };

  const onChanges = (_e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (price !== undefined) {
      ogPrice.current = price;
      props.onChange({ alert: props.alert, crossing: crossing, price, type: 'edit' });
    }
    setEditingMode(false);
  };

  const onCancel = (_e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    setPrice(ogPrice.current);
    setEditingMode(false);
  };

  const onDelete = () => {
    props.onChange({
      alert: props.alert,
      price,
      type: 'delete',
    });
  };

  const onChangeCrossing = (value: number) => {
    setCrossing(value);
  };

  const defaultVal = isString(crossing) ? (crossing === 'UP' ? Crossing.UP : Crossing.DOWN) : crossing;
  if (props.allowEdit && editingMode) {
    return (
      <AlertColumnEditContainer>
        <InputNumber onChange={onTextChange} ref={textboxRef} value={price ?? ''} />
        <Select
          defaultValue={defaultVal}
          onChange={onChangeCrossing}
          options={[
            {
              label: 'Above or equal to (>=)',
              value: Crossing.UP,
            },
            {
              label: 'Below or equal to (<=)',
              value: Crossing.DOWN,
            },
          ]}
          style={{ marginLeft: `5px`, marginTop: `1px`, width: 150 }}
        />
        <RowEditEndContainer>
          <UpdateButton onClick={onChanges}>Update</UpdateButton>
          <CancelButton onClick={onCancel}>Cancel</CancelButton>
        </RowEditEndContainer>
      </AlertColumnEditContainer>
    );
  } else {
    const isDown =
      props.alert.priceAlert &&
      props.alert.priceAlert.crossing &&
      props.alert.priceAlert.crossing.toString() === 'DOWN';

    const isTriggered = props.alert.status && props.alert.status.toString() !== 'ACTIVE' ? true : false;

    return (
      <AlertColumnContainer>
        <AlertLeftOuterDiv $isTriggered={isTriggered}>
          <span>{props.alert.symbol}</span>
          <AlertLeftInnerDiv>
            {isTriggered ? <TriggeredIcon /> : <ActiveAlertIcon />}
            <AlertLeftStatusDiv $isTriggered={isTriggered}>{props.alert.status}</AlertLeftStatusDiv>
          </AlertLeftInnerDiv>
          {isTriggered && <AlertDateTime>on {dateTriggered}</AlertDateTime>}
        </AlertLeftOuterDiv>
        <AlertRightOuterDiv>
          <AlertDiv>
            <AlertText className={isDown ? 'u-downText' : 'u-upText'}>{price ?? ''}</AlertText>
            {isDown ? <DownArrow /> : <UpArrow />}
          </AlertDiv>
          <span>{isDown ? `Price below or equal to (<=)` : `Price above or equal to (>=)`}</span>
          <AlertDateTime>Created {dateCreated}</AlertDateTime>
        </AlertRightOuterDiv>

        {props.allowEdit && (
          <RowContainer>
            <AlertIcons>
              {!isTriggered && (
                <MarginedIcon>
                  <Edit onClick={() => setEditingMode(true)} title="Edit" />
                </MarginedIcon>
              )}
              <MarginedIcon>
                <Delete onClick={onDelete} title="Delete" />
              </MarginedIcon>
            </AlertIcons>
          </RowContainer>
        )}
      </AlertColumnContainer>
    );
  }
};

interface AlertProps {
  $isTriggered: boolean;
}
interface AllowEditProps {
  $isEdit: boolean;
}

const AlertLeftOuterDiv = styled.div<AlertProps>`
  width: ${props => (props.$isTriggered ? '35%' : '40%')};
  display: flex;
  flex-direction: column;
`;

const AlertRightOuterDiv = styled.div`
  display: flex;
  flex-direction: column;
`;

const AlertLeftInnerDiv = styled.div`
  display: flex;
  align-items: center;
  margin-top: 4px;
`;

const AlertLeftStatusDiv = styled.div<AlertProps>`
  color: ${props => (props.$isTriggered ? 'gray' : props.theme.colors.statistic.positive)};
  margin-left: 3px;
`;

const AlertDiv = styled.div`
  display: flex;
  margin-bottom: 3px;
  align-items: center;
`;

const Arrow = styled(Up)`
  font-size: 12px;
  margin-right: 0.4em;
  margin-bottom: -1px;
`;

const UpArrow = styled(Arrow)`
  fill: ${props => props.theme.colors.statistic.positive};
`;

const AlertIcon = styled(AlarmSmall)`
  width: 1.5em;
  height: 1.5em;
`;

const TriggeredIcon = styled(CheckmarkAlert)`
  width: 1.5em;
  height: 1.5em;
  fill: ${props => props.theme.colors.statistic.positive};
`;

const ActiveAlertIcon = styled(AlertIcon)`
  fill: ${props => props.theme.colors.statistic.positive};
`;

const DownArrow = styled(Arrow)`
  transform: rotate(180deg);
  fill: ${props => props.theme.colors.statistic.negative};
`;

const RowContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
`;

const RowEditEndContainer = styled.div`
  display: flex;
  flex-direction: row;
  margin-right: 6px;
  align-items: center;
`;

const ColumnContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const AlertDateTime = styled.div`
  font-weight: bold;
  margin-bottom: 4px;
  margin-top: 9px;
  font-size: 10px;
`;

const AlertText = styled.div`
  white-space: normal;
  margin-right: 4px;
`;

const AlertColumnContainer = styled.div`
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  justify-content: space-between;
`;

const AlertColumnEditContainer = styled.div`
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  margin-top: 10px;
`;

const AlertsHeader = styled.div`
  background-color: ${props => props.theme.colors.backgroundActive};
  padding: 10px;
`;

const Line = styled.hr`
  margin: 8px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const AlertBody = styled.div<AllowEditProps>`
  background-color: ${props => props.theme.colors.background};
  display: flex;
  flex-direction: column;
  max-height: 500px;
  overflow: auto;
  margin-left: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: ${props => (!props.$isEdit ? '15px' : '4px')};
  width: ${props => (!props.$isEdit ? '300px' : '350px')};
`;

const AlertsAdd = styled.div`
  background-color: ${props => props.theme.colors.background};
  margin-top: 2px;
`;

const AlertAddBorder = styled.div`
  border-bottom-width: 1px;
  border-color: ${props => props.theme.colors.border};
  border-style: solid;
  margin-bottom: 5px;
  padding: 5px;
`;

const AlertIcons = styled.div`
  display: flex;
  flex-direction: row;
  margin-bottom: 4px;
  margin-left: 4px;
  margin-right: 4px;
  margin-top: 4px;
`;

const MarginedIcon = styled.div`
  cursor: pointer;
  margin-left: 4px;
  margin-right: 4px;
`;

const AlertsView = styled.div`
  background-color: ${props => props.theme.colors.background};
  border-color: ${props => props.theme.colors.border};
  border-style: solid;
  border-width: 2px;
`;

const AddButton = styled.div`
  background-color: ${props => props.theme.colors.brandMuted};
  color: ${props => props.theme.colors.foregroundActive};
  cursor: pointer;
  margin-bottom: 3px;
  margin-left: 4px;
  padding-bottom: 6px;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 6px;
  :hover {
    background-color: ${props => props.theme.colors.brand};
  }
`;

const AddButtonContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
`;

const CancelButton = styled.div`
  background-color: ${props => props.theme.colors.brandMuted};
  color: ${props => props.theme.colors.foregroundActive};
  cursor: pointer;
  padding: 5px;
  margin-left: 3px;
  :hover {
    background-color: ${props => props.theme.colors.brand};
  }
`;

const UpdateButton = styled.div`
  background-color: ${props => props.theme.colors.brandMuted};
  color: ${props => props.theme.colors.foregroundActive};
  cursor: pointer;
  margin-left: 4px;
  padding: 5px;
  :hover {
    background-color: ${props => props.theme.colors.brand};
  }
`;

const PointerCursor = styled.div`
  cursor: pointer;
`;

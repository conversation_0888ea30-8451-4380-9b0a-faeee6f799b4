'use client';
import { IDoesFilterPassParams, IFilterParams } from '@ag-grid-community/core';
import React, { forwardRef, Ref, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import TimeRange, { DEFAULT_TIME_FROM } from './TimeRange';
import { TimeRangeInterface } from './TimeRange';
import { debounce } from '@benzinga/utils';
import { getTimeNumericValue } from '@benzinga/utils';

const INITIAL_RANGES = [{}];
const APPLY_FILTER_DEBOUNCE_TIMEOUT = 1000;

export const TimeFilter = forwardRef((props: IFilterParams, ref: Ref<unknown>) => {
  const column = props.column as unknown as string;
  const [ranges, setRanges] = useState<TimeRangeInterface[]>(INITIAL_RANGES);

  const applyFilter = React.useMemo(
    () =>
      debounce(() => {
        const filterChangedCallback = props.filterChangedCallback;
        filterChangedCallback();
      }, APPLY_FILTER_DEBOUNCE_TIMEOUT),
    [props.filterChangedCallback],
  );

  useEffect(() => {
    applyFilter();
  }, [ranges, applyFilter]);

  useImperativeHandle(ref, () => {
    return {
      doesFilterPass(params: IDoesFilterPassParams) {
        if (!params.data?.[column]) {
          return false;
        }
        const value = getTimeNumericValue(params.data[column]);
        return ranges.some(({ isExactTime, timeFrom, timeTo }) =>
          isExactTime
            ? value === getTimeNumericValue(timeFrom || DEFAULT_TIME_FROM)
            : (!timeFrom || value >= getTimeNumericValue(timeFrom)) &&
              (!timeTo || value <= getTimeNumericValue(timeTo)),
        );
      },

      getModel() {
        return ranges.filter(({ isExactTime, timeFrom, timeTo }) => timeFrom || (timeTo && !isExactTime));
      },

      isFilterActive() {
        return (
          !!ranges?.length && ranges.some(({ isExactTime, timeFrom, timeTo }) => timeFrom || (timeTo && !isExactTime))
        );
      },

      setModel(ranges: TimeRangeInterface[]) {
        setRanges(ranges?.length ? ranges : INITIAL_RANGES);
      },
    };
  });

  const handleRangeChange = useCallback(
    (index: number, timeFrom?: string, timeTo?: string, isExactTime = false, remove = false) => {
      const newRanges = [...ranges];
      if (remove) {
        newRanges.splice(index, 1);
      } else {
        newRanges[index] = { isExactTime, timeFrom, timeTo };
      }

      setRanges(newRanges);
    },
    [ranges, setRanges],
  );

  const addRange = useCallback(() => {
    setRanges([...ranges, {}]);
  }, [ranges, setRanges]);

  return (
    <>
      {ranges.map(({ isExactTime, timeFrom, timeTo }, index) => (
        <TimeRange
          addRange={addRange}
          index={index}
          isExactTime={isExactTime}
          key={index}
          modifyRange={handleRangeChange}
          ranges={ranges}
          timeFrom={timeFrom}
          timeTo={timeTo}
        />
      ))}
    </>
  );
});

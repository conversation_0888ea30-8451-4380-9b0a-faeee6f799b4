import { CheckV2, CloseV2 } from '@benzinga/valentyn-icons';
import styled from 'styled-components';

interface Props {
  value: boolean;
  small?: boolean;
}

export const PositiveNegativeIndicator: React.FC<Props> = ({ small, value }) => {
  return (
    <Block positive={value} small={small}>
      {value ? <CheckV2 /> : <CloseV2 />}
    </Block>
  );
};

const Block = styled.div<{ positive?: boolean; small?: boolean }>`
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  height: 20px;
  max-width: 46px;
  width: ${({ small }) => (small ? '20px' : '100%')};
  background: ${({ positive, theme }) =>
    positive ? theme.colors.statistic.positive : theme.colorPalette.bzGreyNormal};

  svg {
    stroke: ${({ positive, theme }) =>
      positive
        ? theme.colors.blindMode === 'green'
          ? theme.colorPalette.bzGreyDark
          : theme.colorPalette.bzWhite
        : theme.colors.blindMode === 'green'
          ? theme.colors.foreground
          : theme.colorPalette.bzWhite};
  }
`;

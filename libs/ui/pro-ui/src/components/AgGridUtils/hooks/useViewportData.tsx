import { BodyScrollEndEvent, GridApi, IRowNode } from '@ag-grid-community/core';
import { startTransition, useRef } from 'react';

interface VisibleData {
  symbols: string[];
  fields: string[];
  removedSymbols?: string[];
}

interface useViewportData {
  getVisibleData: (gridApi: GridApi | null) => VisibleData;
  onTransactionApplied: (gridApi: GridApi | null, callback: () => void) => void;
  filterVisibleSymbols: (gridApi: GridApi | null, symbols: string[]) => string[];
  initOnScrollEnd: (
    gridApi: GridApi | null,
    callback: (visibleData: VisibleData, event: BodyScrollEndEvent) => void,
  ) => void;
}

export const useViewportData = (): useViewportData => {
  const scrollListener = useRef<(() => void) | null>(null);
  const previouslyRenderedNodes = useRef<IRowNode[]>([]);

  const getTickerSlug = row => {
    if (!row) return null;
    return row.id ?? row.symbol ?? row.data?.symbol ?? row.data?.ticker ?? null;
  };

  const getVisibleData = (gridApi: GridApi | null): VisibleData => {
    const visibleRows = gridApi?.getRenderedNodes().map(node => node.data);
    const visibleColumns = gridApi?.getAllDisplayedVirtualColumns();
    const symbols = visibleRows?.map(getTickerSlug)?.filter(Boolean) ?? [];
    const fields = (visibleColumns?.map(col => col.getColDef().colId)?.filter(Boolean) as string[]) ?? [];

    return {
      fields,
      symbols,
    };
  };

  const setCurrentRenderedNodes = (nodes?: IRowNode[]): void => {
    previouslyRenderedNodes.current = nodes ?? [];
  };

  const getRemovedSymbols = (gridApi: GridApi | null): string[] => {
    const currentlyRenderedNodes = gridApi?.getRenderedNodes();
    const currentTickers = currentlyRenderedNodes?.map(node => node.data.ticker) ?? [];

    previouslyRenderedNodes.current = previouslyRenderedNodes?.current?.filter(
      node => !currentTickers.includes(node?.data?.ticker),
    );

    if (previouslyRenderedNodes?.current?.length) {
      const previousTickers = previouslyRenderedNodes.current.map(node => node.data.ticker);
      setCurrentRenderedNodes(currentlyRenderedNodes);

      return previousTickers;
    }

    setCurrentRenderedNodes(currentlyRenderedNodes);
    return [];
  };

  const initOnScrollEnd = (
    gridApi: GridApi | null,
    callback: (visibleData: VisibleData, event: BodyScrollEndEvent) => void,
  ) => {
    setCurrentRenderedNodes(gridApi?.getRenderedNodes());
    if (!scrollListener?.current) {
      scrollListener.current =
        gridApi?.addEventListener('bodyScrollEnd', (event: BodyScrollEndEvent) => {
          callback(
            {
              ...getVisibleData(gridApi),
              removedSymbols: event?.direction === 'vertical' ? getRemovedSymbols(gridApi) : [],
            },
            event,
          );
        }) ?? null;
    }
  };

  const onTransactionApplied = (gridApi: GridApi | null, callback: () => void) => {
    if (!gridApi || !callback) return;

    const onTransactionAppliedCallback = () => {
      startTransition(callback);
      gridApi.removeEventListener('asyncTransactionsFlushed', onTransactionAppliedCallback);
    };

    gridApi.addEventListener('asyncTransactionsFlushed', onTransactionAppliedCallback);
  };

  const filterVisibleSymbols = (gridApi: GridApi | null, symbols: string[]): string[] => {
    const { symbols: visibleSymbols } = getVisibleData(gridApi);

    return symbols?.filter(symbol => visibleSymbols.includes(symbol));
  };

  return {
    filterVisibleSymbols,
    getVisibleData,
    initOnScrollEnd,
    onTransactionApplied,
  };
};

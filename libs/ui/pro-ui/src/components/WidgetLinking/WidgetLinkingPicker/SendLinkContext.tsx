'use client';
import { Modal } from '@benzinga/core-ui';
import styled, { TC } from '@benzinga/themetron';
import { WidgetToolbarContext } from '@benzinga/widget-tools';
import { WidgetLink, WidgetLinkingID, WidgetLinkingManager } from '@benzinga/widget-linking';
import React from 'react';
import { openWindow } from '@benzinga/frontend-utils';
import { linkingContextOption } from '../../ContextMenu/Options/linkingContextOption';
import { SessionContext } from '@benzinga/session-context';
import Hooks from '@benzinga/hooks';
import { StockSymbol } from '@benzinga/session';

interface Props {
  displayModal?: boolean;
  linkId?: WidgetLinkingID | null;
  setLink?: (link: WidgetLinkingID | null, extendExpression: boolean) => void;
}

export const SendLinkContext = React.createContext<{
  linkId: WidgetLinkingID;
  onSymbolClick: (symbol: StockSymbol) => void;
}>({ linkId: null, onSymbolClick: () => undefined });

export const SendLinkContextProvider: React.FC<React.PropsWithChildren<Props>> = props => {
  const session = React.useContext(SessionContext);
  const widgetToolbar = React.useContext(WidgetToolbarContext);
  const [displayModal, setDisplayModal] = React.useState<boolean>(false);
  const linkingManager = session.getManager(WidgetLinkingManager);

  const [widgetLinks, setWidgetLinks] = React.useState(() => [
    ...linkingManager.getWidgetLinks(),
    { ...linkingManager.getDefaultLinkFeed().getLink(), title: 'Clear Link Selection' },
  ]);

  Hooks.useSubscriber(linkingManager, e => {
    switch (e.type) {
      case 'linking:link_updated': {
        setWidgetLinks([
          ...linkingManager.getWidgetLinks(),
          { ...linkingManager.getDefaultLinkFeed().getLink(), title: 'Clear Link Selection' },
        ]);
        break;
      }
    }
  });

  const linkIdRef = React.useRef<WidgetLinkingID | null>(null);

  const setLinkNo = React.useCallback(() => {
    setDisplayModal(false);
    const setLink = props.setLink;
    setLink?.(linkIdRef.current, false);
  }, [props.setLink]);

  const setLinkYes = React.useCallback(() => {
    setDisplayModal(false);
    const setLink = props.setLink;
    setLink?.(linkIdRef.current, true);
  }, [props.setLink]);

  React.useEffect(() => {
    const defineControls = widgetToolbar.defineControls;
    const links: WidgetLink[] = widgetLinks;
    defineControls({
      key: 'linking',
      submenuNode: linkingContextOption(links, linkId => {
        linkIdRef.current = linkId;
        if (props.displayModal) {
          setDisplayModal(props.displayModal);
        } else {
          setLinkNo();
        }
      }),
    });
  }, [widgetLinks, widgetToolbar.defineControls, props.displayModal, linkingManager, setLinkNo]);

  React.useEffect(() => {
    const setToolbarColor = widgetToolbar.setToolbarColor;
    const color = linkingManager.getWidgetLinkFeedByID(props.linkId ?? null)?.getLink()?.hue;
    setToolbarColor(color ?? null);
  }, [linkingManager, props.linkId, widgetToolbar.setToolbarColor]);

  Hooks.useSubscriber(linkingManager, e => {
    switch (e.type) {
      case 'linking:link_updated':
        if (e.link.id === (props.linkId ?? null)) {
          widgetToolbar.setToolbarColor(e.link.hue ?? null);
        }
        break;
      default:
        break;
    }
  });

  const manual = React.useMemo(() => ({ manual: { width: '400px' } }), []);

  const onHelpClick = React.useCallback(
    () =>
      openWindow('https://pro.benzinga.help/en/articles/6792965-tool-linking', undefined, undefined, undefined, {
        free: true,
      }),
    [],
  );

  const onSymbolClick = React.useCallback(
    (symbol: StockSymbol) => {
      session
        .getManager(WidgetLinkingManager)
        .getWidgetLinkFeedByID(props.linkId ?? null)
        ?.pushEvent({
          symbol: symbol,
          type: 'ticker_selected',
        });
    },
    [props.linkId, session],
  );

  const value = React.useMemo(
    () => ({
      linkId: props.linkId ?? null,
      onSymbolClick,
    }),
    [onSymbolClick, props.linkId],
  );

  return (
    <SendLinkContext.Provider value={value}>
      {displayModal && (
        <Modal
          fullScreen={true}
          onClose={setLinkNo}
          size={manual}
          title="Enhance The Link Functionality?"
          zIndex={2000}
        >
          <ModalLinkContent>
            <ModalLinkQuestion>
              Would you like to Add the Link to the {widgetToolbar.definition.title} search bar?
            </ModalLinkQuestion>
            <ModalLinkRow>
              <ModalLinkButton onClick={onHelpClick}>Help</ModalLinkButton>
              <ModalLinkButton onClick={setLinkNo}>No</ModalLinkButton>
              <ModalLinkButton onClick={setLinkYes}>Yes</ModalLinkButton>
            </ModalLinkRow>
          </ModalLinkContent>
        </Modal>
      )}
      {props.children}
    </SendLinkContext.Provider>
  );
};

const ModalLinkButton = styled(TC.Button)`
  margin-left: 1px;
`;

const ModalLinkRow = styled(TC.Row)`
  justify-content: flex-end;
`;

const ModalLinkQuestion = styled.div`
  margin-bottom: 6px;
`;

const ModalLinkContent = styled.div`
  margin: 10px;
`;

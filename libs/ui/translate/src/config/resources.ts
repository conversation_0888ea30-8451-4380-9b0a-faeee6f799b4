import common_en from '../locales/en/common.json';
import auth_en from '../locales/en/auth.json';
import welcome_en from '../locales/en/welcome.json';

export const LOCALES = {
  EN: 'en',
  ES: 'es',
  FR: 'fr',
  IT: 'it',
  JA: 'ja',
  KO: 'ko',
} as const;

type LocaleKeys = keyof typeof LOCALES;
export type LocaleType = (typeof LOCALES)[LocaleKeys];

export const TRANSLATION_RESOURCES = {
  [LOCALES.EN]: {
    auth: auth_en,
    common: common_en,
    welcome: welcome_en,
  },
  [LOCALES.ES]: {},
  [LOCALES.FR]: {},
  [LOCALES.IT]: {},
  [LOCALES.KO]: {},
  [LOCALES.JA]: {},
};

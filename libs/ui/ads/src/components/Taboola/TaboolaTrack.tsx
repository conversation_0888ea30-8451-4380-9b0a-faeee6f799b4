import { useEffectDidMount } from '@benzinga/hooks';
import { startTransition } from 'react';
import { loadTaboolaScript } from './utils';

export const TaboolaTrack: React.FC = () => {
  const loadHead = () => {
    window['_taboola'] = window['_taboola'] || [];
    window['_taboola'].push({
      article: 'auto',
    });

    loadTaboolaScript();

    if (window.performance && typeof window.performance.mark == 'function') {
      window.performance.mark('tbl_ic');
    }
  };
  const loadBody = () => {
    window['_taboola'] = window['_taboola'] || [];
    window['_taboola'].push({
      container: 'taboola-newsroom',
      mode: 'rbox-tracking',
      placement: 'Newsroom',
    });
  };

  const loadEndBody = () => {
    window['_taboola'] = window['_taboola'] || [];
    window['_taboola'].push({ flush: true });
  };

  useEffectDidMount(() => {
    startTransition(() => {
      loadHead();
      loadBody();
      loadEndBody();
    });
  });

  return <div id="taboola-newsroom" />;
};

import { runningClientSide } from '@benzinga/utils';

export const DEFAULT_TABOOLA_SCRIPT_KEY = 'benzinga-benzinga1';

export const loadTaboolaScript = (key = DEFAULT_TABOOLA_SCRIPT_KEY, callback?: () => void): void => {
  if (!runningClientSide()) {
    return;
  }

  const SCRIPT_ID = 'taboola-loader';

  if (document.getElementById(SCRIPT_ID)) {
    callback && callback();
    return;
  }

  const script = document.createElement('script');
  script.id = SCRIPT_ID;
  script.src = `//cdn.taboola.com/libtrc/${key || DEFAULT_TABOOLA_SCRIPT_KEY}/loader.js`;
  script.async = true;

  const firstScript = document.getElementsByTagName('script')[0];
  firstScript.parentNode?.insertBefore(script, firstScript);

  callback && callback();
};

'use client';
import React, { useEffect, useRef, useCallback } from 'react';
import { WidgetType } from './widget';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useUser } from '@benzinga/user-context';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
import { isMobile } from '@benzinga/device-utils';
import styles from './floatingWidget.module.scss';

declare global {
  interface Window {
    hj?: () => void;
  }
}

interface FloatingWNSTNWidgetProps {
  widgetType?: WidgetType;
  questions?: string[] | null;
  articleID?: number;
  symbol?: string;
  isCrypto?: boolean;
  assetType?: 'CRYPTO' | 'ETF' | 'FX' | 'INDEX' | 'STOCK';
}

interface ChatParams {
  language: string;
  proxy: string;
  userId: string;
  article_index?: string;
  follow_up_questions?: string;
  assetType?: string;
  assetSymbol?: string;
}

export const FloatingWNSTNWidget: React.FC<FloatingWNSTNWidgetProps> = ({
  articleID,
  assetType,
  questions: initialQuestions,
  symbol,
  widgetType = WidgetType.FollowUp,
}) => {
  const impressed = useRef<boolean>(false);
  const floatingWidgetContainerRef = useRef<HTMLDivElement>(null);

  const session = React.useContext(SessionContext);
  const user = useUser();

  const checkForFooterAndAdjustPosition = useCallback(() => {
    if (!floatingWidgetContainerRef.current) return;

    const connatixElement = document.querySelector('.cnx-float-position-bottom');
    if (connatixElement && isMobile()) {
      floatingWidgetContainerRef.current.style.display = 'none';
      return;
    } else if (floatingWidgetContainerRef.current.style.display === 'none' && !connatixElement) {
      floatingWidgetContainerRef.current.style.display = '';
    }

    const widgetContainer = floatingWidgetContainerRef.current.querySelector(
      '.my-chat-container-container__widget',
    ) as HTMLElement;

    let isWidgetExpanded = false;
    if (isMobile() && widgetContainer) {
      const rect = widgetContainer.getBoundingClientRect();
      const computedStyle = getComputedStyle(widgetContainer);

      isWidgetExpanded =
        computedStyle.height === '100dvh' || computedStyle.height === '100vh' || rect.height === window.innerHeight;
    }

    if (isMobile() && isWidgetExpanded) {
      floatingWidgetContainerRef.current.style.bottom = '0px';
      return;
    }

    const mobileFooter = document.getElementById('AdThrive_Footer_1_phone');
    const activeFooter = mobileFooter;

    if (activeFooter && floatingWidgetContainerRef.current) {
      const footerRect = activeFooter.getBoundingClientRect();
      const footerHeight = footerRect.height;
      const isFooterVisible = footerRect.top < window.innerHeight && footerRect.bottom > 0;
      const isFooterSticky = activeFooter.classList.contains('adthrive-sticky');

      if (isFooterVisible && isFooterSticky) {
        const newOffset = footerHeight + 10;
        floatingWidgetContainerRef.current.style.bottom = `${newOffset}px`;
      }
    } else if (floatingWidgetContainerRef.current) {
      floatingWidgetContainerRef.current.style.bottom = '0px';
    }
  }, []);

  const handleImpress = useCallback(() => {
    if (!impressed.current && floatingWidgetContainerRef?.current?.style.display !== 'none') {
      impressed.current = true;
      session.getManager(TrackingManager).trackWnstnEvent('wnstn_floating_widget_view', {
        page_link: window.location.href,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchQuestions = useCallback(async (articleId: number | string): Promise<string[]> => {
    if (!articleId) {
      return [];
    }

    try {
      const response = await fetch(`/api/wnstn-followups?articleId=${articleId}`);

      if (!response.ok) {
        console.error('WNSTN followup question API request failed:', response.status, response.statusText);
        return [];
      }

      const data = await response.json();

      if (data.status === 'success' && data.follow_up_questions) {
        return data.follow_up_questions;
      } else {
        console.error('WNSTN followup question API returned unsuccessful status or no questions:', data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching WNSTN followup questions:', error);
      return [];
    }
  }, []);

  const loadWidget = useCallback(async () => {
    const accessType = user?.accessType === 'subscribed' || user?.accessType === 'trialing' ? 'pro' : 'www';
    const chatParams: ChatParams = {
      language: 'EN',
      proxy: `https://wnstn-api.benzinga.com/${accessType}`,
      userId: String(user?.id || ''),
    };

    if (widgetType === WidgetType.FollowUp && articleID) {
      chatParams.article_index = String(articleID);

      let questions = initialQuestions;
      if (!questions || questions.length === 0) {
        questions = await fetchQuestions(articleID);
      }

      if (questions && questions.length > 0) {
        chatParams.follow_up_questions = questions.join(';');
      }
    }

    if (widgetType === WidgetType.Asset) {
      if (assetType) {
        chatParams.assetType = assetType;
      }
      if (symbol) {
        chatParams.assetSymbol = symbol;
      }
    }

    window['chatInitParams'] = { ...chatParams };

    const widgetContainer = floatingWidgetContainerRef?.current?.querySelector('.my-chat-container-container__widget');
    if (widgetContainer) {
      widgetContainer.innerHTML = '';
      const script = document.createElement('script');
      script.id = 'wnstn-widget-script';
      script.src = 'https://staticfiles.wnstn.ai/jsfiles/createFloatingWidget.js';
      script.defer = true;
      script.onload = () => {
        const wnstnIframe = widgetContainer.querySelector('iframe');
        if (wnstnIframe) {
          handleImpress();
          wnstnIframe.onerror = error => {
            console.error('Floating WNSTN Widget Error:', error);
            wnstnIframe.remove();
            floatingWidgetContainerRef?.current?.remove();
          };
        }
        setTimeout(checkForFooterAndAdjustPosition, 100);
      };

      const existingScript = document.getElementById('wnstn-widget-script');
      if (existingScript) {
        existingScript.remove();
      }

      widgetContainer.appendChild(script);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleTrackWidgetHover = useCallback(() => {
    if (isGlobalImpressionStored('FLOATING_WNSTN')) return;
    try {
      const trackingManager = session?.getManager(TrackingManager);
      if (trackingManager) {
        storeGlobalImpression('FLOATING_WNSTN');
        trackingManager.trackWnstnEvent('wnstn_floating_widget_hover', {
          node_id: articleID ? String(articleID) : undefined,
          page_link: window.location.href,
          symbol,
        });
      }
    } catch (error) {
      console.error('Error tracking floating WNSTN widget hover:', error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const hotjarWidgetMutationObserver = new MutationObserver((_mutations, observer) => {
      const widget = document.querySelector('._hj-widget-container');
      if (widget) {
        observer.disconnect();
      }
      if (widget && floatingWidgetContainerRef.current) {
        floatingWidgetContainerRef.current.style.display = 'none';
      }
    });

    hotjarWidgetMutationObserver.observe(document.body, { childList: true, subtree: true });
  }, []);

  useEffect(() => {
    loadWidget();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    checkForFooterAndAdjustPosition();

    const footerMutationObserver = new MutationObserver(() => {
      checkForFooterAndAdjustPosition();
    });

    footerMutationObserver.observe(document.body, {
      attributeFilter: ['class', 'style'],
      attributes: true,
      childList: true,
      subtree: true,
    });

    const handleScrollResize = () => {
      checkForFooterAndAdjustPosition();
    };

    window.addEventListener('scroll', handleScrollResize, { passive: true });
    window.addEventListener('resize', handleScrollResize, { passive: true });

    return () => {
      footerMutationObserver.disconnect();
      window.removeEventListener('scroll', handleScrollResize);
      window.removeEventListener('resize', handleScrollResize);
    };
  }, [checkForFooterAndAdjustPosition]);

  useEffect(() => {
    let resizeObserver: ResizeObserver;
    let mutationObserver: MutationObserver;

    const setupObservers = () => {
      const widgetContainer = floatingWidgetContainerRef.current?.querySelector(
        '.my-chat-container-container__widget',
      ) as HTMLElement;

      if (!widgetContainer) return;

      resizeObserver = new ResizeObserver(() => {
        setTimeout(() => {
          checkForFooterAndAdjustPosition();
        }, 100);
      });

      resizeObserver.observe(widgetContainer);

      mutationObserver = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            setTimeout(() => {
              checkForFooterAndAdjustPosition();
            }, 50);
          }
        });
      });

      mutationObserver.observe(widgetContainer, {
        attributeFilter: ['style'],
        attributes: true,
      });
    };

    const widgetContainer = floatingWidgetContainerRef.current?.querySelector('.my-chat-container-container__widget');
    if (widgetContainer) {
      setupObservers();
    }

    return () => {
      if (resizeObserver) resizeObserver.disconnect();
      if (mutationObserver) mutationObserver.disconnect();
    };
  }, [checkForFooterAndAdjustPosition]);

  return (
    <div className={styles.FloatingWidgetContainer} ref={floatingWidgetContainerRef}>
      <div className="my-chat-container">
        <div className="my-chat-container-container__widget" onMouseOver={handleTrackWidgetHover}></div>
      </div>
    </div>
  );
};

export default FloatingWNSTNWidget;

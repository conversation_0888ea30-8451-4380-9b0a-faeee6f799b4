import { FC, memo, useState, useCallback, useEffect } from 'react';
import { SophiPaywall } from './SophiIteration/SophiPaywall';
import { AuthIterationType } from './types';
import { isMobile } from '@benzinga/device-utils';

import { sophiPaywallCopyMap } from './SophiIteration';
const localStorageKey = 'pwdata';

export const SophiIterations: FC<AuthIterationType> = memo(({ authMode, nextUrl, placement, setShowPaywall }) => {
  // contentType, allowClose is passed from Sophi decision, to be added back in props deconstruction
  const showMobile = isMobile();
  const [versionIndex, setVersionIndex] = useState<number | null>(null);

  const closePaywall = (show: boolean) => {
    if (setShowPaywall) {
      setShowPaywall(show);
    } else {
      // dev testing
      alert('Paywall close function not provided');
    }
  };

  let userVisitInfo = { count: 1, week: 0 };
  try {
    const localVisitInfo = window?.localStorage?.getItem(localStorageKey);
    if (localVisitInfo) {
      userVisitInfo = JSON.parse(localVisitInfo);
    }
  } catch (e) {
    // console.error(e);
  }

  // set type based on user visit count
  // count < 5 - cold paywall
  // count >= 10 - warm paywall
  // count < 10 - hot paywall
  const paywalls = Object.keys(sophiPaywallCopyMap).filter(id => {
    if (userVisitInfo.count < 5) {
      return id.startsWith('ct-pw');
    } else if (userVisitInfo.count < 10) {
      return id.startsWith('wt-pw');
    }
    return id.startsWith('ht-pw');
  });

  const getVersionIndex = useCallback((versionsCount: number) => {
    let versionIndex = Math.floor(Math.random() * versionsCount);
    versionIndex = versionIndex >= versionsCount || versionIndex < 0 ? 0 : versionIndex;
    return versionIndex;
  }, []);

  useEffect(() => {
    const idx = getVersionIndex(paywalls.length);
    setVersionIndex(idx);
  }, [getVersionIndex, paywalls.length]);

  return versionIndex !== null ? (
    <SophiPaywall
      allowClose={false}
      authMode={authMode}
      isMobile={showMobile}
      nextUrl={nextUrl}
      paywallId={paywalls[versionIndex]}
      // paywallId={contentType} // passed from Sophi decision
      placement={placement}
      setShowPaywall={closePaywall}
    />
  ) : null;
});

SophiIterations.displayName = 'SophiIterations';

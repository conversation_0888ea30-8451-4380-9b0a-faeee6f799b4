import { useContext, useEffect } from 'react';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { getPaywallUTMParams, getPaywallType } from '../utils/paywallUtils';

interface UsePaywallTrackingProps {
  utmSource: string;
  allowClose?: boolean;
  placement?: string;
  checkoutUrl?: string;
}

export const usePaywallTracking = ({ allowClose, checkoutUrl, placement, utmSource }: UsePaywallTrackingProps) => {
  const session = useContext(SessionContext);

  useEffect(() => {
    const utmParams = getPaywallUTMParams(checkoutUrl);
    const paywallType = getPaywallType(allowClose);

    session.getManager(TrackingManager).trackPaywallEvent('view', {
      paywall_id: utmSource,
      paywall_type: paywallType,
      placement: placement,
      utmParams,
    });
  }, [allowClose, session, placement, utmSource, checkoutUrl]);

  return null;
};

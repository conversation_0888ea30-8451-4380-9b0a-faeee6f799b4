import { WordpressPage } from '@benzinga/content-manager';
import React from 'react';
import { FloatingPopup } from './FloatingPopup';
import { CampaignPopup } from './CampaignPopup';
import { Popup } from '../Popup';
import { useBenzingaEdge } from '@benzinga/edge';
import { appEnvironment, appName } from '@benzinga/utils';

interface Props {
  post?: WordpressPage | null;
}

export const PageCampaigns: React.FC<Props> = ({ post }) => {
  const hasAdLight = useBenzingaEdge().adLightEnabled;
  const isMoneyApp = appEnvironment().isApp(appName.money);

  return (
    <>
      {post?.campaigns && <FloatingPopup targets={post?.campaigns} />}
      {post?.campaigns && (isMoneyApp || !hasAdLight) && <CampaignPopup targets={post?.campaigns} />}
      {post?.unmonetized_interstitial && <Popup interstitial={true} popup={post.unmonetized_interstitial} />}
    </>
  );
};

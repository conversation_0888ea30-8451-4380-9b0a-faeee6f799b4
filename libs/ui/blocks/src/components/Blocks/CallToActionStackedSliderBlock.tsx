import React from 'react';
import {
  ProductBasedCTA,
  ProductBasedCTAProps,
  ProductBasedCTALayout,
  StackedCards,
  StackedCardHeader,
} from '@benzinga/ui';
import { extractImageSize } from '@benzinga/image';
import styled from '@benzinga/themetron';
import { Carousel } from '@benzinga/ui';
import Hooks from '@benzinga/hooks';
import { SplideStyle } from '@benzinga/ui';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { appEnvironment, appName } from '@benzinga/utils';

export interface CallToActionStackedSliderBlockProps {
  display: ProductBasedCTALayout;
  groups?: ProductBasedCTAProps[];
  mobile_items?: number;
  social_proofing?: string;
  title?: string;
}

export const CallToActionStackedSliderBlock: React.FC<CallToActionStackedSliderBlockProps> = ({
  display,
  groups,
  mobile_items = 1,
  social_proofing,
  title,
}) => {
  const isBzApp = appEnvironment().isApp(appName.bz);
  const defaultPerPage = isBzApp ? 2 : 3;
  const ref = React.useRef<HTMLDivElement>(null);
  const { width } = Hooks.useRefElementSize(ref);
  const perPage = width <= 500 ? mobile_items : width <= 720 ? 2 : defaultPerPage;

  if (groups && groups?.length === 0) return;

  return (
    <CallToActionCarouselWrapper ref={ref}>
      {display !== 'aol' && <StackedCardHeader heading={title} social_proofing={social_proofing} />}

      {(() => {
        if (display === 'vertical') {
          return (
            <div className={`cta-stacked ${SplideStyle.splide_slidewrap}`}>
              <Carousel
                component={campaign => {
                  const image = extractImageSize(campaign?.image);
                  return (
                    <ProductBasedCTA
                      button_text={campaign.button_text}
                      description={campaign.description}
                      highlight_list={campaign?.highlight_list ?? []}
                      image_url={image?.url}
                      layout={display}
                      review={campaign.review}
                      review_url={campaign.review_url}
                      title={campaign.title}
                      url={campaign.url}
                    />
                  );
                }}
                items={groups as any[]}
                options={{
                  autoWidth: false,
                  breakpoints: {
                    750: {
                      perPage: 1,
                    },
                    900: {
                      perPage: 2,
                    },
                  },
                  focus: groups && groups.length > 2 ? 'center' : 0,
                  perPage,
                }}
                showArrowsOnlyOnOverflow={true}
                showPagination={true}
              />
            </div>
          );
        } else if (display === 'aol') {
          return <StackedCards groups={groups} heading={title} layout={display} social_proofing={social_proofing} />;
        } else {
          return (
            <div className={`flex flex-col md:gap-3 gap-0`}>
              {groups &&
                groups.map((campaign, index) => {
                  const image = extractImageSize(campaign?.image);
                  return (
                    <div className={`basis-full`} key={index}>
                      <ProductBasedCTA
                        button_text={campaign.button_text}
                        description={campaign.description}
                        highlight_list={campaign?.highlight_list ?? []}
                        image_url={image?.url}
                        layout={display}
                        review={campaign?.review ?? 0}
                        review_url={campaign?.review_url ?? ''}
                        title={campaign.title}
                        url={campaign.url}
                      />
                    </div>
                  );
                })}
            </div>
          );
        }
      })()}
    </CallToActionCarouselWrapper>
  );
};

const CallToActionCarouselWrapper = styled.div`
  h3 {
    font-size: 1.3rem;
  }
  .call-to-action-wrapper {
    .cta-button {
      button {
        font-size: 1rem;
        padding: 0.75rem 0.65rem;
      }
    }
  }
`;

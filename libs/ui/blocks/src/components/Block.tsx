import React from 'react';

import { sanitizeHTML } from '@benzinga/frontend-utils';
import { NoFirstRender, ImportOnVisibility } from '@benzinga/hooks';
import { createHTMLElement, CoreElementWrapper } from './CoreElement';
import type { YouTubeVideoWordpress } from '@benzinga/videos-manager';
import { appEnvironment, appName } from '@benzinga/utils';

const Grid = React.lazy(() => import('./Grid').then(module => ({ default: module.GridBlockGroup })));

const ActionButtonBlock = React.lazy(() =>
  import('./Blocks/ActionButtonBlock').then(module => ({ default: module.ActionButtonBlock })),
);
const AdvertisingCardBlock = React.lazy(() =>
  import('./Blocks/AdvertisingCardBlock').then(module => ({ default: module.AdvertisingCardBlock })),
);
const BenzingaBriefsBlock = React.lazy(() =>
  import('./Blocks/BenzingaBriefsBlock').then(module => ({ default: module.BenzingaBriefsBlock })),
);
const BenzingaLassoBlock = React.lazy(() =>
  import('./Blocks/BenzingaLassoBlock').then(module => ({ default: module.BenzingaLassoBlock })),
);
const BenzingaStaticsBlock = React.lazy(() =>
  import('./Blocks/BenzingaStaticsBlock').then(module => ({ default: module.BenzingaStaticsBlock })),
);
const CalendarsWidgetBlock = React.lazy(() =>
  import('./Blocks/CalendarsWidgetBlock').then(module => ({ default: module.CalendarsWidgetBlock })),
);
const CallToActionBlock = React.lazy(() =>
  import('./Blocks/CallToActionBlock').then(module => ({ default: module.CallToActionBlock })),
);
const CallToActionStackedSliderBlock = React.lazy(() =>
  import('./Blocks/CallToActionStackedSliderBlock').then(module => ({
    default: module.CallToActionStackedSliderBlock,
  })),
);
const CallToAction = React.lazy(() =>
  import('./Blocks/CallToActionBlock').then(module => ({ default: module.CallToActionElement })),
);
const CarouselBlock = React.lazy(() =>
  import('./Blocks/CarouselBlock').then(module => ({ default: module.CarouselBlock })),
);
const CarouselEventsBlock = React.lazy(() =>
  import('./Blocks/CarouselEventsBlock').then(module => ({ default: module.CarouselEventsBlock })),
);
const CarouselNewsBlock = React.lazy(() =>
  import('./Blocks/CarouselNewsBlock').then(module => ({ default: module.CarouselNewsBlock })),
);
const CodeBlock = React.lazy(() => import('./Blocks/CodeBlock').then(module => ({ default: module.CodeBlock })));

const ContentFeedBlock = React.lazy(() =>
  import('./Blocks/ContentFeedBlock').then(module => ({ default: module.ContentFeedBlock })),
);
const CoreColumnsBlock = React.lazy(() =>
  import('./Blocks/CoreColumnsBlock').then(module => ({ default: module.CoreColumnsBlock })),
);
const DianomiBlock = React.lazy(() =>
  import('./Blocks/DianomiAdBlock').then(module => ({ default: module.DianomiBlock })),
);
const ETFManagersBlock = React.lazy(() =>
  import('./Blocks/ETFManagersBlock').then(module => ({ default: module.ETFManagersBlock })),
);
const GoogleAdBlock = React.lazy(() =>
  import('./Blocks/GoogleAdBlock').then(module => ({ default: module.GoogleAdBlock })),
);
const InvestingChannelBlock = React.lazy(() =>
  import('./Blocks/InvestingChannelBlock').then(module => ({ default: module.InvestingChannelBlock })),
);
const LinkGroupBlock = React.lazy(() =>
  import('./Blocks/LinkGroupBlock').then(module => ({ default: module.LinkGroupBlock })),
);
const NewsFeaturedSectionBlock = React.lazy(() =>
  import('./Blocks/NewsFeaturedSectionBlock').then(module => ({ default: module.NewsFeaturedSectionBlock })),
);
const NewsFeedBlock = React.lazy(() =>
  import('./Blocks/NewsFeedBlock').then(module => ({ default: module.NewsFeedBlock })),
);
const NewsGridBlock = React.lazy(() =>
  import('./Blocks/NewsGridBlock').then(module => ({ default: module.NewsGridBlock })),
);
const NewsletterBoxBlock = React.lazy(() =>
  import('./Blocks/NewsletterBoxBlock').then(module => ({ default: module.NewsletterBoxBlock })),
);
const PodcastsListBlock = React.lazy(() =>
  import('./Blocks/PodcastsListBlock').then(module => ({ default: module.PodcastsListBlock })),
);
const PostFeedBlock = React.lazy(() =>
  import('./Blocks/PostFeedBlock').then(module => ({ default: module.PostFeedBlock })),
);
const PrimisBlock = React.lazy(() => import('./Blocks/PrimisBlock').then(module => ({ default: module.PrimisBlock })));
const SidebarPostsBlock = React.lazy(() =>
  import('./Blocks/SidebarPostsBlock').then(module => ({ default: module.SidebarPostsBlock })),
);
const StockMoversBlock = React.lazy(() =>
  import('./Blocks/StockMoversBlock').then(module => ({ default: module.StockMoversBlock })),
);
const TradeIdeasWidgetBlock = React.lazy(() =>
  import('./Blocks/TradeIdeasWidgetBlock').then(module => ({ default: module.TradeIdeasWidgetBlock })),
);
const WatchlistSidebarWidgetBlock = React.lazy(() =>
  import('./Blocks/WatchlistSidebarWidgetBlock').then(module => ({ default: module.WatchlistSidebarWidgetBlock })),
);
const WistiaPlayer = React.lazy(() =>
  import('./Blocks/WistiaPlayerBlock').then(module => ({ default: module.WistiaPlayer })),
);
const YoutubeBlock = React.lazy(() =>
  import('./Blocks/YoutubeBlock').then(module => ({ default: module.YoutubeBlock })),
);
const InstagramBlock = React.lazy(() =>
  import('./Blocks/InstagramBlock').then(module => ({ default: module.InstagramBlock })),
);
const YoutubeListBlock = React.lazy(() =>
  import('./Blocks/YoutubeListBlock').then(module => ({ default: module.YoutubeListBlock })),
);
const ListingPreviewBlock = React.lazy(() =>
  import('./Blocks/ListingPreviewBlock').then(module => ({ default: module.ListingPreviewBlock })),
);

const CryptocurrenciesBlock = React.lazy(() =>
  import('./Blocks/CryptocurrenciesBlock').then(module => ({ default: module.CryptocurrenciesBlock })),
);
const ShareButtonsBlock = React.lazy(() =>
  import('./Blocks/ShareButtonsBlock').then(module => ({ default: module.ShareButtonsBlock })),
);
const CoinMarqueeBlock = React.lazy(() =>
  import('./Blocks/CoinMarqueeBlock').then(module => ({ default: module.CoinMarqueeBlock })),
);
const CoinListBlock = React.lazy(() =>
  import('./Blocks/CoinListBlock').then(module => ({ default: module.CoinListBlock })),
);
const SectionHeaderBlock = React.lazy(() =>
  import('./Blocks/SectionHeaderBlock').then(module => ({ default: module.SectionHeaderBlock })),
);
const ResponsiveImageBlock = React.lazy(() =>
  import('./Blocks/ResponsiveImageBlock').then(module => ({ default: module.ResponsiveImageBlock })),
);
const CoreImage = React.lazy(() => import('./Blocks/CoreImage').then(module => ({ default: module.CoreImage })));
const PageTitleBlock = React.lazy(() =>
  import('./Blocks/PageTitleBlock').then(module => ({ default: module.PageTitleBlock })),
);
const WidgetBlock = React.lazy(() => import('./Blocks/WidgetBlock').then(module => ({ default: module.WidgetBlock })));

const GravityFormBlock = React.lazy(() =>
  import('./Blocks/GravityFormBlock').then(module => ({ default: module.GravityFormBlock })),
);
const CompareProducts = React.lazy(() =>
  import('./Blocks/CompareProducts').then(module => ({ default: module.CompareProducts })),
);
const ConnatixLiveBlock = React.lazy(() =>
  import('./Blocks/ConnatixLiveBlock').then(module => ({ default: module.ConnatixLiveBlock })),
);
const ConnatixBlock = React.lazy(() =>
  import('./Blocks/ConnatixBlock').then(module => ({ default: module.ConnatixBlock })),
);
const CardBlock = React.lazy(() => import('./Blocks/CardBlock').then(module => ({ default: module.CardBlock })));
const AttributesTableBlock = React.lazy(() =>
  import('./Blocks/AttributesTableBlock').then(module => ({ default: module.AttributesTableBlock })),
);
const OfferingCardBlock = React.lazy(() =>
  import('./Blocks/OfferingCardBlock').then(module => ({ default: module.OfferingCardBlock })),
);
const RedditBlock = React.lazy(() => import('./Blocks/RedditBlock').then(module => ({ default: module.RedditBlock })));

const RaptiveAdPlaceholderBlock = React.lazy(() =>
  import('./Blocks/RaptiveAdPlaceholderBlock').then(module => ({ default: module.RaptiveAdPlaceholderBlock })),
);

const NativoAdPlaceholderBlock = React.lazy(() =>
  import('./Blocks/NativoAdPlaceholderBlock').then(module => ({ default: module.NativoAdPlaceholderBlock })),
);

const FeaturedPodcastCardsBlock = React.lazy(() =>
  import('./Blocks/FeaturedPodcastCardsBlock').then(module => ({ default: module.FeaturedPodcastCardsBlock })),
);
const TaboolaPlacementBlock = React.lazy(() =>
  import('./Blocks/TaboolaPlacementBlock').then(module => ({ default: module.TaboolaPlacementBlock })),
);
const YouTubeVideoCard = React.lazy(() =>
  import('./Blocks/YoutubeBlock').then(module => ({ default: module.YouTubeVideoCardElement })),
);
const BeehiivEmailCaptureBlock = React.lazy(() =>
  import('./Blocks/BeehiivEmailCaptureBlock').then(module => ({ default: module.BeehiivEmailCaptureBlock })),
);
const GoBankRateOfferingsBlock = React.lazy(() =>
  import('./Blocks/GoBankRateOfferingsBlock').then(module => ({ default: module.GoBankRateOfferingsBlock })),
);
const SmartAssetsBlock = React.lazy(() =>
  import('./Blocks/SmartAssetsBlock').then(module => ({ default: module.SmartAssetsBlock })),
);
const BestOfSummary = React.lazy(() =>
  import('./Blocks/BestOfSummaryBlock').then(module => ({ default: module.BestOfSummaryBlock })),
);
const AdvertorialProductBlock = React.lazy(() =>
  import('./Blocks/AdvertorialProductBlock').then(module => ({ default: module.AdvertorialProductBlock })),
);
const CountDownCardBlock = React.lazy(() =>
  import('./Blocks/CountDownCardBlock').then(module => ({ default: module.CountDownCardBlock })),
);

export interface BlockProps {
  blockName: string;
  attrs?: any;
}

export interface PrimisBlock extends BlockProps {
  id: string;
}

export interface BlockComponentProps {
  block: any;
  campaigns?: any;
  getBlock?: (props: BlockComponentProps) => JSX.Element | JSX.Element[] | null;
  type?: 'sidebar' | 'default';
}

export interface CampaignifyPlaceholderBlock extends BlockProps {
  position: string;
  nodeID: number | string;
}

export type BlockType = BlockProps | PrimisBlock | CampaignifyPlaceholderBlock;

export const Block: React.FC<BlockComponentProps> = props => {
  props.block.type = props.type ?? 'default';
  const blockComponent = getBlockComponent(props);
  if (blockComponent === null && typeof props.getBlock === 'function') {
    return props.getBlock ? props.getBlock(props) : blockComponent;
  }
  return blockComponent;
};

export const getBlockComponent = ({ block, campaigns, getBlock }: BlockComponentProps) => {
  if (
    block.blockName === 'core/paragraph' ||
    block.blockName === 'core/heading' ||
    block.blockName === 'core/table' ||
    block.blockName === 'core/list' ||
    block.blockName === 'tadv/classic-paragraph'
  ) {
    if (block?.tag) {
      return createHTMLElement(block);
    }
    return <CoreElementWrapper dangerouslySetInnerHTML={{ __html: block.innerHTML }} />;
  } else if (block.blockName === 'core/image') {
    return <CoreImage block={block} />;
  } else if (block.blockName === 'custom/card') {
    return <CardBlock blocks={block.blocks} getBlock={getBlock} />;
  } else if (block.blockName === 'custom/attributes-table') {
    return <AttributesTableBlock {...block} />;
  } else if (block.blockName === 'advanced-gutenberg-blocks/code') {
    return <CodeBlock {...block} />;
  } else if (block.blockName === 'acf/content-feed') {
    return <ContentFeedBlock {...block} />;
  } else if (block.blockName === 'acf/etf-managers') {
    return <ETFManagersBlock {...block} />;
  } else if (
    block.blockName === 'core-embed/youtube' ||
    (block.blockName === 'core/embed' && block.attrs.providerNameSlug === 'youtube')
  ) {
    return <YoutubeBlock {...block} />;
  } else if (
    (block.blockName === 'core/embed' && block.attrs.providerNameSlug === 'twitter') ||
    (block?.tag === 'blockquote' && block?.tagAttributes?.className === 'twitter-tweet')
  ) {
    return (
      <ImportOnVisibility
        component={() => import('./Blocks/TwitterBlock').then(module => ({ default: module.TwitterBlock }))}
        {...block}
      />
    );
  } else if (
    (block.blockName === 'core/embed' && block.attrs.providerNameSlug === 'reddit') ||
    (['blockquote', 'figure'].includes(block?.tag) &&
      (block?.tagAttributes?.className?.includes('is-provider-reddit') ||
        block?.tagAttributes?.className?.includes('wp-block-embed-reddit')))
  ) {
    return (
      <NoFirstRender>
        <RedditBlock {...block} />
      </NoFirstRender>
    );
  } else if (
    (block.blockName === 'core/embed' && block.attrs.providerNameSlug === 'youtube') ||
    (['blockquote', 'figure'].includes(block?.tag) &&
      (block?.tagAttributes?.className?.includes('is-provider-youtube') ||
        block?.tagAttributes?.className?.includes('wp-block-embed-youtube')))
  ) {
    const iframeSrcRegex = /<iframe[^>]*?\bsrc="([^"]*)"[^>]*>/;
    const matchArray = (block?.innerHTML as string)?.match(iframeSrcRegex);
    const embedUrl = matchArray?.[1];
    return (
      <NoFirstRender>
        <YoutubeBlock attrs={{ url: embedUrl }} />
      </NoFirstRender>
    );
  } else if (block.blockName === 'acf/trade-ideas-widget') {
    return <TradeIdeasWidgetBlock {...block} />;
  } else if (block.blockName === 'acf/product-home-offer' || block.blockName === 'acf/offering-cards') {
    return <ListingPreviewBlock {...block} />;
  } else if (block.blockName === 'acf/offering-card') {
    return <OfferingCardBlock {...block} />;
  } else if (block.blockName === 'acf/compare-products' || block.blockName === 'acf/compare-brokers') {
    return <CompareProducts {...block} products={block.products} />;
  } else if (block.blockName === 'acf/watchlist-sidebar-widget') {
    return <WatchlistSidebarWidgetBlock {...block} />;
  } else if (
    (block?.tag === 'blockquote' && block?.tagAttributes?.className === 'instagram-media') ||
    block.blockName === 'core-embed/instagram'
  ) {
    return <InstagramBlock {...block} />;
  } else if (block.blockName === 'acf/youtube-list') {
    return <YoutubeListBlock {...block} />;
  } else if (block.blockName === 'acf/link-group') {
    return <LinkGroupBlock {...block} />;
  } else if (block.blockName === 'acf/wistia-player') {
    return <WistiaPlayer {...block} />;
  } else if (block.blockName === 'acf/section-block-header') {
    return <SectionHeaderBlock {...block} />;
  } else if (block.blockName === 'ad/campaign') {
    return <div className="bz-campaign" dangerouslySetInnerHTML={{ __html: sanitizeHTML(block.value) }} />;
  } else if (block.blockName === 'acf/news-featured-section') {
    return <NewsFeaturedSectionBlock {...block} />;
  } else if (block.blockName === 'acf/news-grid') {
    return <NewsGridBlock {...block} />;
  } else if (block.blockName === 'acf/newsletter-subscription-box') {
    return <NewsletterBoxBlock {...block.attrs.data} deviceType={block.deviceType} />;
  } else if (block.blockName === 'acf/dianomi-ad') {
    return <DianomiBlock {...block.attrs.data} />;
  } else if (block.blockName === 'acf/news-feed') {
    return <NewsFeedBlock {...block} />;
  } else if (block.blockName === 'gravityforms/form') {
    return <GravityFormBlock {...block} />;
  } else if (block.blockName === 'acf/beehiiv-email-capture') {
    return <BeehiivEmailCaptureBlock {...block.attrs.data} deviceType={block.deviceType} />;
  } else if (block.blockName === 'acf/go-bank-rate-offerings') {
    return <GoBankRateOfferingsBlock {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/best-of-summary') {
    return <BestOfSummary {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/smart-assets-block') {
    return <SmartAssetsBlock {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/advertorial-product') {
    return <AdvertorialProductBlock {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/videos' || block.blockName === 'acf/youtube-list') {
    if (Array.isArray(block.videos)) {
      return (
        <Grid>
          {block.videos.map((item: YouTubeVideoWordpress) => (
            <YouTubeVideoCard key={item.id} video={item} />
          ))}
        </Grid>
      );
    }
    return null;
  } else if (block.blockName === 'acf/advertisement-card') {
    return <AdvertisingCardBlock {...block.attrs.data} />;
  } else if (block.blockName === 'acf/benzinga-briefs') {
    return <BenzingaBriefsBlock {...block} />;
  } else if (block.blockName === 'acf/carousel') {
    return <CarouselBlock {...block} />;
  } else if (block.blockName === 'acf/carousel-news') {
    return <CarouselNewsBlock {...block} />;
  } else if (block.blockName === 'acf/carousel-events') {
    return <CarouselEventsBlock {...block} />;
  } else if (block.blockName === 'acf/calendars-widget') {
    return <CalendarsWidgetBlock {...block} />;
  } else if (block.blockName === 'acf/responsive-image') {
    return <ResponsiveImageBlock {...block} />;
  } else if (
    block.blockName === 'acf/call-to-action' ||
    block.blockName === 'acf/cta-template' ||
    block.blockName === 'acf/cta'
  ) {
    return <CallToActionBlock {...block} />;
  } else if (block.blockName === 'acf/call-to-action-stacked-slider') {
    return <CallToActionStackedSliderBlock {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/google-ad-placement') {
    // Disabling because of Raptive ads
    const isBzApp = appEnvironment().isApp(appName.bz);
    if (isBzApp) return null;
    return <GoogleAdBlock {...block} />;
  } else if (block.blockName === 'acf/raptive-ad-placement') {
    const isBzApp = appEnvironment().isApp(appName.bz);
    if (!isBzApp) return null;
    return <RaptiveAdPlaceholderBlock {...block} />;
  } else if (block.blockName === 'acf/nativo-ad-placement') {
    return <NativoAdPlaceholderBlock {...block} />;
  } else if (block.blockName === 'acf/stock-movers-compact') {
    return <StockMoversBlock {...block} layout="compact" />;
  } else if (block.blockName === 'acf/stock-movers') {
    return <StockMoversBlock {...block} />;
  } else if (block.blockName === 'acf/share-buttons') {
    return <ShareButtonsBlock />;
  } else if (block.blockName === 'acf/page-title') {
    return <PageTitleBlock {...block} />;
  } else if (block.blockName === 'acf/taboola-placement') {
    return <TaboolaPlacementBlock {...block} />;
  } else if (block.blockName === 'acf/post-feed') {
    return <PostFeedBlock {...block} />;
  } else if (block.blockName === 'acf/count-down-card') {
    return <CountDownCardBlock {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/primis-block') {
    // TODO: Return back after Raptive fully setup
    // return <PrimisBlock {...block} />;
    return null;
  } else if (block.blockName === 'acf/connatix-block') {
    return <ConnatixBlock {...block} />;
  } else if (block.blockName === 'acf/connatix-live-block') {
    return <ConnatixLiveBlock {...block} />;
  } else if (block.blockName === 'acf/podcasts-list') {
    return <PodcastsListBlock {...block} />;
  } else if (block.blockName === 'acf/featured-podcast-cards') {
    const episodes = block?.attrs?.data?.episodes;
    return <FeaturedPodcastCardsBlock episodes={episodes} />;
  } else if (block.blockName === 'acf/sidebar-related-posts') {
    return <SidebarPostsBlock {...block} />;
  } else if (block.blockName === 'acf/investing-channel-block' || block.blockName === 'ad/investing-channel') {
    // TODO: Return back after Raptive fully setup
    // return <InvestingChannelBlock block={block} />;
  } else if (block.blockName === 'acf/cryptocurrencies') {
    return <CryptocurrenciesBlock {...block} />;
  } else if (block.blockName === 'acf/benzinga-counter') {
    return <BenzingaStaticsBlock {...block} />;
  } else if (block.blockName === 'affiliate-plugin/lasso') {
    return <BenzingaLassoBlock {...{ block }} />;
  } else if (['core/html', 'core/separator', 'core/spacer'].includes(block.blockName)) {
    if (block.innerHTML.includes('crypto-widget-CoinMarquee')) {
      return <CoinMarqueeBlock {...block} />;
    } else if (block.innerHTML.includes('crypto-widget-CoinList')) {
      return <CoinListBlock {...block} />;
    }
    if (block?.tag) {
      return createHTMLElement(block);
      // if (
      //   block.tag === 'p' ||
      //   block.tag === 'figure' ||
      //   block.tag === 'table' ||
      //   block.tag === 'script' ||
      //   // block.tag === 'div' ||
      //   block.tag === 'a' ||
      //   block.tag === 'em' ||
      //   block.tag === 'strong'
      // ) {
      //   return createHTMLElement(block);
      // } else {
      //   console.log(block);
      //   return null;
      // }
    } else {
      return <div dangerouslySetInnerHTML={{ __html: block.innerHTML }} style={{ display: 'inline' }} />;
    }
  } else if (block.blockName === 'core/group') {
    if (!Array.isArray(block.innerBlocks)) return <div />;
    const groupBlocks = block.innerBlocks?.map((inner_block, index) => {
      const key = `core-group-block-${inner_block?.blockName}-${index}`;
      return (
        <div className="core-group-wrapper" key={key}>
          <Block {...{ block: inner_block, campaigns, getBlock }} />
        </div>
      );
    });
    if (block?.attrs?.className) {
      return <div className={block.attrs.className}>{groupBlocks}</div>;
    } else {
      return groupBlocks;
    }
  } else if (block.blockName === 'core/columns') {
    return <CoreColumnsBlock block={block} campaigns={campaigns} getBlock={getBlock} />;
  } else if (block.blockName === 'optinmonster-placeholder') {
    return Array.isArray(block.attrs.ids) ? (
      <>
        {block.attrs.ids.map((id: string) => (
          <div id={id}></div>
        ))}
      </>
    ) : null;
  } else if (block.blockName === 'acf/widget-container' && block?.blocks) {
    return <WidgetBlock {...{ block, campaigns, getBlock }} />;
  } else if (block.blockName === 'acf/action-button') {
    return <ActionButtonBlock {...block} />;
  } else if (block.blockName === 'acf/call-phone-number') {
    return (
      <CallToAction
        button_text={block.attrs.data.phone_number}
        description={block.attrs.data.description}
        floating={false}
        image_url={block.attrs.data.image}
        layout="floating"
        title={block.attrs.data.title}
        url={`tel:` + block.attrs.data.phone_number}
      />
    );
  } else if (block.blockName && block.innerHTML) {
    const approvedBlocks = ['acf/into-the-block-widget', 'acf/ipo-performance', 'acf/posts-by-state', 'acf/geo-posts'];
    return (
      <div
        dangerouslySetInnerHTML={{
          __html: approvedBlocks.includes(block.blockName) ? block.innerHTML : sanitizeHTML(block.innerHTML),
        }}
      />
    );
  }

  // return <p style={{ color: 'red' }}>{block.blockName}</p>;
  return null;
};

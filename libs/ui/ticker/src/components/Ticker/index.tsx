import React, { memo, useCallback, useMemo } from 'react';
import { Dropdown } from '@benzinga/core-ui';
import { TickerCard, TickerCardQuote } from '../TickerCard';
import styled from '@benzinga/themetron';
import { QuotePageSlug, TickerLink } from '../TickerLink';
import { TickerDetails, TickerVariant, TickerBoxSymbol, WrapperProps } from './TickerDetails';
import { DelayedQuote, Quote, getQuoteChange } from '@benzinga/quotes-manager';
import { convertToTickerCardQuote, getBgColor, getTextColor } from './utils';
import { CryptoQuote, useTickerData } from './useTickerData';
import { useQuoteSubscription, useTickerDetails } from '@benzinga/quotes-manager-hooks';
import { GetReportButton } from '../GetReportButton';
import { FaFilePdf } from 'react-icons/fa';
import { isMobile } from '@benzinga/device-utils';
import { useLocalStorage, useHydrate } from '@benzinga/hooks';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { RankingDetail } from '@benzinga/quotes-manager';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';

export type { TickerVariant };

export interface TickerProps {
  closeWhenClickOutside?: boolean;
  direction?: boolean;
  exchange?: string;
  freeAlerts?: boolean;
  hidePopup?: boolean;
  hideChange?: boolean;
  logo?: string;
  portalElement?: HTMLElement | Element | null;
  onPressTicker?: (ticker: string) => void;
  symbol: string;
  targetElement?: JSX.Element | null;
  value?: number;
  variant?: TickerVariant;
  showDownloadReport?: boolean;
  slug?: QuotePageSlug;
  quote?: TickerCardQuote;
  rankingData?: RankingDetail;
  target?: '_blank' | '_self';
}
export const Ticker: React.FC<TickerProps> = ({
  closeWhenClickOutside,
  direction,
  exchange,
  freeAlerts,
  hideChange,
  hidePopup,
  logo,
  onPressTicker,
  portalElement,
  showDownloadReport,
  slug,
  symbol,
  targetElement,
  variant,
}) => {
  const { cryptoQuote, stockQuote } = useTickerData(symbol, exchange);
  const { quote } = useQuoteSubscription(symbol);
  const { percentageChange } = getQuoteChange<Quote>(quote);
  const tickerDetails = useTickerDetails(symbol);
  const percentChange = Number(cryptoQuote?.changePercent ?? stockQuote?.changePercent ?? percentageChange);
  const isMobileHydrated = useHydrate(isMobile(), true);
  const target = isMobileHydrated ? '_self' : '_blank';

  const tickerLinkWithDetailsProps = useMemo(() => {
    return {
      direction,
      hideChange,
      logo,
      onPressTicker,
      slug,
      symbol,
      value: percentChange,
      variant,
    };
  }, [direction, hideChange, logo, onPressTicker, percentChange, slug, symbol, variant]);

  if (!stockQuote && !cryptoQuote) {
    return targetElement || <TickerBox {...{ hideChange, logo, onPressTicker, slug, symbol, target, variant }} />;
  }

  if (hidePopup) {
    return <TickerBox {...{ hideChange, logo, onPressTicker, slug, symbol, target, value: percentChange, variant }} />;
  }

  const tickerCardQuote = convertToTickerCardQuote({
    cryptoQuote: cryptoQuote as CryptoQuote,
    stockQuote: stockQuote as DelayedQuote,
  }) as TickerCardQuote;

  if (targetElement) {
    return (
      <Dropdown
        closeWhenClickOutside={isMobile() ? true : closeWhenClickOutside}
        distance={0}
        fluid
        portalElement={portalElement}
        shouldBeHoverable={() => false}
        target={targetElement}
      >
        <TickerCard
          isCrypto={symbol?.includes('/USD')}
          key={symbol}
          onPressTicker={onPressTicker}
          quote={tickerCardQuote}
        />
      </Dropdown>
    );
  }

  if (isMobileHydrated) {
    return (
      <TickerBoxWrapper $value={percentChange} $variant={variant} className="ticker-box-wrapper">
        <TickerLinkWithDetails {...tickerLinkWithDetailsProps} target="_self" />
      </TickerBoxWrapper>
    );
  }

  return (
    <div className="ticker-wrapper">
      <TickerDropdown
        {...{
          closeWhenClickOutside,
          direction,
          freeAlerts,
          hideChange,
          logo,
          onPressTicker,
          quote: tickerCardQuote,
          rankingData: tickerDetails?.rankings,
          showDownloadReport,
          slug,
          symbol,
          tickerCardQuote,
          value: percentChange,
          variant,
        }}
      />
    </div>
  );
};

export const TickerBox: React.FC<TickerProps> = ({
  direction,
  freeAlerts,
  hideChange,
  logo,
  onPressTicker,
  portalElement,
  quote,
  showDownloadReport,
  slug,
  symbol,
  target,
  targetElement,
  value,
  variant = 'box',
}) => {
  return (
    <TickerContainer
      {...{
        direction,
        freeAlerts,
        hideChange,
        logo,
        onPressTicker,
        portalElement,
        quote,
        showDownloadReport,
        slug,
        symbol,
        target,
        targetElement,
        value,
        variant,
      }}
    />
  );
};

const TickerContainer: React.FC<TickerProps> = ({
  direction,
  freeAlerts,
  hideChange = false,
  logo,
  onPressTicker,
  showDownloadReport,
  slug,
  symbol,
  target,
  value,
  variant,
}) => {
  if (variant === 'ticker') {
    return (
      <TickerBoxSymbol $value={value} $variant={variant}>
        {symbol}
      </TickerBoxSymbol>
    );
  }

  return (
    <TickerBoxWrapper $value={value} $variant={variant} className="ticker-box-wrapper">
      <TickerLinkWithDetails
        direction={direction}
        hideChange={hideChange}
        logo={logo}
        onPressTicker={onPressTicker}
        slug={slug}
        symbol={symbol}
        target={target}
        value={value}
        variant={variant}
      />
      <AdditionalButtons freeAlerts={freeAlerts} showDownloadReport={showDownloadReport} symbol={symbol} />
    </TickerBoxWrapper>
  );
};

const TickerDropdown: React.FC<TickerProps> = ({
  closeWhenClickOutside,
  direction,
  freeAlerts,
  hideChange = false,
  logo,
  onPressTicker,
  quote,
  rankingData,
  showDownloadReport,
  slug,
  symbol,
  value,
  variant,
}) => {
  //const session = React.useContext(SessionContext);

  // Ticker hover Disabled
  // const handleShouldBeHoverable = useCallback(() => {
  //   if (isMobile()) return false;
  //   if (!isGlobalImpressionStored(`${symbol}`)) {
  //     storeGlobalImpression(`${symbol}`);
  //     session.getManager(TrackingManager).trackTickerEvent('hover', { symbol });
  //   }
  //   return true;
  // }, [session, symbol]);

  const tickerLinkWithDetailsProps = useMemo(() => {
    return {
      direction,
      hideChange,
      logo,
      onPressTicker,
      slug,
      symbol,
      value,
      variant,
    };
  }, [direction, hideChange, logo, onPressTicker, slug, symbol, value, variant]);

  return (
    <TickerBoxWrapper $value={value} $variant={variant} className="ticker-box-wrapper">
      <Dropdown
        closeWhenClickOutside={isMobile() ? true : closeWhenClickOutside}
        distance={0}
        fluid
        shouldBeHoverable={() => false}
        target={<TickerLinkWithDetails {...tickerLinkWithDetailsProps} />}
      >
        <TickerCard
          isCrypto={symbol?.includes('/USD')}
          key={symbol}
          onPressTicker={onPressTicker}
          quote={quote as TickerCardQuote}
          rankingData={rankingData}
        />
      </Dropdown>
      <AdditionalButtons freeAlerts={freeAlerts} showDownloadReport={showDownloadReport} symbol={symbol} />
    </TickerBoxWrapper>
  );
};

const TickerLinkWithDetails: React.FC<TickerProps> = memo(
  ({ direction, hideChange = false, logo, onPressTicker, slug, symbol, target, value, variant }) => (
    <TickerLink
      className="ticker-link-wrapper"
      onPressTicker={onPressTicker}
      slug={slug}
      symbol={symbol}
      target={target}
    >
      <TickerDetails
        direction={direction}
        hideChange={hideChange}
        logo={logo}
        symbol={symbol}
        value={value}
        variant={variant}
      />
    </TickerLink>
  ),
);

const AdditionalButtons: React.FC<{ freeAlerts?: boolean; showDownloadReport?: boolean; symbol: string }> = ({
  freeAlerts,
  showDownloadReport,
  symbol,
}) => {
  const getFreeAlertsLink = `https://www.benzinga.com/profile/portfolio/?add_ticker=${symbol}`;

  const onFreeAlertClick = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.stopPropagation();
    window.open(getFreeAlertsLink);
  };

  return (
    <>
      {!showDownloadReport && freeAlerts && (
        <TickerBoxAlertWrapper className="text-blue-500" onClick={onFreeAlertClick}>
          + Free Alerts
        </TickerBoxAlertWrapper>
      )}
      {showDownloadReport && (
        <GetReportButton
          size="sm"
          targetElement={
            <div className="flex items-center ml-2 text-sm text-blue-500">
              Get Free Report
              <FaFilePdf className="ml-1" />
            </div>
          }
          ticker={symbol}
        />
      )}
    </>
  );
};

export const TickerBoxAlertWrapper = styled.span`
  font-size: 0.8rem;
  font-weight: 500;
  margin: 0 0.4rem;
  &:hover {
    text-decoration: underline;
  }
`;

export const TickerBoxWrapper = styled.div<WrapperProps>`
  align-items: center;
  background: ${({ $value, $variant, theme }) => getBgColor({ theme, value: $value, variant: $variant })};
  border-color: ${({ theme }) => theme.colors.border} !important;
  border-radius: 0.2rem;
  border-width: ${props => (props.$variant === 'light' ? 0 : '1px')};
  color: ${({ $value, $variant, theme }) => getTextColor({ theme, value: $value, variant: $variant })};
  display: inline-flex;
  margin: 0;
  padding-left: 4px;
  padding-right: 4px;
  overflow: hidden;
  text-decoration: none;
  text-align: left;
  line-height: 22px;
  height: 24px;
  cursor: pointer;
  white-space: nowrap;
  .direction {
    height: 24px;
    .anticon {
      top: 2px;
      position: relative;
    }
  }
  .download-report {
    text-decoration: underline;
    font-size: ${({ theme }) => theme.fontSize.base};
    padding: 0 5px;
  }
`;

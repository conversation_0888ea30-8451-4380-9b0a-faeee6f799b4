import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import { TOOLTIP_TEXTS } from '.';
import { Icon, Tooltip } from '@benzinga/core-ui';
import { RankingPositive } from './RankingPositive';
import { RankingNegative } from './RankingNegative';

export const GetPriceTrend = (trend: string, label: string) => {
  const tooltipText = TOOLTIP_TEXTS[label] || '';
  return (
    <div className="position-wrap gap-2">
      <span className="position-label flex items-center gap-1 mr-1">
        {label}
        <Tooltip content={tooltipText} width={250}>
          <Icon
            className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
            icon={faInfoCircle}
          />
        </Tooltip>
      </span>
      <div className={`position-direction ${trend === 'Y' ? 'positive' : 'negative'}`}>
        {trend === 'Y' ? <RankingPositive /> : <RankingNegative />}
      </div>
    </div>
  );
};

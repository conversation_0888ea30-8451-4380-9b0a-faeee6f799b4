import { getColor } from './utils';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import { TOOLTIP_TEXTS } from '.';
import { Icon, Tooltip } from '@benzinga/core-ui';

export const RankingItem = ({ label, value }: { label: string; value: number }) => {
  const tooltipText = TOOLTIP_TEXTS[label] || '';
  return (
    <div className="ranking-item">
      <span className="ranking-label flex items-center gap-1 mr-1">
        {label}
        <Tooltip content={tooltipText} width={250}>
          <Icon
            className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
            icon={faInfoCircle}
          />
        </Tooltip>
      </span>
      <div className={`${value ? getColor(value) : 'no-valuue'} ranking-value`}>
        {value === 0 ? 'N/A' : value.toFixed(2)}
      </div>
    </div>
  );
};

import React from 'react';
import { WordpressSidebar } from '@benzinga/content-manager';
import { ArticleBlock as ArticleBlockType } from '@benzinga/article-manager';
import { NoFirstRender } from '@benzinga/hooks';
import { Blocks } from '@benzinga/blocks';
import { LayoutAdmin } from '@benzinga/blocks';
import { RaptiveAdPlacementType } from '@benzinga/ads-utils';

const TaboolaPlacement = React.lazy(() =>
  import('@benzinga/ads').then(module => ({ default: module.TaboolaPlacement })),
);

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const WNSTNWidget = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.WNSTNWidget })));

export interface ArticleSidebarProps {
  hasAdLight?: boolean;
  sidebar: WordpressSidebar;
  raptiveEnabled?: boolean;
  url?: string;
  enableTaboola?: boolean;
  enableWNSTN?: boolean;
  nodeId?: number;
  followUpQuestions?: string[] | null;
}

export const ArticleSidebar: React.FC<ArticleSidebarProps> = ({
  enableTaboola,
  enableWNSTN,
  followUpQuestions,
  hasAdLight,
  nodeId,
  raptiveEnabled,
  sidebar,
  url,
}) => {
  const hasRaptiveAdPlacementBlock = (type: RaptiveAdPlacementType) => {
    return !!sidebar?.blocks?.find(
      block => block.blockName === 'acf/raptive-ad-placement' && block.attrs?.data?.type === type,
    );
  };

  const hasExistingStaticSidebarPlacement = hasRaptiveAdPlacementBlock('static-sidebar');
  const hasExistingStickySidebarPlacement = hasRaptiveAdPlacementBlock('sticky-sidebar');

  return (
    <div className="flex flex-col gap-y-4">
      <NoFirstRender>
        <React.Suspense fallback={<div />}>{sidebar && <LayoutAdmin post={sidebar} />}</React.Suspense>
      </NoFirstRender>

      {enableWNSTN && followUpQuestions ? (
        <div className="h-[220px]">
          <NoFirstRender>
            <WNSTNWidget articleID={nodeId} questions={followUpQuestions} />
          </NoFirstRender>
        </div>
      ) : (
        !hasExistingStaticSidebarPlacement &&
        raptiveEnabled && (
          <React.Suspense fallback={<div />}>
            <RaptiveAdPlaceholder className="w-[300px] overflow-hidden" type="static-sidebar" />
          </React.Suspense>
        )
      )}

      {url && enableTaboola && (
        <TaboolaPlacement
          id="right-rail-thumbnails"
          mode="thumbnails-right"
          placement="Right Rail Thumbnails"
          settings={{
            article: 'auto',
            target_type: 'mix',
            url: url,
          }}
          shouldFlush={false}
          url={url}
          vizSensor={false}
        />
      )}

      <React.Suspense fallback={<div />}>
        {sidebar?.blocks && (
          <Blocks
            blocks={sidebar.blocks as unknown as ArticleBlockType[]}
            className="flex flex-col gap-4"
            type="sidebar"
          />
        )}
      </React.Suspense>
      {!hasExistingStickySidebarPlacement && raptiveEnabled && !hasAdLight && (
        <React.Suspense fallback={<div />}>
          <RaptiveAdPlaceholder className="w-[300px] overflow-hidden" type="sticky-sidebar" />
        </React.Suspense>
      )}
      {/* <React.Suspense>
        {!appEnvironment().isApp(appName.india) && <AmplyWidget id="amply-widget-sidebar" variant="sidebar" />}
      </React.Suspense> */}
    </div>
  );
};

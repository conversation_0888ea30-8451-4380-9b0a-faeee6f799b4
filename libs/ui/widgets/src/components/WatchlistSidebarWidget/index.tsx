import React from 'react';
import { SignUpBox } from '@benzinga/watchlist-ui';
import { WatchlistCreateBox } from '../WatchlistCreateBox';
import { WatchListMovers } from '../WatchlistMovers';
import { useWatchlistsLoading } from '@benzinga/watchlist-manager-hooks';
import { SectionTitle } from '@benzinga/core-ui';
import { useTranslation } from 'react-i18next';

export interface WatchlistSidebarWidgetProps {
  hideText?: boolean;
}

export const WatchlistSidebarWidget: React.FC<WatchlistSidebarWidgetProps> = ({ hideText = false }) => {
  const { t } = useTranslation('common');

  const { isLoading, isLoggedIn, watchlists } = useWatchlistsLoading();

  return (
    <div className="mt-4">
      {isLoggedIn ? (
        <div>
          {watchlists?.length ? (
            <div>
              {!hideText && <SectionTitle>{t('Sidebars.Widgets.WatchlistSidebarWidget.title')}</SectionTitle>}
              <WatchListMovers isLoadingWatchlists={isLoading} watchlists={watchlists} />
            </div>
          ) : (
            <WatchlistCreateBox
              actionText={t('Sidebars.Widgets.WatchlistSidebarWidget.watchListBox.actionText').toString()}
              title={t('Sidebars.Widgets.WatchlistSidebarWidget.watchListBox.title').toString()}
            />
          )}
        </div>
      ) : (
        <SignUpBox
          action={t('Sidebars.Widgets.WatchlistSidebarWidget.signUpBox.action').toString()}
          subtitle={t('Sidebars.Widgets.WatchlistSidebarWidget.signUpBox.subtitle').toString()}
          title={t('Sidebars.Widgets.WatchlistSidebarWidget.signUpBox.title').toString()}
        />
      )}
    </div>
  );
};

export default WatchlistSidebarWidget;

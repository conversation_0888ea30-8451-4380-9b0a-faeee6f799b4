'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import { faCheck } from '@fortawesome/pro-solid-svg-icons/faCheck';
import { Icon } from '@benzinga/core-ui';
import { FaArrowRightLong } from 'react-icons/fa6';
import StockAlertStyle from './stockAlertPaywall.module.scss';
import { BzImage } from '@benzinga/image';

export const StockAlertPaywall: React.FC = () => {
  const paywallChecklistFeature = [
    'Daily Updated Stock Rankings',
    'Top Stocks to Buy & Sell',
    'Hidden Market Opportunities',
    'Stocks Flashing Warning Signs',
    'Exclusive Leaderboards & Insights',
  ];
  return (
    <div className={StockAlertStyle['StockAlertWrapper']}>
      <a
        className="d-block"
        href={`https://www.benzinga.com/premium/ideas/benzinga-edge/?utm_campaign=wallstreetadvantage&utm_adType=paywall&utm_ad=rankings-page`}
        target="_blank"
      >
        <div className="flex w-full flex-col lg:flex-row">
          <div className={`${StockAlertStyle['banner-intro-details']} basis-1 lg:basis-5/12`}>
            <div className="flex flex-col justify-center h-full items-center lg:items-start flex-1 pt-4">
              <div className="pl-5">
                <BzImage height="19px" src="/next-assets/images/bz-edge-logo-dark.svg" width="170px" />
                <h5 className="uppercase mb-2 pt-3">benzinga edge members only</h5>
                <h2 className="ex-large-font mb-3 text-center lg:text-left">
                  Get the Complete Picture on Any Stock, Anytime
                </h2>
                <p>Save 43% When You Join Today</p>
              </div>
              <div className={`${StockAlertStyle['member-login']} flex p-2 pl-5 items-center gap-2 mt-4`}>
                <span>Aleready Member?</span>
                <a className="flex items-center gap-2" href="https://www.benzinga.com/login">
                  Login
                  <FaArrowRightLong />
                </a>
              </div>
            </div>
          </div>
          <div className={`${StockAlertStyle['benefits-section']} basis-1 lg:basis-7/12`}>
            <div className={StockAlertStyle['benefits-clip']}>
              <div className="flex items-center flex-col py-3 justify-center h-full">
                <div className={`${StockAlertStyle['benefits-header']} px-2`}>Members Receive</div>
                <ul className={`${StockAlertStyle['benefits-list']} p-8`}>
                  {paywallChecklistFeature.map((feature, index) => {
                    return (
                      <li key={`paywall-checklist-feature-${index}`}>
                        <span>
                          <Icon icon={faCheck} />
                        </span>
                        <span>{feature}</span>
                      </li>
                    );
                  })}
                </ul>
                <div className={` ${StockAlertStyle['unlock-access']} px-3 py-2 rounded uppercase`}>
                  Unlock Full Rankings
                </div>
              </div>
            </div>
          </div>
        </div>
      </a>
    </div>
  );
};

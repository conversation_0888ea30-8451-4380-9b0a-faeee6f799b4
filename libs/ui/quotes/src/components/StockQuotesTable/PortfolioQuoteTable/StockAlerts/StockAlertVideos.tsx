'use client';
import { TradeAlertVideo } from '@benzinga/plus-manager';
import MuxPlayer from '@mux/mux-player-react';
import styled from '@benzinga/themetron';
import { DateTime } from 'luxon';
import { BzImage } from '@benzinga/image';
import { faPlay } from '@fortawesome/pro-solid-svg-icons/faPlay';
import { SectionTitle, Icon } from '@benzinga/core-ui';

export interface StockAlertVideosProps {
  videos: TradeAlertVideo[];
  isPaywallActive: boolean;
}
export const StockAlertVideos: React.FC<StockAlertVideosProps> = ({ isPaywallActive, videos }) => {
  return (
    <StockAlertVideosWrap className="stock-alert-videos gap-4 mt-4">
      <SectionTitle bordered level={2}>
        Portfolio Updates
      </SectionTitle>
      <div className="flex flex-col ">
        {videos.map(video => (
          <div className="stock-alert-video mb-3" key={video.mux_id}>
            {video?.mux_id && video?.playback_id && (
              <div className="flex flex-row gap-4 items-center">
                {isPaywallActive ? (
                  <a
                    className="image-wrap"
                    href={`https://www.benzinga.com/premium/ideas/benzinga-edge/?utm_campaign=wallstreetadvantage&utm_adType=paywall&utm_ad=rankings-page`}
                    target="_blank"
                  >
                    {video.thumbnail_url && (
                      <div className="relative">
                        <BzImage
                          alt={video?.name ?? ''}
                          height="145"
                          objectFit="cover"
                          preload={true}
                          preloadOptions={{ media: '(min-width: 750px)' }}
                          src={video.thumbnail_url}
                          width="260"
                        />
                        <Icon className="play-icon text-[40px]" icon={faPlay} />
                      </div>
                    )}
                  </a>
                ) : (
                  <div className="video-player">
                    <MuxPlayer
                      className="basis-1/5"
                      metadata={{
                        video_id: video.mux_id,
                        video_title: video.name,
                      }}
                      playbackId={video.playback_id}
                      poster={video.thumbnail_url}
                    />
                  </div>
                )}

                <div>
                  <h4 className="basis-4/5">{video.name}</h4>
                  <p>{video.created_at && DateTime.fromISO(video.created_at).toFormat('LLL dd, yyyy')}</p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </StockAlertVideosWrap>
  );
};

const StockAlertVideosWrap = styled.div`
  clear: both;
  display: block;
  mux-player {
    --seek-backward-button: none;
    --seek-forward-button: none;
  }
  .video-player {
    max-width: 260px;
    @media (max-width: 567px) {
      max-width: 200px;
    }
  }
  .image-wrap {
    dispaly: block;
    position: relative;
    cursor: pointer;
    .play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
    }
  }
`;

import { useTheme } from '@benzinga/themetron';
import Link from 'next/link';
import { getColorByValue } from '@benzinga/frontend-utils';
import { formatPriceWithCurrency } from '@benzinga/utils';

const getCellClass = (isPaywallActive, existingClass) => {
  if (isPaywallActive) {
    return existingClass + ' blur-sm';
  }
  return existingClass;
};

export const StockAlertsColumnsDef = ({ isPaywallActive }: { isPaywallActive: boolean }) => {
  const theme = useTheme();

  return [
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `ticker-data ${data?.positionStatus}`),
      cellRenderer: ({ data }) => {
        return (
          <div className="cell-item">
            <div className="flex info-wrap gap-3">
              <div className="company-info flex flex-col">
                <div className="flex items-center gap-2">
                  <Link href={`/quote/${data.symbol}`}>
                    <span className="symbol font-semibold">{data.symbol}</span>{' '}
                  </Link>
                  <span className="text-sm">{data?.exit_price > 0 ? ' - Closed' : null}</span>
                </div>
                <div className="industry">{data.name}</div>
              </div>
            </div>
          </div>
        );
      },
      field: 'symbol',
      headerName: 'Ticker',
      sortEnabled: true,
    },
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `font-semibold ${data?.positionStatus}`),
      field: 'date_added',
      headerName: 'Date Added',
      sortEnabled: true,
    },
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `font-semibold ${data?.positionStatus}`),
      cellRenderer: ({ data }) => {
        return (
          <div className="cell-item">
            <div className="flex flex-col">
              {data.lastTradePrice ? `$${Number(data.lastTradePrice).toFixed(2)}` : '-'}
              <div className="flex gap-1">
                <span
                  className="change"
                  style={{
                    color: getColorByValue(theme, Number(data.change).toFixed(2)),
                  }}
                  title={Number(data.change).toFixed(2)?.toString()}
                >
                  ${Number(data.change).toFixed(2) ?? '– '}
                </span>
                <span
                  className="change-percent"
                  style={{
                    backgroundColor: `${getColorByValue(theme, Number(data.changePercent).toFixed(2))}20`,
                    color: getColorByValue(theme, Number(data.changePercent).toFixed(2)),
                  }}
                  title={Number(data.changePercent).toFixed(2)?.toString()}
                >
                  {Number(data.changePercent).toFixed(2) ?? '– '}%
                </span>
              </div>
            </div>
          </div>
        );
      },
      field: 'lastTradePrice',
      headerName: 'Price',
      sortEnabled: true,
    },
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `font-semibold ${data?.positionStatus}`),
      field: 'entry_price',
      headerName: 'Entry',
      sortEnabled: true,
    },
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `font-semibold ${data?.positionStatus}`),
      field: 'limit_price',
      headerName: 'Buy-Up-To',
      sortEnabled: true,
    },
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `font-semibold ${data?.positionStatus}`),
      cellRenderer: ({ data }) => {
        return (
          <div
            style={{
              color: getColorByValue(theme, Number(data.current_return).toFixed(2)),
            }}
          >
            {data?.current_return}%
          </div>
        );
      },
      field: 'current_return',
      headerName: 'Return',
      sortEnabled: true,
    },
    {
      cellClass: ({ data }) => getCellClass(isPaywallActive, `font-semibold ${data?.positionStatus}`),
      cellRenderer: ({ data }) => {
        return formatPriceWithCurrency(data?.exit_price, 'USD');
      },
      field: 'exit_price',
      headerName: 'Exit Price',
      sortEnabled: true,
    },
  ];
};

import React from 'react';
import styled from '@benzinga/themetron';
import { AppStoreNames } from './footerEntity';

interface Props {
  icon: JSX.Element;
  name: AppStoreNames;
  storeUrl: string;
}

const NewAppStoreButton: React.FC<Props> = ({ icon, name, storeUrl }) => {
  const dataAction =
    name === 'apple-app-store' ? 'Download on Apple App Store Click' : 'Download on Google Play Store Click';

  return (
    <StyledAnchor
      aria-label="Click to download the Benzinga app"
      className={`${name}-button-wrapper button-wrapper`}
      data-action={dataAction}
      href={storeUrl}
    >
      {icon}
    </StyledAnchor>
  );
};

export default NewAppStoreButton;

const StyledAnchor = styled.a`
  &.button-wrapper {
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140px;
    -webkit-transition: all 200ms ease;
    transition: all 200ms ease;
    height: 50px;
    box-sizing: border-box;
  }
`;

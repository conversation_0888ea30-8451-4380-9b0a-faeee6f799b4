import React from 'react';
import { map, values } from 'ramda';
import styled from '@benzinga/themetron';
import { isMobile } from '@benzinga/device-utils';
import FooterLink from '../../FooterLink';
import NewAppStoreButton from '../../NewAppStoreButton';
import { FooterStickyNav } from '../../FooterStickyNav';

import {
  aboutBenzinga,
  AppStore,
  copyrightSection,
  FooterLink as IFooterLink,
  tradingToolsAndEducation,
  socialSites,
  marketResources,
  appStores,
} from '../../NewFooterEntity';

import { NavigationFooterProps } from '../..';
import NewSocialIcon from '../../NewSocialIcon';
import SocialIcon from '../../SocialIcon';
import RingTheBellForm from '../../../RingTheBellForm';

export const NewDefaultFooter: React.FC<NavigationFooterProps> = ({
  showAppList = true,
  showFooterLinksTable = true,
  showStickyFooter,
  socialMedia = {},
}) => {
  const year = new Date().getFullYear();

  const renderFooterLinks = (links: IFooterLink[]) =>
    links.map(({ label, url }) => <FooterLink key={label} label={label} url={url} />);

  const socialSitesList =
    Object.keys(socialMedia).length > 0
      ? Object.keys(socialMedia).map((site: string) => {
          const socialSitesProps = {
            ...socialSites[site],
            ...socialMedia[site],
          };

          return <SocialIcon {...socialSitesProps} key={socialSitesProps.name} />;
        })
      : Object.keys(socialSites).map((site: string) => {
          const socialSitesProps = socialSites[site];
          return <NewSocialIcon {...socialSitesProps} key={socialSitesProps.name} />;
        });

  const AppList = map(
    (appStore: AppStore) => (
      <NewAppStoreButton icon={appStore.icon} key={appStore.name} name={appStore.name} storeUrl={appStore.link} />
    ),
    values(appStores),
  );

  return (
    <>
      <div className={isMobile() ? 'footer has-sticky-nav' : 'footer'}>
        <div className="new-footer-top-section">
          {showFooterLinksTable && (
            <Row className="footer-row">
              <StyledColumn>
                <h3>Connect With Us</h3>
                <div className="new-logo-and-socials-wrapper">
                  <div className="new-social-sites-list">{socialSitesList}</div>
                </div>
                {showAppList && <div className="app-store-wrapper">{AppList}</div>}
                <h3 className="about-bz">About Benzinga</h3>
                <ul>{renderFooterLinks(aboutBenzinga)}</ul>
              </StyledColumn>
              <StyledColumn>
                <h3>Market Resources</h3>
                <ul>{renderFooterLinks(marketResources)}</ul>
              </StyledColumn>
              <StyledColumn>
                <h3>Trading Tools &amp; Education</h3>
                <ul>{renderFooterLinks(tradingToolsAndEducation)}</ul>
              </StyledColumn>
              <StyledColumn>
                <h3>Ring the Bell</h3>
                <div className="newsletter-form-container">
                  <div className="newsletter-description">
                    <p>
                      A newsletter built for market enthusiasts by market enthusiasts. Top stories, top movers, and
                      trade ideas delivered to your inbox every weekday before and after the market closes.
                    </p>
                  </div>
                  <RingTheBellForm />
                </div>
              </StyledColumn>
            </Row>
          )}
        </div>
        <div className="new-footer-bottom-section footer-bottom-common">
          <StyledFooterSection>
            <ul>{renderFooterLinks(copyrightSection)}</ul>
          </StyledFooterSection>
        </div>
        <div className="copyright-bar">&copy; {year} Benzinga | All Rights Reserved</div>
      </div>
      {isMobile() && showStickyFooter && <FooterStickyNav />}
    </>
  );
};

export default NewDefaultFooter;

const Row = styled.div`
  &.footer-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, 300px);
    width: 70%;
    margin: auto;
    justify-content: center;
    margin-top: 3rem;
    padding-top: 0.5rem;
    padding-bottom: 1rem;

    @media screen and (min-width: 480px) {
      justify-content: flex-start;
      width: 80%;
    }

    @media screen and (min-width: 768px) {
      grid-template-columns: repeat(auto-fit, 250px);
      grid-gap: 30px;
      width: 70%;
      justify-content: center;
    }

    @media screen and (min-width: 1024px) {
      grid-template-columns: repeat(auto-fit, 300px);
      grid-gap: 0;
      width: 85%;
      justify-content: center;
    }
  }
`;

const StyledFooterSection = styled.div`
  margin: auto;
  ul {
    list-style: none;
    flex-wrap: wrap;
    justify-content: center;
    display: flex;
    align-items: center;
    padding: 0;
    li {
      font-size: ${({ theme }) => theme.fontSize.lg};
      margin: 0 15px;
      font-family: Arial, sans-serif;

      > a {
        color: ${({ theme }) => theme.colorPalette.gray350};
        text-decoration: none;
        transition: all 0.3s ease;

        &:hover {
          color: ${({ theme }) => theme.colorPalette.white};
          padding-left: 5px;
        }
      }
    }
  }
`;

const StyledColumn = styled.div`
  display: flex;
  flex-direction: column;
  text-align: left;
  padding: 0 12px;
  margin-bottom: 2rem;

  @media screen and (min-width: 768px) {
    max-width: 300px;
  }
  @media screen and (max-width: 1024px) {
    width: 300px;
  }

  h3 {
    display: flex;
    font-family: Arial, sans-serif;
    color: ${({ theme }) => theme.colorPalette.white};
    font-size: ${({ theme }) => theme.fontSize['xl-plus']};
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    margin: 0;
    margin-bottom: 24px;
    position: relative;

    @media screen and (min-width: 480px) {
      margin-top: 0;
    }

    @media screen and (min-width: 768px) {
      font-size: ${({ theme }) => theme.fontSize['xl-plus']};
    }
  }
  h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    width: 50px;
    height: 2px;
    background-color: ${({ theme }) => theme.colorPalette.blue1000};
  }
  .about-bz {
    margin-top: 30px;
    margin-right: 0px;
    margin-left: 0px;
    margin-bottom: 24px;
  }

  ul {
    list-style: none;
    padding: 0;
    li {
      font-size: ${({ theme }) => theme.fontSize.lg};
      font-family: Arial, sans-serif;
      transition: all 0.3s ease;

      &:hover {
        padding-left: 5px;
      }

      > a {
        color: ${({ theme }) => theme.colorPalette.gray350};
        text-decoration: none;

        &:hover {
          color: ${({ theme }) => theme.colorPalette.white};
        }
      }
    }
  }
`;

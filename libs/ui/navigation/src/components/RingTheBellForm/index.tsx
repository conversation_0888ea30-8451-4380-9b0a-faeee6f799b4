'use client';
import React, { ChangeEvent, FormEvent, useState } from 'react';
import { SessionContext } from '@benzinga/session-context';
import { ContentManager } from '@benzinga/content-manager';
import { TrackingManager } from '@benzinga/tracking-manager';

const pub_id = 'pub_c03f46e3-b180-439f-8cf4-103cbf2ac567';

const RingTheBellForm: React.FC = () => {
  const session = React.useContext(SessionContext);
  const [email, setEmail] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [resSuccessMsg, setResSuccessMsg] = useState<string>('');

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError('');
    try {
      if (!email || !/^[^@]+@\w+(\.\w+)+\w$/.test(email)) {
        setError('Please enter a valid email address');
      }
      const subscribe = await session.getManager(ContentManager).subscribeToNewsletter({
        email: email,
        pub_id,
      });

      if (subscribe.ok?.status === 'fail' || subscribe.err) {
        setError('Failed to subscribe to newsletter.');
      } else {
        session.getManager(TrackingManager).trackFormEvent('submit', 'beehiiv-form-sign-up', {
          form_id: pub_id,
          form_type: 'Beehiiv Form',
        });
        setResSuccessMsg('Thank you for subscribing to our newsletter!');
        setEmail('');
      }
    } catch (err) {
      setError('Failed to subscribe to newsletter. Please try again later.');
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setError('');
    setResSuccessMsg('');
  };
  return (
    <form className="newsletter-form" onSubmit={handleSubmit}>
      <input
        className={error ? 'input-error' : resSuccessMsg ? 'input-success' : ''}
        onChange={handleChange}
        placeholder="Your Email Address"
        required
        type="email"
        value={email}
      />
      {error && <div className="error-message">{error}</div>}
      {resSuccessMsg && <div className="success-message">{resSuccessMsg}</div>}
      <button type="submit">Subscribe</button>
    </form>
  );
};

export default RingTheBellForm;

'use client';
import React, { startTransition } from 'react';
import { Collapse, ErrorBoundary, Icon } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import { Search } from '@benzinga/search-ui';
import { QuotesList } from './QuotesList';
import { NavigationHeaderProps } from '../../entities';
import { Menu, MenuItemLink } from './Menu';
import { MenuInterface, MenuItem } from './interfaces';
import { ButtonBzPro } from '../BenzingaProButton';
import { DefaultQuotesList } from './_mockMenu';
import { GlobalMenu } from '../GlobalMenu';
import { AccountMenu } from '../AccountMenu';
import classnames from 'classnames';
import Hooks, { NoFirstRender } from '@benzinga/hooks';
import i18n from '@benzinga/translate';
import { useTranslation } from 'react-i18next';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { faChevronRight } from '@fortawesome/pro-solid-svg-icons/faChevronRight';
import { faChevronLeft } from '@fortawesome/pro-solid-svg-icons/faChevronLeft';
import { faBars } from '@fortawesome/pro-light-svg-icons/faBars';
import { faXmark } from '@fortawesome/pro-light-svg-icons/faXmark';
import type { BreakingNews } from '@benzinga/basic-news-manager';
import { BreakingNewsBanner } from '../BreakingNewsBanner';
import { NavigationLogo } from './NavigationLogo';
import { appEnvironment, appName } from '@benzinga/utils';
import { useRouter } from 'next/navigation';
import { AdBannerFallback } from './RotatingBannerFallback';

const RotatingBanner = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RotatingBanner };
  }),
);

const RaptiveHeaderBanner = React.lazy(() =>
  import('./RaptiveHeaderBanner').then(module => {
    return { default: module.RaptiveHeaderBanner };
  }),
);

export interface HeaderMobileProps {
  breakingNews?: BreakingNews[];
  AboveHeaderBlock?: React.ReactNode;
}

type IExtendedMenu = Partial<MenuInterface> & { links?: MenuItem[] };
type PathInterface = [undefined, ...IExtendedMenu[]];

const constants = {
  BANNER_HEIGHT: 48,
  MAIN_BLOCK_HEIGHT: 49,
  QUOTE_BAR_HEIGHT: 52,
  RAPTIVE_BANNER_HEIGHT: 50,
  ROTATING_BANNER_HEIGHT: 80,
  SEARCH_BAR_HEIGHT: 61,
};

export const HeaderMobile = ({
  AboveHeaderBlock,
  breakingNews,
  featuredMenu,
  hideMenuBar,
  hideQuoteBar,
  logoVariant,
  marketTickers,
  menus,
  quotes,
  shouldRenderRaptiveBanner,
  showRaptiveBanner,
  showRotatingBanner,
}: NavigationHeaderProps & HeaderMobileProps) => {
  const [isMenuVisible, setIsMenuVisible] = React.useState(false);
  const [isFullScreenSearch, setIsFullScreenSearch] = React.useState(false);
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const [lastScrollPosition, setLastScrollPosition] = React.useState(0);
  const [hideRotatingBanner, setHideRotatingBanner] = Hooks.useLocalStorage('hideRotatingBanner', !showRotatingBanner);
  const hideRotatingBannerHydrated = Hooks.useHydrate(hideRotatingBanner, showRotatingBanner === false ? true : false);
  const [extendedMenu, setExtendedMenu] = React.useState<IExtendedMenu | undefined>();
  const [path, setPath] = React.useState<PathInterface>([undefined]);

  const raptiveHeaderBannerRef = React.useRef<HTMLDivElement>(null);

  const isLoggedIn = Hooks.useHydrate(useIsUserLoggedIn(), false);

  const router = useRouter();

  const [heights, setHeights] = React.useState<{
    bannerHeight: number;
    mainBlockHeight: number;
    quoteBarHeight: number;
    searchBarHeight: number;
    raptiveBannerHeight: number;
    rotatingBannerHeight: number;
  }>({
    bannerHeight: 0,
    mainBlockHeight: constants.MAIN_BLOCK_HEIGHT,
    quoteBarHeight: constants.QUOTE_BAR_HEIGHT,
    raptiveBannerHeight: constants.RAPTIVE_BANNER_HEIGHT,
    rotatingBannerHeight: constants.ROTATING_BANNER_HEIGHT,
    searchBarHeight: constants.SEARCH_BAR_HEIGHT,
  });

  const { bannerHeight, mainBlockHeight, quoteBarHeight, raptiveBannerHeight, rotatingBannerHeight, searchBarHeight } =
    heights;

  const offset = hideQuoteBar
    ? mainBlockHeight
    : mainBlockHeight +
      quoteBarHeight +
      searchBarHeight +
      bannerHeight +
      (hideRotatingBannerHydrated ? 0 : 80) +
      (showRaptiveBanner ? raptiveBannerHeight : 0);

  React.useEffect(() => {
    const mainBlockHeight =
      Number(document.getElementById('mobile-main-block')?.clientHeight) || constants.MAIN_BLOCK_HEIGHT;
    const quoteBarHeight = !hideQuoteBar ? Number(document.getElementById('mobile-quote-bar')?.clientHeight) : 0;
    const searchBarHeight = !hideQuoteBar ? Number(document.getElementById('mobile-search-bar')?.clientHeight) : 0;
    const bannerHeight = 0;

    const raptiveBannerHeight =
      Number(raptiveHeaderBannerRef?.current?.clientHeight) || constants.RAPTIVE_BANNER_HEIGHT;

    startTransition(() => {
      setHeights({
        bannerHeight,
        mainBlockHeight,
        quoteBarHeight,
        raptiveBannerHeight,
        rotatingBannerHeight: constants.ROTATING_BANNER_HEIGHT,
        searchBarHeight,
      });
    });
  }, [breakingNews, hideQuoteBar]);

  React.useEffect(() => {
    if (hideRotatingBanner) {
      startTransition(() => {
        setHeights(prevState => {
          return {
            ...prevState,
            streamlineBannerHeight: 0,
          };
        });
      });
    }
  }, [hideRotatingBanner]);

  Hooks.useBodyLock(isMenuVisible || isFullScreenSearch);

  const showBreakingNewsBanner = () => {
    setHeights(prevState => {
      return {
        ...prevState,
        bannerHeight: constants.BANNER_HEIGHT,
      };
    });
  };

  React.useEffect(() => {
    if (window?.scrollY > 0) {
      if (breakingNews && breakingNews?.length > 0) {
        showBreakingNewsBanner();
      }
    }
  }, [breakingNews]);

  Hooks.useEffectDidMount(() => {
    window?.addEventListener(
      'scroll',
      () => {
        if (breakingNews && breakingNews?.length > 0) {
          showBreakingNewsBanner();
        }
      },
      { once: true },
    );
  });

  Hooks.useEventListener('scroll', () => {
    if (!hideQuoteBar) {
      const scrollDirection = lastScrollPosition > window.scrollY ? 0 : 1;

      if ((isCollapsed && window.scrollY <= offset) || scrollDirection === 0) {
        setIsCollapsed(false);
      } else if (!isCollapsed && window.scrollY > offset && scrollDirection === 1) {
        setIsCollapsed(true);
      }

      setLastScrollPosition(window.scrollY);
    }
  });

  const handleLogout = React.useCallback(() => {
    let next = window.location.href;
    if (next.includes('/account')) {
      next = window.location.origin;
    }
    next = encodeURIComponent(next);

    const url = new URL(`${window.location.origin}/logout?next=${next}`);
    router.push(url.toString());
  }, [router]);

  const handleToggleMenuVisible = () => {
    setIsMenuVisible(!isMenuVisible);
    const footerAd = document.getElementById('AdThrive_Footer_1_phone');
    if (footerAd) {
      footerAd.style.display = isMenuVisible ? 'flex' : 'none';
    }
  };

  const handleToggleFullScreenSearch = (value?: boolean) => {
    const newValue = value ? value : !isFullScreenSearch;
    setIsFullScreenSearch(newValue);
  };

  const primaryMenu = menus?.primary ?? [];
  const secondaryMenu = menus?.secondary.filter(menuItem => menuItem?.deviceType !== 'desktop') ?? [];

  const newSecondaryMenu = secondaryMenu.filter(item => item);
  const combinedMenu = [...primaryMenu, ...newSecondaryMenu];

  const handleClickMenuItem = (menu: MenuInterface | IExtendedMenu) => {
    if (menu.subnav) {
      setExtendedMenu(menu);
      const newPath: PathInterface = [...path];
      newPath.push(menu);
      setPath(newPath);
    } else if (menu.groups) {
      setExtendedMenu(menu as IExtendedMenu);
      const newPath: PathInterface = [...path];
      newPath.push(menu as IExtendedMenu);
      setPath(newPath);
    }
  };

  const handleBackButtonClick = () => {
    if (Array.isArray(path)) {
      const currentIndex = path.findIndex(pathObject => pathObject === extendedMenu);
      const previousPath = path[currentIndex - 1];
      setExtendedMenu(previousPath);
      if (typeof previousPath === 'undefined') {
        setExtendedMenu(undefined);
        setPath([undefined]);
      }
    }
  };

  const isIndiaApp = appEnvironment().isApp(appName.india);

  const handleDismissAdBanner = () => {
    setHideRotatingBanner(true);
  };

  const mobileHeaderWrapperOffset =
    mainBlockHeight +
    quoteBarHeight +
    (hideRotatingBannerHydrated ? 0 : rotatingBannerHeight) +
    (showRaptiveBanner ? raptiveBannerHeight : 0);

  const withRaptiveAdBannerEnabledOffset = constants.MAIN_BLOCK_HEIGHT + constants.QUOTE_BAR_HEIGHT;

  return (
    <MobileHeaderContainer $hideQuoteBar={hideQuoteBar} $offset={offset}>
      <MobileHeaderWrapper
        $isCollapsed={isCollapsed}
        $offset={showRaptiveBanner ? withRaptiveAdBannerEnabledOffset : mobileHeaderWrapperOffset}
        $raptiveBannerHeight={raptiveBannerHeight}
        $showRaptiveBanner={showRaptiveBanner}
        className={classnames('z-[999]', {
          fullscreen: isFullScreenSearch,
        })}
        id="navigation-header"
      >
        {showRotatingBanner && (
          <ErrorBoundary name="rotating-banner">
            <NoFirstRender fallback={AdBannerFallback}>
              {!hideRotatingBannerHydrated && (
                <React.Suspense fallback={AdBannerFallback}>
                  <RotatingBanner close={handleDismissAdBanner} fallback={AdBannerFallback} />
                </React.Suspense>
              )}
            </NoFirstRender>
          </ErrorBoundary>
        )}
        {shouldRenderRaptiveBanner && (
          <React.Suspense fallback={<div />}>
            <RaptiveHeaderBanner isHidden={!showRaptiveBanner} ref={raptiveHeaderBannerRef} />
          </React.Suspense>
        )}
        <div className="left-0 right-0 top-0 z-[999]" id="mobile-main-block">
          {AboveHeaderBlock && (
            <div
              className="bg-white relative"
              style={{
                zIndex: 50,
              }}
            >
              {AboveHeaderBlock}
            </div>
          )}
          <div
            className="mobile-header flex items-center justify-between relative"
            style={{
              transition: 'all linear 0.3s',
              zIndex: 20,
            }}
          >
            {!hideMenuBar && (
              <div className="menu-icon-wrapper" onClick={handleToggleMenuVisible}>
                {isMenuVisible ? <Icon icon={faXmark} /> : <Icon icon={faBars} />}
              </div>
            )}
            <ErrorBoundary name="benzinga-logo-wrapper">
              <NavigationLogo deviceType="mobile" logoVariant={logoVariant} />
            </ErrorBoundary>
            <div className="flex h-12 items-center text-blue-300">
              <GlobalMenu />
              {!isIndiaApp && <AccountMenu deviceType="mobile" isLoggedIn={isLoggedIn} onLogout={handleLogout} />}
            </div>
          </div>
        </div>
        {isMenuVisible ? (
          <div
            className="mobile-menu-wrapper bottom-0 fixed flex flex-col left-0 overflow-y-auto right-0 z-50"
            style={{
              top: `${offset}px`,
            }}
          >
            <MenuWrapper>
              {extendedMenu ? (
                <div className="more-items-container">
                  <button className="back-button-container" onClick={handleBackButtonClick}>
                    <Icon className="chevron-left-icon" icon={faChevronLeft} />
                    <span>{extendedMenu.label}</span>
                  </button>
                  {extendedMenu?.label && (extendedMenu?.href || extendedMenu.logged_in_href) && (
                    <MenuItemLink
                      className="menu-item-link 2"
                      href={isLoggedIn && extendedMenu.logged_in_href ? extendedMenu.logged_in_href : extendedMenu.href}
                      key={extendedMenu.label}
                    >
                      {extendedMenu.label}
                    </MenuItemLink>
                  )}
                  {Array.isArray(extendedMenu?.subnav?.links) &&
                    extendedMenu?.subnav?.links.map(link =>
                      link?.subnav?.links && Array.isArray(link?.subnav?.links) ? (
                        <div className="menu-item-group">
                          <Collapse defaultCollapsed={true} header={link.label}>
                            <>
                              <MenuItemLink
                                className="menu-item-link"
                                href={isLoggedIn && link.logged_in_href ? link.logged_in_href : link.href}
                                key={link.label}
                              >
                                {link.label}
                              </MenuItemLink>
                              {link?.subnav?.links.map(Sublink => (
                                <MenuItemLink
                                  className="menu-item-link"
                                  href={isLoggedIn && Sublink.logged_in_href ? Sublink.logged_in_href : Sublink.href}
                                  key={Sublink.label}
                                >
                                  {Sublink.label}
                                </MenuItemLink>
                              ))}
                            </>
                          </Collapse>
                        </div>
                      ) : (
                        <MenuItemLink
                          className="menu-item-link 3"
                          href={isLoggedIn && link.logged_in_href ? link.logged_in_href : link.href}
                          key={link.label}
                        >
                          {link.label}
                        </MenuItemLink>
                      ),
                    )}
                  {Array.isArray(extendedMenu?.groups) &&
                    extendedMenu.groups.map(group => (
                      <div className="menu-item-group">
                        <Collapse defaultCollapsed={true} header={group.label}>
                          {group.links.map(link => (
                            <MenuItemLink
                              className="menu-item-link 4"
                              href={isLoggedIn && link.logged_in_href ? link.logged_in_href : link.href}
                              key={link.label}
                            >
                              {link.label}
                            </MenuItemLink>
                          ))}
                        </Collapse>
                      </div>
                    ))}
                  {extendedMenu.label === 'Tools' && (
                    <ButtonBzPro
                      label="Free Benzinga Pro Trial"
                      url="https://pro.benzinga.com/register/?utm_source=tools-bz-pro-free-trial"
                      variant="trial"
                    />
                  )}
                </div>
              ) : (
                combinedMenu.map((menu: MenuInterface, mainMenuKey: number) => {
                  if (menu.subnav || menu.groups) {
                    return (
                      <MenuItemLink
                        as="div"
                        className="menu-item-link 5"
                        key={mainMenuKey}
                        onClick={() => handleClickMenuItem(menu)}
                      >
                        {menu.label}
                        <Icon className="chevron-right-icon" icon={faChevronRight} />
                      </MenuItemLink>
                    );
                  } else {
                    return <Menu key={mainMenuKey} menu={menu} />;
                  }
                })
              )}
              {!isIndiaApp && (
                <>
                  <FeaturedMenuItems items={featuredMenu} />
                  <MenuItemLink
                    className="premium-button"
                    href="https://www.benzinga.com/go/insider-report?utm_source=research-redirect"
                    target="_blank"
                  >
                    <div>Research Services</div>
                  </MenuItemLink>
                </>
              )}
            </MenuWrapper>
          </div>
        ) : null}
        {!hideQuoteBar && !isFullScreenSearch ? (
          <div
            className="mobile-quote-bar overflow-x-scroll"
            id="mobile-quote-bar"
            style={{
              marginTop: '-.5px',
            }}
          >
            <QuotesList quotes={quotes} tickers={marketTickers ?? DefaultQuotesList} />
          </div>
        ) : null}
        {!hideQuoteBar ? (
          <div
            className={classnames('search-wrapper transition', {
              fullscreen: isFullScreenSearch,
            })}
            id="mobile-search-bar"
            style={{
              marginTop: '-.5px',
              transition: 'all linear 0.3s',
            }}
          >
            <Search
              isFullScreenSearch={isFullScreenSearch}
              onToggle={handleToggleFullScreenSearch}
              placeholder="Search Tickers, Companies or News..."
            />
          </div>
        ) : null}
        {!hideQuoteBar && !!bannerHeight && (
          <ErrorBoundary name="breaking-news-banner">
            {breakingNews && breakingNews?.length > 0 && <BreakingNewsBanner data={breakingNews} />}
          </ErrorBoundary>
        )}
      </MobileHeaderWrapper>
    </MobileHeaderContainer>
  );
};

const FeaturedMenuItems: React.FC<{ items?: MenuItem[] }> = ({ items }) => {
  const { t } = useTranslation('common', { i18n });
  const isLoggedIn = Hooks.useHydrate(useIsUserLoggedIn(), false);

  if (!items) return null;

  return (
    <ul className="mt-auto relative z-50">
      {items?.map((item, key) => {
        return (
          <SubNavigationItem key={key}>
            <SubNavigationLink href={isLoggedIn && item.logged_in_href ? item.logged_in_href : item.href}>
              {t(item.title)}
            </SubNavigationLink>
          </SubNavigationItem>
        );
      })}
      <div className="bz-pro-logo border-blue-800 border-t-2 flex flex-shrink-0 text-white w-full">
        <ButtonBzPro
          label="Get Benzinga Pro"
          url="https://www.benzinga.com/pro/register?adType=homepagebluebutton&utm_source=homepage-blue-button&utm_campaign=14daytrial"
          variant="with-arrow"
        />
      </div>
    </ul>
  );
};

const MobileHeaderWrapper = styled.div<{
  $offset?: number;
  $isCollapsed?: boolean;
  $showRaptiveBanner?: boolean;
  $raptiveBannerHeight?: number;
}>`
  position: fixed;
  top: ${props => (props.$isCollapsed ? `-${props.$offset}px` : '0')};
  left: 0;
  right: 0;
  width: 100%;
  transition: all linear 0.12s;

  .raptive-header-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 101;
    width: 100%;

    .raptive-ad-wrapper {
      margin: 0;
      min-height: 0;
      min-width: 320px;
    }
  }

  .mobile-header,
  .mobile-menu-wrapper,
  .mobile-quote-bar {
    background-color: #08244d;
  }

  .mobile-header {
    border-bottom: 1px solid #36537e;
    margin-top: ${props => (props.$showRaptiveBanner ? `${props.$raptiveBannerHeight}px` : '0')};
  }

  .menu-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    color: ${({ theme }) => theme.colorPalette.gray200};
    font-size: ${({ theme }) => theme.fontSize.xl};
  }

  &.fullscreen {
    .account-menu {
      pointer-events: none;
    }
  }

  .search-wrapper {
    background: linear-gradient(180deg, #082132 0%, #1d446a 100%);
    width: 100%;
    z-index: 30;

    &.fixed {
      position: fixed;
      top: ${props => (props.$offset ? `${props.$offset}px` : '48px')};
    }

    &.fullscreen {
      position: fixed;
      background: #242424e0;
      z-index: 999;
      height: 100vh;

      .searchbar-wrapper {
        /* background-color: #ffffff; */
        padding: 0;

        .search-input-container {
          border-bottom: 1px solid #a4a4a4;
          padding: 8px 12px;
          background-color: #1d446a;
        }
        .search-input-wrapper {
          background-color: white;
          border-radius: 4px;
        }
      }

      .search-dropdown {
        border-radius: unset;
        border: none;
        box-shadow: none;
        position: unset;
      }

      .search-result-wrapper {
        border-radius: unset;
      }
    }

    .searchbar-wrapper {
      padding: 8px 12px;
      background-color: transparent;
      font-weight: bold;
      line-height: 15px;
      .search-icon {
        width: 32px;
      }
      input {
        font-size: ${({ theme }) => theme.fontSize.base};
        height: 45px;
      }
      .searchbar-container {
        background-color: white;
        border-radius: 2px;
      }
      @media screen and (max-width: 800px) {
        input {
          font-size: 16px;
        }
      }
    }
  }

  .mobile-menu-wrapper {
    z-index: 999999;
    margin-top: -1.5px;
    .premium-button {
      color: white;
      line-height: 40px;
      padding: 4px 10px;
      position: fixed;
      height: min-content;
      bottom: 0px;
      z-index: 10000;
      background-color: #062b4c;

      div {
        background-color: ${({ theme }) => theme.colorPalette.orange500};
        border-radius: 10px;
        padding: 0px 10px;
        width: 100%;
        border-radius: 5px;
        margin: 2px 0px;
        text-align: center;
      }

      svg {
        font-size: 20px;
        padding-right: 5px;
      }

      &:hover {
        div {
          background-color: ${({ theme }) => theme.colorPalette.orange400};
          border-bottom: 3px solid ${({ theme }) => theme.colorPalette.orange600};
        }
      }
    }
  }

  .bz-pro-logo {
    height: 60px;
    background: #062b4c;
    margin-bottom: 60px;
    a {
      border-top: solid 1px #666;
      padding: 1rem;
      width: 100%;
    }
  }
  .mobile-logo-wrapper {
    margin: 0;
    padding: 0;
    width: 205px;
    margin-right: auto;

    .benzinga-crypto-logo-wrapper {
      margin-top: 5px;
    }

    .benzinga-logo {
      height: 18.2px;
      width: 134.35px;

      &.reactive-holiday-logo {
        height: 30px;
      }
    }
  }
`;

const MobileHeaderContainer = styled.div<{ $offset?: number; $hideQuoteBar?: boolean }>`
  margin-top: ${props => (props.$offset ? `${props.$offset}px` : props.$hideQuoteBar ? `48px` : '162px')};
  width: 100%;
`;

const MenuWrapper = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  .menu-wrapper a {
    display: block;
    &:hover {
      background-color: ${({ theme }) => theme.colorPalette.blue800};
      color: #ffffff;
    }

    &.trading-school {
      &:hover {
        color: ${({ theme }) => theme.colorPalette.white} !important;
      }
    }
  }
  .more-items-container {
    background-color: #07244e;
    display: flex;
    flex-direction: column;
    .menu-item-link {
      background-color: #07244e;
    }
    .menu-item-group {
      .collapse-container {
        border: none;
        border-radius: 0;
      }
      .collapse-panel-header {
        background: none;
        border: none;
        color: #fff;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: center;
        padding: 0 0.65rem;
        height: 40px;
        font-weight: ${({ theme }) => theme.fontWeight.bold};
      }
      .collapse-panel-content {
        padding: 0 1rem;
      }
    }
  }
  .back-button-container {
    display: flex;
    align-items: center;
    color: #fff;
    font-weight: ${({ theme }) => theme.fontWeight.bold};
    background-color: #0a1d3a;
    border-bottom: 1px solid #36537e;
    padding: 0.5rem 0.85rem;
    width: 100%;

    .chevron-left-icon {
      margin-right: 1.5rem;
      margin-bottom: 0.15rem;
    }
  }
  .more-items-header {
    font-weight: ${({ theme }) => theme.fontWeight.bold};
    font-size: ${({ theme }) => theme.fontSize.xl};
    color: #ffffff;
  }
  .menu-item-link {
    height: unset;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.85rem;
    line-height: 40px;

    &:hover {
      color: #4387e6;
    }

    &.invest-in-art-button {
      //background: rgba(255, 255, 255, 0.19);
      //box-shadow: 0 4px 30px rgb(0 0 0 / 10%);
      backdrop-filter: blur(4.9px);
      -webkit-backdrop-filter: blur(4.9px);
      border-top: 1px solid rgba(255, 255, 255, 0.18);
      line-height: 45px;
      //background: linear-gradient(to right, rgba(255, 255, 255, 0.19) 20%, rgba(255, 255, 255, 0) 90%);
      box-shadow: 0 7px 25px 0 rgb(0 0 0 / 20%);
      background: linear-gradient(-90deg, rgba(255, 255, 255, 0.02), rgb(157 157 157 / 29%));
      text-align: center;
    }
  }
`;

const SubNavigationLink = styled.a`
  color: ${({ theme }) => theme.colorPalette.white};
  text-transform: uppercase;
  font-size: ${({ theme }) => theme.fontSize.base};
  line-height: 20px;
  padding: 16px 14px;
  display: block;
`;

const SubNavigationItem = styled.li`
  border-left: 2px solid ${({ theme }) => theme.colors.danger};
  background: linear-gradient(
    -90deg,
    ${({ theme }) => theme.colors.danger}33 0%,
    ${({ theme }) => theme.colors.danger}00 102.07%
  );
  width: 100%;
  display: block;

  &:hover {
    background: ${({ theme }) => theme.colors.danger}40;
  }

  &:nth-child(2) {
    border-left-color: ${({ theme }) => theme.colors.warning};
    background: linear-gradient(
      -90deg,
      ${({ theme }) => theme.colors.warning}33 0%,
      ${({ theme }) => theme.colors.warning}00 102.07%
    );

    &:hover {
      background: ${({ theme }) => theme.colors.warning}40;
    }
  }

  &:nth-child(3) {
    border-left-color: ${({ theme }) => theme.colors.successDark};
    background: linear-gradient(
      -90deg,
      ${({ theme }) => theme.colors.successDark}33 0%,
      ${({ theme }) => theme.colors.successDark}00 102.07%
    );

    &:hover {
      background: ${({ theme }) => theme.colors.successDark}40;
    }
  }

  &:nth-child(4) {
    border-left-color: ${({ theme }) => theme.colors.success};
    background: linear-gradient(
      -90deg,
      ${({ theme }) => theme.colors.success}33 0%,
      ${({ theme }) => theme.colors.success}00 102.07%
    );

    &:hover {
      background: ${({ theme }) => theme.colors.success}40;
    }
  }
`;

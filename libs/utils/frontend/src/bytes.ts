import { isNilOrEmpty } from '@benzinga/utils';

export const getSizeInBytes = value => {
  if (isNilOrEmpty(value)) return;
  if (Array.isArray(value)) {
    const bytes = new TextEncoder().encode(JSON.stringify(value)).length;
    const units = ['Bytes', 'KB', 'MB', 'GB'];
    let i = 0,
      size = bytes;
    while (size >= 1024 && i < units.length - 1) {
      size /= 1024;
      i++;
    }
    return `${size.toFixed(2)} ${units[i]}`;
  }
  switch (typeof value) {
    case 'number':
      return value;
    case 'string':
      return new TextEncoder().encode(value).length.toFixed(2);
    case 'object':
      if (value === null) {
        return 0;
      } else if (Array.isArray(value)) {
        return value.reduce((acc, item) => acc + getSizeInBytes(item), 0);
      } else if (typeof value === 'object') {
        return Object.values(value).reduce((acc, item) => acc + getSizeInBytes(item), 0);
      }
      break;
  }
  return 0;
};

'use client';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import React, { useEffect } from 'react';

interface Props {
  containerRef: React.MutableRefObject<HTMLDivElement | null>;
}

export const IMPRESSION_ELEMENT_ATTRIBUTE = 'data-impression';

export const useImpressionTracker = ({ containerRef }: Props) => {
  const session = React.useContext(SessionContext);

  useEffect(() => {
    const registerImpression = (nodeID: string) => {
      const impressionSlug = `${nodeID}`;
      if (!isGlobalImpressionStored(impressionSlug)) {
        storeGlobalImpression(impressionSlug);
        session.getManager(TrackingManager).trackHeadlineEvent('viewed_inline', {
          nodeID,
        });
      }
    };

    const updateVisibleElements = () => {
      if (!containerRef.current) return;

      const children = Array.from(
        containerRef.current.querySelectorAll(`[${IMPRESSION_ELEMENT_ATTRIBUTE}]`),
      ) as HTMLElement[];

      const windowHeight = window.innerHeight;
      const buffer = 100;

      for (const child of children) {
        if (!child.hasAttribute(IMPRESSION_ELEMENT_ATTRIBUTE)) continue;

        const rect = child.getBoundingClientRect();
        if (rect.top < windowHeight + buffer && rect.bottom > -buffer) {
          const impressionId = child.getAttribute(IMPRESSION_ELEMENT_ATTRIBUTE);
          if (impressionId) {
            registerImpression(impressionId);
          }
        }
      }
    };

    updateVisibleElements();
    window.addEventListener('resize', updateVisibleElements);
    window.addEventListener('scrollend', updateVisibleElements);

    return () => {
      window.removeEventListener('resize', updateVisibleElements);
      window.removeEventListener('scrollend', updateVisibleElements);
    };
  }, [session, containerRef]);

  return containerRef;
};

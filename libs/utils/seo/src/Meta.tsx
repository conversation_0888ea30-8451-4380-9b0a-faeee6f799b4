'use client';
import React, { memo, useEffect, useState } from 'react';
import Head from 'next/head';
import { has, includes, toLower } from 'ramda';
import { Schema } from './Schema';
// import { decode } from 'html-entities';
import { appEnvironment, appName, fixedEncodeURI } from '@benzinga/utils';
import { MetaProps, UrlMeta } from './entities';
import { formatStringToValidJsonString } from './utils';

const getCss = (css: string | undefined) => css && <link as="style" href={`${css}`} rel="preload" />;

// @ToDo: Replace w/ CDN URL
const defaultImage = '/next-assets/images/schema-image-default.png';
const logoPNG = '/next-assets/images/schema-publisher-logo-benzinga.png';
const getCanonical = (canonical?: UrlMeta) => canonical && <link href={`${canonical}`.split('?')[0]} rel="canonical" />;
const getJavascript = (js: string | undefined) => js && <script src={`${js}`} type="text/javascript" />;

const organizationMetaSchema = () => {
  return {
    '@type': 'Organization',
    logo: {
      '@type': 'ImageObject',
      url: `${logoPNG}`,
    },
    name: 'Benzinga',
    url: 'https://www.benzinga.com',
  };
};

const sitelinkSchema = () => {
  const schema: Record<string, unknown> = {
    '@context': 'https://schema.org/',
    '@type': 'WebSite',
    name: 'Benzinga',
    potentialAction: {
      '@type': 'SearchAction',
      'query-input': 'required name=search_term_string',
      target: 'https://www.benzinga.com/search/fast?cx={search_term_string}',
    },
    url: 'https://www.benzinga.com/',
  };

  return schema;
};

const voiceSearchSchema = () => {
  return {
    '@context': 'https://schema.org/',
    '@type': 'WebPage',
    name: 'Benzinga',
    speakable: {
      '@type': 'SpeakableSpecification',
      xpath: ['/html/head/title', "/html/head/meta[@name='description']/@content"],
    },
    url: 'https://www.benzinga.com/',
  };
};

const decodeDescription = (text: string) => {
  // return text ? decode(text) : '';
  return text ?? '';
};

const structuredDataPage = ({
  author,
  authorURL,
  canonical,
  dateCreated,
  dateUpdated,
  description,
  image,
  isNotAccessibleForFree,
  structuredData,
  title,
}: MetaProps): string => {
  author = author || 'Benzinga';
  dateCreated = dateCreated || '2010-01-01T00:00:00Z';
  dateUpdated = dateUpdated || '2010-01-01T00:00:00Z';
  image = image || defaultImage;
  const pageType = (structuredData && has('pageType', structuredData) && structuredData.pageType) || 'WebPage';
  const keywords = (structuredData && has('keywords', structuredData) && structuredData.keywords) || [];
  const mentions = (structuredData && has('mentions', structuredData) && structuredData.mentions) || [];

  let authorURLField = '';
  if (authorURL) {
    authorURLField = `,"url": "${authorURL}"`;
  }

  let articleSection = '';
  if (structuredData && has('articleSection', structuredData) && structuredData.articleSection) {
    articleSection = `"articleSection": ${JSON.stringify(structuredData.articleSection)}`;
  }

  let speakable = '';
  if (structuredData?.speakable) {
    speakable = `"speakable": ${structuredData.speakable}`;
  }

  // ToDo: title should be truncated to 110 chars or less, truncated at word, add ellipse
  title = title?.substring(0, 105);

  const cleanQuote = (text: string | null) => {
    return text ? text.replace(/"/g, '&quot;') : '';
  };

  const convertQuoteToSingle = (text: string | null) => {
    return text ? text.replace(/"/g, `'`) : '';
  };

  // mainEntityOfPage, image - Required for AMP
  // prettier-ignore

  let aboutProperty = '';
  if (structuredData?.about) {
    aboutProperty = `"about": ${JSON.stringify(structuredData.about)}`;
  }

  let articleBodyProperty = '';
  if (structuredData?.articleBody) {
    const cleanBody = formatStringToValidJsonString(structuredData.articleBody);
    articleBodyProperty = `"articleBody": ${JSON.stringify(cleanBody)}`;
  }

  let paywallProperties = '';
  if (isNotAccessibleForFree) {
    paywallProperties = `"isAccessibleForFree": false, "hasPart": {
      "@type": "WebPageElement",
      "isAccessibleForFree": false,
      "cssSelector": ".paywall-content"
    }`;
  }

  return `{
    "@context": "http://schema.org",
    "@type": "${pageType}",
    "publisher": ${JSON.stringify(organizationMetaSchema())},
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "${canonical}"
    },
    "headline": "${cleanQuote(title)?.replace(/(\r\n|\n|\r)/gm, ' ')}",
    "url": "${canonical}",
    "dateCreated": "${dateCreated ?? null}",
    "datePublished": "${dateCreated ?? null}",
    "dateModified": "${dateUpdated ?? null}",
    "creator": {
      "@type": "Person",
      "name": "${cleanQuote(author)}"
      ${authorURLField}
    },
    "author": {
      "@type": "Person",
      "name": "${cleanQuote(author)}"
      ${authorURLField}
    },
    "description": "${convertQuoteToSingle(decodeDescription(description))}",
    "keywords": [${keywords}],
    "mentions": [${mentions}],
    "image": "${fixedEncodeURI(image.toString()) ?? null}"
    ${articleSection ? ',' + articleSection : ''}
    ${speakable ? ',' + speakable : ''}
    ${aboutProperty ? ',' + aboutProperty : ''}
    ${articleBodyProperty ? ',' + articleBodyProperty : ''}
    ${paywallProperties ? ',' + paywallProperties : ''}
  }`;
};

const getStructuredDataPageScript = (props: MetaProps) => (
  <script
    dangerouslySetInnerHTML={{
      __html: structuredDataPage(props)?.replace(/\s+/g, ' '),
    }}
    key="structuredDataPage"
    type="application/ld+json"
  />
);

const structuredDataCompany = `{
  "@context": "http://schema.org",
  "@type": "NewsMediaOrganization",
  "name": "Benzinga",
  "email": "<EMAIL>",
  "legalName": "Benzinga",
  "telephone": "************",
  "url": "https://www.benzinga.com/",
  "logo": "${logoPNG}",
  "image": "${defaultImage}",
  "address": {
    "@type": "PostalAddress",
    "addressLocality": "Detroit",
    "addressRegion": "MI",
    "postalCode": "48226",
    "streetAddress": "1 Campus Martius Suite 200",
    "addressCountry": "US"
  },
  "sameAs": [
    "https://www.facebook.com/pages/Benzingacom/159483118580?v=app_7146470109",
    "https://twitter.com/benzinga",
    "https://www.linkedin.com/company/benzinga",
    "https://plus.google.com/108838891574408087738/posts",
    "https://www.youtube.com/user/BenzingaTV"
  ],
  "brand": [
    {
      "@type": "Brand",
      "name":  "PreMarket Playbook",
      "url":   "https://www.benzinga.com/premarket"
    },
    {
      "@type": "Brand",
      "name":  "Benzinga Markets",
      "url":   "https://www.benzinga.com/markets/"
    },
    {
      "@type": "Brand",
      "name":  "Benzinga Pro",
      "url":   "https://pro.benzinga.com/"
    },
    {
      "@type": "Brand",
      "name":  "Benzinga Cloud: Data & APIs",
      "url":   "https://www.benzinga.com/apis"
    },
    {
      "@type": "Brand",
      "name":  "Benzinga Events",
      "url":   "https://www.benzinga.com/events"
    },
    {
      "@type": "Brand",
      "name":  "Benzinga Money",
      "url":   "https://www.benzinga.com/money/"
    }
  ]
}`;

const getStructuredDataCompanyScript = (
  <script
    dangerouslySetInnerHTML={{
      __html: structuredDataCompany?.replace(/\s+/g, ' '),
    }}
    key="structuredDataCompany"
    type="application/ld+json"
  />
);

export const Meta: React.FC<MetaProps> = memo(props => {
  const [metaInfo, setMetaInfo] = useState(props);
  useEffect(() => {
    if (props.trackMeta) {
      setMetaInfo(props);
    }
  }, [props]);

  const {
    author,
    canonical,
    css,
    description,
    hrefLanguage,
    image,
    js,
    ogImageHeight = 630,
    ogImageWidth = 1200,
    robots,
    shortUrl,
    symbol,
    translationMeta,
    twitterCard,
  } = props;

  let { ogDescription, ogImage, ogTitle, ogType, title, twitterDescription, twitterImage, twitterTitle } = props;
  title = title ?? 'Benzinga - Actionable Trading Ideas, Real-Time News, Financial Insight';
  ogDescription = ogDescription ?? description;
  ogImage = ogImage || defaultImage;
  ogTitle = ogTitle ?? title;
  ogType = ogType ?? 'website';
  twitterDescription = twitterDescription ?? description;
  twitterImage = twitterImage || image;
  twitterTitle = twitterTitle ?? title;

  // Set sfter og/twitter title to avoid adding
  if (!includes('benzinga', toLower(title))) {
    title = `${title} - Benzinga`;
  }
  // const metaTags = metaFactory(props);
  // const renderMetaTags = mapIndexed(tag => parse(String(tag)), metaTags);

  return (
    <>
      <Head>
        <title>{title}</title>
        {/* {renderMetaTags} */}
        {getCss(css)}
        {getJavascript(js)}
        {getStructuredDataPageScript(metaInfo)}
        {getStructuredDataCompanyScript}
        <meta content="Copyright Benzinga. All rights reserved." name="copyright" />
        {robots && <meta content={robots} key="robots" name="robots" />}
        {image && <link href={image as string} rel="image_src" />}
        {description && <meta content={description} name="description" />}
        {getCanonical(canonical)}
        {author && <meta content={author} name="author" />}
        {symbol && <meta content={symbol} name="ticker" />}
        {/* Favicons and Miscellaneous */}
        <link color="#1177BA" href="/next-assets/images/safari-pinned-tab.svg" rel="mask-icon" />
        <link href="/next-assets/images/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
        <link href="/next-assets/images/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png" />
        <link href="/next-assets/images/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png" />
        <link href="/next-assets/site.webmanifest" rel="manifest" />
        <link color="#5bbad5" href="/next-assets/images/safari-pinned-tab.svg" rel="mask-icon" />
        <meta content="#2d89ef" name="msapplication-TileColor" />
        <meta content="#ffffff" name="theme-color" />
        <meta content="159483118580" property="fb:app_id" />

        {/* Alternates - Feeds, AMP, etc... */}
        <link href="http://feeds.benzinga.com/benzinga" rel="alternate" title="Benzinga" type="application/rss+xml" />
        {shortUrl && <link href={shortUrl as string} id="shorturl" rel="shorturl" type="text/html" />}
        {canonical && <meta content={canonical as string} name="syndication-source" />}

        {/* Twitter */}
        <meta
          content={twitterCard || (appEnvironment().isApp(appName.india) ? 'summary_large_image' : 'summary')}
          key="twitter:card"
          name="twitter:card"
        />
        <meta content="@benzinga" key="twitter:site" name="twitter:site" />
        <meta content={twitterTitle} key="twitter:title" name="twitter:title" />
        <meta content={decodeDescription(twitterDescription)} key="twitter:description" name="twitter:description" />
        {twitterImage && <meta content={twitterImage as string} key="twitter:image:src" name="twitter:image:src" />}

        {/* OpenGraph */}
        <meta content="Benzinga" key="og:site_name" property="og:site_name" />
        <meta content={canonical as string} key="og:url" property="og:url" />
        <meta content={ogTitle} key="og:title" property="og:title" />
        <meta content={ogType} key="og:type" property="og:type" />
        <meta content={decodeDescription(ogDescription)} key="og:description" property="og:description" />
        {ogImage && <meta content={ogImage as string} key="og:image" property="og:image" />}
        <meta content="image/jpeg" property="og:image:type" />
        {ogImageWidth && <meta content={String(ogImageWidth)} key="og:image:width" property="og:image:width" />}
        {ogImageHeight && <meta content={String(ogImageHeight)} key="og:image:height" property="og:image:height" />}

        {/* Shortcuts - Future Ticket */}

        {/* Apps */}
        <meta
          content="app-id=688949481, app-argument=https://itunes.apple.com/us/app/id688949481"
          name="apple-itunes-app"
          property="Benzinga for iOS"
        />
        <meta content="yes" name="mobile-web-app-capable" />
        <meta content="black" name="apple-mobile-web-app-status-bar-style" />

        {/* https://developers.google.com/search/docs/advanced/robots/robots_meta_tag#max-image-preview
        https://developers.google.com/search/case-studies/large-images-case-study */}
        <meta content="max-image-preview:large" name="robots"></meta>

        {/* Required for Desktop push notificaitons, but useful for other thigns TBD */}
        <link href="/manifest.json" rel="manifest" />

        {/* Validation Keys - Old System */}
        <meta content="648186192" property="fb:admins" />
        <meta content="B01B3489A33F83DDA4083E88C260097C" name="msvalidate.01" />
        <meta content="dbc639293c56bcb2" name="y_key" />
        <meta content="84f3b1552db5dfbe" name="y_key" />
        <meta content="101a0095-334d-4935-b87e-0e8fc771214e" name="fo-verify" />
        {/* <meta name="ir-site-verification-token" value="-627710181" /> */}
        {(() => {
          if (translationMeta) {
            const translationMetaTag: React.ReactElement[] = [];
            for (const key in translationMeta) {
              const lang = key.split('_')[1];
              const url = translationMeta[key].url;
              translationMetaTag.push(<link href={url} hrefLang={lang} rel="alternate" />);
            }
            return translationMetaTag;
          } else {
            return null;
          }
        })()}
        {hrefLanguage && hrefLanguage !== 'en' ? (
          <link href={canonical as string} hrefLang={hrefLanguage} key="da_alternate" rel="alternate" />
        ) : (
          <meta content="English" name="language" />
        )}
        <link href={canonical as string} hrefLang="x-default" key="hreflang-x-default" rel="alternate" />
        <link href={canonical as string} hrefLang="en" key="hreflang-en" rel="alternate" />
      </Head>
      <Schema data={voiceSearchSchema()} name="voice-search" />
      <Schema data={sitelinkSchema()} name="sitelink-search" />
    </>
  );
});

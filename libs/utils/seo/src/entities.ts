export enum PageType {
  Website = 'website',
  Front = 'front',
  TickerRatings = 'ticker-ratings',
  Story = 'story',
  Channel = 'channel',
  Topic = 'topic',
  Ticker = 'ticker',
  Tool = 'tool',
  Calendar = 'calendar',
  Evergreen = 'evergreen',
}

export interface StructuredDataI {
  articleBody?: string;
  about?: {
    '@type': string;
    name: string;
    tickerSymbol: string;
    url: string;
  };
  articleSection?: string | null;
  keywords?: string[];
  mainEntity?: string;
  mentions?: string[];
  pageType?: string;
  speakable?: string;
}

export interface Dimensions {
  authorName?: string;
  contentType: string;
  channels?: string[];
  isBZisBZPRO?: boolean;
}

export interface TranslationMetaData {
  [key: string]: {
    url?: string;
    post_id?: number;
  };
}

export type UrlMeta = URL | string | null;

export interface MetaProps {
  author?: string;
  authorId?: number;
  authorType?: string | null;
  authorURL?: string;
  canonical?: string | null;
  css?: string;
  dateCreated?: string | null;
  dateUpdated?: string | null;
  description: string;
  dimensions?: Dimensions;
  hrefLanguage?: string;
  image?: UrlMeta;
  js?: string;
  ogDescription?: string | null;
  ogImage?: UrlMeta;
  ogImageHeight?: number;
  ogImageWidth?: number;
  ogTitle?: string;
  ogType?: string;
  robots?: string | null;
  shortUrl?: UrlMeta;
  structuredData?: StructuredDataI | null;
  symbol?: string;
  title: string;
  twitterDescription?: string | null;
  twitterImage?: UrlMeta;
  twitterTitle?: string;
  vertical?: string[];
  pageType?: PageType;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  translationMeta?: TranslationMetaData;
  referer?: string;
  trackMeta?: boolean;
  subTitle?: string;
  schema?: Record<string, unknown> | null;
  language?: string;
  translations?: Record<string, unknown>;
  host?: string;
  isNotAccessibleForFree?: boolean;
}

export type OpenGraphType =
  | 'article'
  | 'book'
  | 'music.song'
  | 'music.album'
  | 'music.playlist'
  | 'music.radio_station'
  | 'profile'
  | 'website'
  | 'video.tv_show'
  | 'video.other'
  | 'video.movie'
  | 'video.episode';

export interface Paywall {
  active: boolean;
  hasAccess: boolean;
  paywallStyle: string;
}

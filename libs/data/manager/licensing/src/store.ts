import { LicensingResponse } from './entities';
import { ListenableSubscribable } from '@benzinga/subscribable';

export type LicensingStoreEvent = {
  type: 'licensing:update_widgets_licensing';
  data: LicensingResponse;
};

export class LicensingStore extends ListenableSubscribable<LicensingStoreEvent> {
  private widgetsLicensing?: LicensingResponse;
  private date?: Date;

  constructor() {
    super();
    this.date = undefined;
    this.widgetsLicensing = undefined;
  }

  public shouldWeFetchWidgetsLicensing = (): boolean => {
    const ONE_HOUR = 60 * 60 * 1000;
    const lastCalled = this.date?.getTime() ?? 0;
    if (this.widgetsLicensing === undefined || Date.now() - lastCalled > ONE_HOUR) {
      return true;
    }
    return false;
  };

  public updateWidgetsLicensing = (data: LicensingResponse): void => {
    this.date = new Date();
    this.widgetsLicensing = data;
    this.dispatch({
      data,
      type: 'licensing:update_widgets_licensing',
    });
  };

  public getWidgetsLicensing = (): LicensingResponse | undefined => {
    return this.widgetsLicensing;
  };
}

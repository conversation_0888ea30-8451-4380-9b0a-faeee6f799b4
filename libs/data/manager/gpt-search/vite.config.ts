import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';

export default defineConfig({
  cacheDir: '../../../../node_modules/.vite/libs/data/manager/gpt-search',
  plugins: [react(), nxViteTsPaths()],

  root: __dirname,

  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },

  test: {
    cache: { dir: '../../../../node_modules/.vitest' },
    coverage: { provider: 'v8', reportsDirectory: '../../../../coverage/libs/data/manager/gpt-search' },
    environment: 'jsdom',
    globals: true,
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
  },
});

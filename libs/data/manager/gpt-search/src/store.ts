import { ListenableSubscribable } from '@benzinga/subscribable';
import { GptSearchEvent } from './manager';
import { GptResponseData } from './entity';

export class GptSearchStore extends ListenableSubscribable<GptSearchEvent> {
  private data: GptResponseData[];
  constructor() {
    super();
    this.data = [];
  }

  public getCachedData = () => this.data;

  public getCachedWithPromptData = (prompt: string): GptResponseData | undefined =>
    this.data.find(d => d.prompt === prompt);

  public updatePromptCachedData = (prompt: string, data: any) => {
    const index = this.data.findIndex(d => d.prompt === prompt);
    if (index !== -1) {
      this.data[index] = { completions: data, prompt };
    } else {
      this.data.push({ completions: data, prompt });
    }
    this.dispatch({ data: this.data, type: 'gpt-search:update' });
    return;
  };
}

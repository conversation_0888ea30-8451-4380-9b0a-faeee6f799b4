import { SafeError, SafePromise } from '@benzinga/safe-await';
import { Subscribable, Subscription } from '@benzinga/subscribable';
import { GptSearchRequest } from './request';
import { Session } from '@benzinga/session';
import { GptSearchStore } from './store';
import { GptResponseData, ScannerConfigParameters } from './entity';
import { oneRequestAtATimeMultipleArgs } from '@benzinga/utils';
type GptSearchErrorEvent = {
  error?: SafeError;
  type: 'gpt-search:error';
};

type GptSearchUpdateEvent = {
  type: 'gpt-search:update';
  data: GptResponseData[];
};

export type GptSearchEvent = GptSearchErrorEvent | GptSearchUpdateEvent;

export class GptSearchManager extends Subscribable<GptSearchEvent> {
  private request: GptSearchRequest;
  private store: GptSearchStore;
  private requestSubscription?: Subscription<GptSearchRequest>;

  private getScannerConfigInternal = oneRequestAtATimeMultipleArgs(
    async (config: ScannerConfigParameters): SafePromise<GptResponseData> => {
      const data = this.store.getCachedWithPromptData(config.prompt);
      if (data) return { ok: data };
      const response = await this.request.getScannerConfig(config);
      if (response.err) {
        this.dispatch({ error: response.err, type: 'gpt-search:error' });
      }

      this.store.updatePromptCachedData(config.prompt, response.ok);

      return { ok: this.store.getCachedWithPromptData(config.prompt) as GptResponseData };
    },
  );

  constructor(session: Session) {
    super();
    this.store = new GptSearchStore();
    this.request = new GptSearchRequest(session);
  }

  public static getName = () => 'gpt-search';

  public getScannerConfig = (config: ScannerConfigParameters) => this.getScannerConfigInternal(config);
  public getCachedWithPromptData = (prompt: string): GptResponseData | undefined =>
    this.store.getCachedWithPromptData(prompt);

  public getCachedData = (): GptResponseData[] => this.store.getCachedData();

  protected onFirstSubscription = (): void => {
    this.requestSubscription = this.request.listen(event => this.dispatch(event));
  };

  protected onZeroSubscriptions = (): void => {
    this.requestSubscription?.unsubscribe();
  };
}

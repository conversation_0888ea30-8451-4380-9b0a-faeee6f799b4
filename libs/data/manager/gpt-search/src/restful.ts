import { RestfulClient, Session } from '@benzinga/session';
import { GptSearchEnvironment } from './environment';
import { ScannerConfigParameters } from './entity';
export class GptSearchRestful extends RestfulClient {
  constructor(session: Session) {
    super(session.getEnvironment(GptSearchEnvironment).url, session, { 'x-device-key': false });
  }

  getScannerConfig = async (config: ScannerConfigParameters) => {
    const url = this.URL('/api/v1/editorial/ai/scanner-gpt');
    return this.post(url, { ...config });
  };
}

import { ListenableSubscribable } from '@benzinga/subscribable';
import { GptSearchEvent } from './manager';
import { GptSearchRestful } from './restful';
import { Session } from '@benzinga/session';
import { GptResponseData, ScannerConfigParameters } from './entity';

export class GptSearchRequest extends ListenableSubscribable<GptSearchEvent> {
  private restful: GptSearchRestful;

  constructor(session: Session) {
    super();
    this.restful = new GptSearchRestful(session);
  }

  public getScannerConfig = async (config: ScannerConfigParameters) => {
    const response = await this.restful.getScannerConfig(config);

    if (response.err) {
      this.dispatch({
        error: response.err,
        type: 'gpt-search:error',
      });
      return { err: response.err };
    } else {
      this.dispatch({
        data: response.ok as GptResponseData[],
        type: 'gpt-search:update',
      });
      return { ok: response.ok };
    }
  };
}

import { ScannerConfig } from '@benzinga/scanner-config-manager';
import { ScannerProtos } from '@benzinga/scanner-manager';
export interface GptResponseData {
  prompt: string;
  completions: any;
}

export interface ScannerConfigParameters {
  prompt: string;
  prev_config: Omit<
    ScannerConfig,
    'columns' | 'sortDir' | 'tableParameters' | 'tags' | 'source' | 'refreshInterval'
  > & {
    sortDir: ScannerProtos.SortDir;
    fields: string[];
    stopColumnHighlighting?: boolean;
    filtersMenuCollapsed?: boolean;
    flightMode?: boolean;
  };
}

export interface MarketingWin {
  id: string;
  product: string;
  symbol: string;
  name: string;
  theWin: string;
  theWinDetailed: string;
  date: string;
  percentGain?: number;
  tradeType: string;
}

export interface ProductNotification {
  id: string;
  title: string;
  dateStart: string;
  dateEnd: string;
  teaser: string;
  product: string[];
  image: {
    sys: {
      type: string;
      linkType: string;
      id: string;
    };
  };
  link: string;
}

export enum ProductNotificationContentType {
  MarketingWins = 'marketing-wins',
  TopStocks = 'top-stocks',
  ProductNotifications = 'productNotifications',
}

export type ProductNotificationContent = MarketingWin | ProductNotification;

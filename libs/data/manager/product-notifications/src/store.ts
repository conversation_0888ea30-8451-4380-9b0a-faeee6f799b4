import { ProductNotificationContentType, ProductNotificationContent } from './entities';

export class ProductNotificationsStore {
  private productNotifications: Map<ProductNotificationContentType, ProductNotificationContent[]>;
  constructor() {
    this.productNotifications = new Map();
  }

  public updateContent = (contentType: ProductNotificationContentType, content: ProductNotificationContent[]) => {
    this.productNotifications.set(contentType, content);
  };

  public getContent = (contentType: ProductNotificationContentType): ProductNotificationContent[] => {
    return this.productNotifications.get(contentType) ?? [];
  };
}

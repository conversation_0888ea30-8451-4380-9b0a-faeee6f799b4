import { ProductNotificationContent } from './entities';

// actual object is much bigger and complex. Ignoring the rest for now.
export interface IngressProductNotificationContent<T> {
  sys: {
    id: string;
  };
  fields: T;
}
export interface IngressProductNotificationsResponse {
  items: IngressProductNotificationContent<ProductNotificationContent>[];
}

export function ingressProductNotificationsContent(
  response: IngressProductNotificationsResponse,
): ProductNotificationContent[] {
  return response.items.map(w => {
    return {
      ...w.fields,
      id: w.sys.id,
    };
  });
}

import { ProductNotificationContent } from './entities';

export const NOTIFICATION_IMPRESSION_KEY = 'productNotificationImpressionKeys';

export const getImpressionNotifications = (): string[] => {
  try {
    const impressionRecord = localStorage.getItem(NOTIFICATION_IMPRESSION_KEY);

    if (!impressionRecord) {
      return [];
    }

    const impressions = JSON.parse(impressionRecord);

    return Array.isArray(impressions) ? impressions : [];
  } catch (e) {
    return [];
  }
};

export const isNotificationImpressed = (notificationKey: string, impressionKeys?: string[]): boolean => {
  const notificationsImpression = impressionKeys ?? getImpressionNotifications();

  return !!notificationsImpression.includes(notificationKey);
};

export const setNotificationImpressed = (notificationKey: string): void => {
  const notificationsImpression = getImpressionNotifications();

  if (!notificationsImpression.includes(notificationKey)) {
    notificationsImpression.push(notificationKey);
    localStorage.setItem(NOTIFICATION_IMPRESSION_KEY, JSON.stringify(notificationsImpression));
  }
};

export const isNotificationInDateRange = (notification: ProductNotificationContent): boolean => {
  if ('dateStart' in notification && 'dateEnd' in notification) {
    const startDate = new Date(notification.dateStart);
    const endDate = new Date(notification.dateEnd);
    const now = new Date();

    return now >= startDate && now <= endDate;
  } else {
    return true;
  }
};

export const isNotificationActive = (notification: ProductNotificationContent): boolean => {
  return isNotificationInDateRange(notification);
};

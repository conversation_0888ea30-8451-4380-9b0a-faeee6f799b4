import { ProductNotificationContentType } from './entities';
import { ProductNotificationsRequestParams } from './request';

export interface ProductNotificationEgressRequestParams {
  limit?: number;
  skip?: number;
  contentType: ProductNotificationContentType;
}

export function egressProductNotificationsRequestParams(
  params?: ProductNotificationEgressRequestParams,
): ProductNotificationsRequestParams {
  return {
    content_type: params?.contentType ?? ProductNotificationContentType.ProductNotifications,
    limit: params?.limit,
    skip: params?.skip,
  };
}

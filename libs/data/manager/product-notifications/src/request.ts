import { SafeError, SafePromise } from '@benzinga/safe-await';
import { Session } from '@benzinga/session';
import { ExtendedListenableSubscribable } from '@benzinga/subscribable';
import { egressProductNotificationsRequestParams, ProductNotificationEgressRequestParams } from './egress';
import { ProductNotificationContent, ProductNotificationContentType } from './entities';
import { ingressProductNotificationsContent } from './ingress';
import { ProductNotificationsRestful } from './restful';
import { ProductNotificationsStore } from './store';

interface ProductNotificationsFunctions {
  getProductNotifications: ProductNotificationsRequest['getProductNotifications'];
}

interface ProductNotificationsErrorEvent {
  error?: SafeError;
  errorType: 'get_product_notification_error';
  type: 'error';
}

interface ProductNotificationsEvents {
  content: ProductNotificationContent[];
  type: 'product_notifications_received';
}

export type ProductNotificationsRequestEvent = ProductNotificationsEvents | ProductNotificationsErrorEvent;

export interface ProductNotificationsRequestParams {
  limit?: number;
  content_type?: ProductNotificationContentType;
  skip?: number;
}

export class ProductNotificationsRequest extends ExtendedListenableSubscribable<
  ProductNotificationsRequestEvent,
  ProductNotificationsFunctions
> {
  private restful: ProductNotificationsRestful;
  private prevParams?: ProductNotificationEgressRequestParams;
  private shouldFetch: boolean;
  private store: ProductNotificationsStore;

  constructor(session: Session) {
    super();
    this.shouldFetch = true;
    this.restful = new ProductNotificationsRestful(session);
    this.store = new ProductNotificationsStore();
  }

  public getProductNotifications = async (
    force?: boolean,
    params?: ProductNotificationEgressRequestParams,
  ): SafePromise<ProductNotificationContent[]> => {
    if (force || (params?.limit ?? 0) > (this.prevParams?.limit ?? 0) || this.shouldFetch) {
      const safeProductNotifications = await this.restful.getProductNotifications(
        egressProductNotificationsRequestParams(params),
      );

      if (safeProductNotifications.err) {
        this.dispatch({
          error: safeProductNotifications.err,
          errorType: 'get_product_notification_error',
          type: 'error',
        });
        return {
          err: safeProductNotifications.err,
        };
      }

      const productNotificationsContent = ingressProductNotificationsContent(safeProductNotifications.ok);

      this.dispatch({
        content: productNotificationsContent,
        type: 'product_notifications_received',
      });

      if (params?.contentType) {
        this.store.updateContent(params?.contentType, productNotificationsContent);
      }

      this.shouldFetch = false;
      setTimeout(
        () => {
          this.shouldFetch = true;
        },
        1000 * 60 * 15,
      );

      return {
        ok: productNotificationsContent,
      };
    } else {
      return { ok: params?.contentType ? this.store.getContent(params?.contentType) : [] };
    }
  };

  protected onSubscribe(): ProductNotificationsFunctions {
    return {
      getProductNotifications: this.getProductNotifications,
    };
  }
}

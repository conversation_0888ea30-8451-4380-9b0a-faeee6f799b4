import { SafePromise } from '@benzinga/safe-await';
import { Subscribable, Subscription } from '@benzinga/subscribable';
import { ProductNotificationsRequest, ProductNotificationsRequestEvent } from './request';
import { Session } from '@benzinga/session';
import { ProductNotificationContent } from './entities';
import { ProductNotificationEgressRequestParams } from './egress';
import { isNotificationActive, NOTIFICATION_IMPRESSION_KEY } from './utils';
import { UserSettingsManager } from '@benzinga/user-settings';

export type ProductNotificationsManagerEvent = ProductNotificationsRequestEvent;

export class ProductNotificationsManager extends Subscribable<ProductNotificationsManagerEvent> {
  private request: ProductNotificationsRequest;
  private requestSubscription?: Subscription<ProductNotificationsRequest>;
  private userSettings: UserSettingsManager;

  constructor(session: Session) {
    super();
    this.request = new ProductNotificationsRequest(session);
    this.userSettings = session.getManager(UserSettingsManager);
  }

  public static getName = () => 'benzinga-product-notifications';

  public getProductNotifications = async (
    params?: ProductNotificationEgressRequestParams,
    force?: boolean,
  ): SafePromise<ProductNotificationContent[]> => {
    return await this.request.getProductNotifications(force, params);
  };

  public getLatestUnimpressedProductNotification = async (
    params?: ProductNotificationEgressRequestParams,
  ): SafePromise<ProductNotificationContent | null> => {
    const { ok: productNotifications } = await this.getProductNotifications(params);
    const impressedKeys = this.getImpressedProductNotifications();

    return {
      ok:
        productNotifications?.find(
          productNotification =>
            isNotificationActive(productNotification) && !impressedKeys.includes(productNotification.id),
        ) ?? null,
    };
  };

  public getImpressedProductNotifications = (): string[] => {
    try {
      const userSettings = this.userSettings.getUserSettings();

      return (userSettings?.[NOTIFICATION_IMPRESSION_KEY] as string[]) ?? [];
    } catch {
      return [];
    }
  };

  public setImpressedProductNotifications = async (notificationKey: string) => {
    this.userSettings.setUserSetting(NOTIFICATION_IMPRESSION_KEY, [
      ...this.getImpressedProductNotifications(),
      notificationKey,
    ]);
  };

  protected override onSubscribe(_id: number, ..._args: readonly unknown[]) {
    return {
      getNotifications: this.getProductNotifications,
    };
  }

  protected override onZeroSubscriptions() {
    this.requestSubscription?.unsubscribe();
  }
}

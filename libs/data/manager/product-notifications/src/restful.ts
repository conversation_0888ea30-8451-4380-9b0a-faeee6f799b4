import { SafePromise } from '@benzinga/safe-await';

import { RestfulClient } from '@benzinga/session';
import { Session } from '@benzinga/session';
import { ProductNotificationsEnvironment } from './environment';
import { IngressProductNotificationsResponse } from './ingress';
import { ProductNotificationsRequestParams } from './request';

export class ProductNotificationsRestful extends RestfulClient {
  private spaceId;
  private environmentId;
  private apiKey;

  constructor(session: Session) {
    super(session.getEnvironment(ProductNotificationsEnvironment).url, session, undefined, {
      tokenParameterName: 'accessToken',
    });
    this.spaceId = session.getEnvironment(ProductNotificationsEnvironment).space;
    this.environmentId = session.getEnvironment(ProductNotificationsEnvironment).environment;
    this.apiKey = session.getEnvironment(ProductNotificationsEnvironment).apiKey;
  }

  getProductNotifications = (
    params: ProductNotificationsRequestParams,
  ): SafePromise<IngressProductNotificationsResponse> => {
    const url = this.URL(`spaces/${this.spaceId}/environments/${this.environmentId}/entries`, {
      ...params,
    });
    return this.debouncedGet(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
    });
  };
}

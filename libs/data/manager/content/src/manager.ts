import { SafeError, SafePromise } from '@benzinga/safe-await';
import { ExtendedSubscribable } from '@benzinga/subscribable';

import {
  LayoutResponse,
  Entity,
  PostsQuery,
  Profile,
  RedirectResponse,
  TranslationResponse,
  ReviewForm,
  WordpressPage,
  WordpressPost,
  Podcast,
  PodcastsQuery,
  ProductResponse,
  TermResponse,
  ZipCodeQuery,
  ZipCodeResponse,
  MortgageData,
  MortgageDataResponse,
  MenuInterface,
  CampaignResponse,
  TermId,
  Term,
  Show,
  ShowsQuery,
  VertialQuery,
  RequestDisclosureByParams,
  PartnerDisclosure,
  WordpressLayout,
  GetImpressionsParams,
  CreateImpressionsParams,
  GetImpressionResponse,
  Impression,
  RegisterImpressionResponse,
  ImpressionBase,
  PodcastShows,
  PodcastPostQuery,
  PodcastPost,
  WebStory,
  DentalInsuranceData,
  DentalInsuranceResponse,
  AskTheExpert,
  AskTheExpertResponse,
  SponsoredContentArticlesParams,
  CryptoExchangeRateResponse,
  NodeLayout,
  CompareProductFieldsResponse,
  SubscribeToNewsletterRequest,
  SubscribeToNewsletterResponse,
  ProductFilterOptions,
} from './entities';
import { ContentRequest, ContentRestfulEvent, ContentFunctions } from './request';
import { Session } from '@benzinga/session';
import { ContentStore } from './store';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { oneRequestAtATime } from '@benzinga/utils';
import { isGlobalImpressionStored, storeGlobalImpression } from './utils';

export type ContentManagerEvent = ContentRestfulEvent;

export class ContentManager extends ExtendedSubscribable<ContentManagerEvent, ContentFunctions> {
  public getDebouncedSponsoredContentArticles = oneRequestAtATime((force?: boolean): SafePromise<StoryObject[]> => {
    return this.getSponsoredContentArticles(force);
  });

  private request: ContentRequest;
  private store: ContentStore;

  constructor(session: Session) {
    super();
    this.request = new ContentRequest(session);
    this.store = new ContentStore();
  }

  public static getName = () => 'benzinga-content';

  public getNodeLayout = async (id: string): SafePromise<NodeLayout> => {
    return await this.request.getNodeLayout(id);
  };

  public getPoll = async (id: number): SafePromise<any> => {
    return await this.request.getPoll(id);
  };

  public postPollSubmission = async (id: number, data: any): SafePromise<any> => {
    return await this.request.postPollSubmission(id, data);
  };

  public getRedirectByUrl = async (url: string): SafePromise<RedirectResponse> => {
    return await this.request.getRedirectByUrl(url);
  };

  public getEntity = async (id: string): SafePromise<Entity> => {
    return await this.request.getEntity(id);
  };

  public getIndiaWebStories = async (limit: number): SafePromise<WebStory[]> => {
    return await this.request.getIndiaWebStories(limit);
  };

  public getNavigation = async (postType: string): SafePromise<MenuInterface> => {
    return await this.request.getNavigation(postType);
  };

  public getProfile = async (id: string): SafePromise<Profile> => {
    return await this.request.getProfile(id);
  };

  public getProfileWithPath = async (path: string): SafePromise<Profile> => {
    return await this.request.getProfileWithPath(path);
  };

  public getPost = async (post_id: number | string): SafePromise<WordpressPost> => {
    return await this.request.getPost(post_id);
  };

  public getPage = async (page_id: number | string): SafePromise<WordpressPage> => {
    return await this.request.getPage(page_id);
  };

  public getPageWithPath = async (path: string): SafePromise<WordpressPage> => {
    return await this.request.getPageWithPath(path);
  };

  public isTopicWhitelisted = async (path: string): Promise<boolean> => {
    const { default: topics } = await import('./WhitelistedTopics');

    if (!topics || !Array.isArray(topics)) {
      return true; // If no topics are defined, assume all paths are valid
    }

    const slugify = (str: string) =>
      str
        .toString()
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^\w-]+/g, '')
        .replace(/--+/g, '-')
        .replace(/^-+/, '')
        .replace(/-+$/, '');

    const pathSlug = slugify(path);

    const topicSlugs = topics.map((topic: string) => slugify(topic));
    return topicSlugs.includes(pathSlug);
  };

  public getPageWithPathBySiteCode = async (path: string, siteCode: string): SafePromise<WordpressPage> => {
    return await this.request.getPageWithPathBySiteCode(path, siteCode);
  };

  public getPortfolioPage = async (path: string): SafePromise<WordpressPage> => {
    return await this.request.getPortfolioPage(path);
  };

  public getTermByPath = async (path: string): SafePromise<TermResponse> => {
    return await this.request.getTermByPath(path);
  };

  public getTermByName = async (name: string): SafePromise<TermResponse> => {
    return await this.request.getTermByName(name);
  };

  public getTermById = async (tid: TermId, force = false): SafePromise<Term[]> => {
    if (this.store.shouldFetchTerm(tid) === true || force) {
      const resp = await this.request.getTermById(tid);

      if (resp.err) {
        return { err: resp.err, ok: undefined };
      } else if (resp?.ok?.data) {
        this.store.setTerm(resp.ok.data);
      }

      return { ok: resp?.ok?.data || [] };
    } else {
      const term = this.store.getTermById(tid);
      return { ok: term ? [term] : [] };
    }
  };

  public clearCache = async (path: string): SafePromise<any> => {
    return await this.request.clearCache(path);
  };

  public clearNodeCache = async (id: number): SafePromise<any> => {
    return await this.request.clearNodeCache(id);
  };

  public getTopicByName = async (name: string): SafePromise<WordpressLayout> => {
    return await this.request.getTopicByName(name);
  };

  public getWordpressPost = async (post_id: number | string): SafePromise<WordpressPost> => {
    return await this.request.getWordpressPost(post_id);
  };

  public getMoneyWidget = async (slug: string): SafePromise<WordpressPost> => {
    return await this.request.getMoneyWidget(slug);
  };

  public getPostCampaigns = async (post_id: number): SafePromise<any> => {
    return await this.request.getPostCampaigns(post_id);
  };

  public getGoLinks = async (slugs: string[]): SafePromise<any> => {
    return await this.request.getGoLinks(slugs);
  };

  public getPosts = async (query: PostsQuery): SafePromise<WordpressPost[]> => {
    return await this.request.getPosts(query);
  };

  public getCustomPosts = async (query: PostsQuery): SafePromise<WordpressPost[]> => {
    return await this.request.getCustomPosts(query);
  };

  public getNodeTranslation = async (node_id: string): SafePromise<TranslationResponse> => {
    return await this.request.getNodeTranslation(node_id);
  };

  public getPartnerWithPath = async (path: string): SafePromise<Profile> => {
    return await this.request.getPartnerWithPath(path);
  };

  public getPartnerDisclosurePageBySlug = async (slug: string): SafePromise<WordpressPage> => {
    return await this.request.getPartnerDisclosurePageBySlug(slug);
  };

  public getProductFilters = async (path: string): SafePromise<ProductFilterOptions> => {
    return await this.request.getProductFilters(path);
  };

  public getPartnerDisclosureBySlug = async (
    slug: string,
    params: Omit<RequestDisclosureByParams, 'topics' | 'channels'>,
  ): SafePromise<PartnerDisclosure> => {
    return await this.request.getPartnerDisclosureBySlug(slug, params);
  };

  public getPartnerDisclosureByNodeParams = async (
    params: RequestDisclosureByParams,
  ): SafePromise<PartnerDisclosure> => {
    return await this.request.getPartnerDisclosureByNodeParams(params);
  };

  public getPodcastEpisodes = async (query: PodcastsQuery): SafePromise<Podcast[]> => {
    return await this.request.getPodcastEpisodes(query);
  };

  public getPodcastsByShow = async (show: PodcastShows): SafePromise<Podcast[]> => {
    return await this.request.getPodcastsByShow(show);
  };

  public getPodcastPost = async (query: PodcastPostQuery): SafePromise<PodcastPost> => {
    return await this.request.getPodcastPost(query);
  };

  public getPodcastPostById = async (id: number): SafePromise<PodcastPost> => {
    return await this.request.getPodcastPostById(id);
  };

  public getShowById = async (id: number): SafePromise<Show | undefined> => {
    return await this.request.getShowById(id);
  };

  public getShows = async (query: ShowsQuery = {}): SafePromise<Show[]> => {
    return await this.request.getShows(query);
  };

  public getProduct = async (query: PostsQuery): SafePromise<ProductResponse> => {
    return this.request.getProduct(query);
  };

  public getCustomFieldsLabel = async (query: PostsQuery): SafePromise<CompareProductFieldsResponse> => {
    return this.request.getCustomFieldsLabel(query);
  };

  public submitReview = async (form: ReviewForm, review: any): Promise<any> => {
    return this.request.submitReview(form, review);
  };

  public getStateAvailability = async (query: ZipCodeQuery): SafePromise<ZipCodeResponse> => {
    return this.request.getStateAvailability(query);
  };

  public getCampaigns = async (): SafePromise<CampaignResponse> => {
    return this.request.getCampaigns();
  };

  public saveMortageLead = async (data: MortgageData): SafePromise<MortgageDataResponse> => {
    return this.request.saveMortageLead(data);
  };

  public getVertical = async (params: VertialQuery): SafePromise<any> => {
    return await this.request.getVertical(params);
  };

  public saveDentalInsuranceLead = async (data: DentalInsuranceData): SafePromise<DentalInsuranceResponse> => {
    return this.request.saveDentalInsuranceLead(data);
  };

  public saveAskTheExpertComment = async (data: AskTheExpert): SafePromise<AskTheExpertResponse> => {
    return this.request.saveAskTheExpertComment(data);
  };

  public subscribeToNewsletter = async (
    data: SubscribeToNewsletterRequest,
  ): SafePromise<SubscribeToNewsletterResponse> => {
    return this.request.subscribeToNewsletter(data);
  };

  public getContentImpressions = async (params: GetImpressionsParams): SafePromise<GetImpressionResponse> => {
    return await this.request.getContentImpressions(params);
  };

  public createContentImpressions = async (params: CreateImpressionsParams): SafePromise<Impression[]> => {
    return await this.request.createContentImpressions(params);
  };

  public registerContentImpression = async (content_id: number): SafePromise<RegisterImpressionResponse> => {
    return await this.request.registerContentImpression(content_id);
  };

  public getExchangeRate = async (): SafePromise<CryptoExchangeRateResponse> => {
    return await this.request.getExchangeRate();
  };

  public registerFeedContentImpression = async (
    contentId: number,
    storeSlug: string,
  ): SafePromise<RegisterImpressionResponse> => {
    if (!isGlobalImpressionStored(storeSlug)) {
      storeGlobalImpression(storeSlug);
      return await this.registerContentImpression(contentId);
    }

    return {
      ok: {
        registered: false,
      },
    };
  };

  public updateContentLimitation = async (
    content_id: string,
    params: Partial<ImpressionBase>,
  ): SafePromise<Impression> => {
    return await this.request.updateContentLimitation(content_id, params);
  };

  public updateContentLimits = async (limitMap: Record<number, number>): SafePromise<Impression[]> => {
    try {
      const updatedContentLimitations: Impression[] = [];

      if (Object.keys(limitMap).length) {
        const promises: SafePromise<Impression>[] = [];

        Object.keys(limitMap).forEach(key => {
          promises.push(new Promise(resolve => resolve(this.updateContentLimitation(key, { limit: limitMap[key] }))));
        });

        const responses = await Promise.allSettled<SafePromise<Impression>>(promises);

        responses.forEach(result => {
          if (result.status === 'fulfilled' && result.value?.ok) {
            updatedContentLimitations.push(result.value.ok);
          }
        });

        return { ok: updatedContentLimitations };
      }

      return { ok: updatedContentLimitations };
    } catch (error: any) {
      return {
        err: new SafeError(error.message, 'update_content_limits'),
      };
    }
  };

  public getSponsoredContentArticles = async (
    force?: boolean,
    options?: SponsoredContentArticlesParams,
  ): SafePromise<StoryObject[]> => {
    if (this.store.shouldFetchSponsoredNodes() === true || force) {
      const resp = await this.request.getSponsoredContentArticles(options);

      if (resp.err) {
        return { err: resp.err, ok: undefined };
      } else if (resp?.ok) {
        this.store.setSponsoredNodes(resp.ok);
      }

      return { ok: resp?.ok || [] };
    } else {
      const sponsoredNodes = this.store.getSponsoredNodes();
      return { ok: sponsoredNodes ? sponsoredNodes : [] };
    }
  };

  public getRandomSponsoredContentArticle = async (): SafePromise<StoryObject | null> => {
    try {
      const response = await this.getSponsoredContentArticles();

      if (Array.isArray(response?.ok)) {
        return { ok: response.ok[Math.floor(Math.random() * response.ok.length)] };
      }

      return { ok: null };
    } catch (error: any) {
      return {
        err: new SafeError(error.message, 'update_content_limits'),
      };
    }
  };

  protected onSubscribe(): ContentFunctions {
    return {
      createContentImpressions: this.createContentImpressions,
      getContentImpressions: this.getContentImpressions,
      getEntity: this.getEntity,
      getNodeLayout: this.getNodeLayout,
      getNodeTranslation: this.getNodeTranslation,
      getProfile: this.getProfile,
      getRedirectByUrl: this.getRedirectByUrl,
      registerContentImpression: this.registerContentImpression,
      updateContentLimitation: this.updateContentLimitation,
    };
  }
}

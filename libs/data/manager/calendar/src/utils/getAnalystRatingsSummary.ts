import { Ratings } from '../entities';

const BuyRating = ['Buy', 'Conviction Buy', 'Market Outperform', 'Outperform', 'Strong Buy'];
const OverweightRating = ['Overweight'];
const HoldRating = ['Hold', 'Neutral'];
const UnderweightRating = ['Underweight'];
const SellRating = ['Sell', 'Underperform'];
type RatingsAggregate = {
  Buy: number;
  Hold: number;
  Overweight: number;
  Sell: number;
  Underweight: number;
};

export interface AnalystRatingsSummary {
  aggregateRatings: RatingsAggregate | null;
  consensusPriceTarget: string | null;
  consensusRating: string | null;
  consensusRatingValue: number | null;
  highestPriceTarget: string | null;
  lowestPriceTarget: string | null;
  mostRecentFirms: { date: string; name: string }[];
  numRatings: number;
  numUniqueRatings: number;
  ratings: Ratings[];
  uniqueRatingsFromLastThreeYears: Ratings[];
  highestPriceRating: PricedRating | null;
  lowestPriceRating: PricedRating | null;
}

interface PricedRating {
  date: string;
  firm: string;
  price: string;
}
const sortByRecentDate = (data: Ratings[]) => {
  return data?.sort((a, b) => new Date(b.date || 0).getTime() - new Date(a.date || 0).getTime());
};

const getAggregateRatings = (ratings: Ratings[]): RatingsAggregate => {
  const aggregate = {
    Buy: 0,
    Hold: 0,
    Overweight: 0,
    Sell: 0,
    Underweight: 0,
  };
  ratings?.map((rating: Ratings) => {
    if (BuyRating.includes(rating.rating_current)) {
      aggregate['Buy']++;
    } else if (OverweightRating.includes(rating.rating_current)) {
      aggregate['Overweight']++;
    } else if (HoldRating.includes(rating.rating_current)) {
      aggregate['Hold']++;
    } else if (UnderweightRating.includes(rating.rating_current)) {
      aggregate['Underweight']++;
    } else if (SellRating.includes(rating.rating_current)) {
      aggregate['Sell']++;
    }
  });
  return aggregate;
};

const calculateConsensusRating = (ratings: string[]): number => {
  const ratingValues = {
    'Strong Sell': 1,
    // eslint-disable-next-line sort-keys-fix/sort-keys-fix
    Sell: 2,
    Underweight: 2.5,
    // eslint-disable-next-line sort-keys-fix/sort-keys-fix
    Neutral: 3,
    // eslint-disable-next-line sort-keys-fix/sort-keys-fix
    Hold: 3,
    // eslint-disable-next-line sort-keys-fix/sort-keys-fix
    Buy: 4,
    Outperform: 4.7,
    Overweight: 4.5,
    // eslint-disable-next-line sort-keys-fix/sort-keys-fix
    'Market Outperform': 4.8,
    'Strong Buy': 5,
    // eslint-disable-next-line sort-keys-fix/sort-keys-fix
    'Conviction Buy': 5,
  };

  const sumOfRatings = ratings?.reduce((totalRatings, rating) => {
    const ratingValue = ratingValues[rating];
    return totalRatings + (ratingValue ? ratingValue : 0);
  }, 0);

  const result = (sumOfRatings / (ratings.length * 5)) * 100;

  return result;
};

const getUniqueArray = (array: Ratings[], keysToFilter: string[]): Ratings[] => {
  return Object.values(
    array?.reduce((uniqueMap, entry) => {
      const key = keysToFilter.map(k => entry[k]).join('|');
      if (!(key in uniqueMap)) uniqueMap[key] = entry;
      return uniqueMap;
    }, {}),
  );
};

const getMostFrequent = (array: string[]): string => {
  const hashmap = array?.reduce((acc, val) => {
    acc[val] = (acc[val] || 0) + 1;
    return acc;
  }, {});
  return Object.keys(hashmap).reduce((a, b) => (hashmap[a] > hashmap[b] ? a : b));
};

const getThreeYearsAgo = (): number => {
  const date = new Date();
  date.setFullYear(date.getFullYear() - 3);
  return date.getTime();
};

const getMostRecentFirms = (ratings: Ratings[]): { date: string; name: string }[] => {
  const recentRatings = sortByRecentDate(ratings);
  const seen = new Set();
  const recentFirms: { date: string; name: string }[] = [];

  for (const rating of recentRatings) {
    const key = `${rating.date}-${rating.analyst}`;
    if (!seen.has(key)) {
      seen.add(key);
      recentFirms.push({ date: rating.date, name: rating.analyst });
    }
    if (recentFirms.length === 5) break;
  }

  return recentFirms;
};

export const getDetailedData = (ratings: Ratings[]): AnalystRatingsSummary => {
  if (!ratings.length) {
    return {
      aggregateRatings: null,
      consensusPriceTarget: null,
      consensusRating: null,
      consensusRatingValue: null,
      highestPriceRating: null,
      highestPriceTarget: null,
      lowestPriceRating: null,
      lowestPriceTarget: null,
      mostRecentFirms: [],
      numRatings: ratings.length,
      numUniqueRatings: 0,
      ratings: [],
      uniqueRatingsFromLastThreeYears: [],
    };
  }

  const sortedByNewest = sortByRecentDate(ratings);
  const uniqueValuesArray: Ratings[] = getUniqueArray(sortedByNewest, ['analyst', 'analyst_name']);
  const threeYearsAgoInMiliseconds = getThreeYearsAgo();
  const uniqueRatingsFromLastThreeYears = uniqueValuesArray.filter(
    item => new Date(item.date).getTime() >= threeYearsAgoInMiliseconds && item.pt_current !== '',
  );

  if (!uniqueRatingsFromLastThreeYears.length) {
    return {
      aggregateRatings: null,
      consensusPriceTarget: null,
      consensusRating: null,
      consensusRatingValue: null,
      highestPriceRating: null,
      highestPriceTarget: null,
      lowestPriceRating: null,
      lowestPriceTarget: null,
      mostRecentFirms: [],
      numRatings: ratings.length,
      numUniqueRatings: uniqueValuesArray.length,
      ratings: sortedByNewest,
      uniqueRatingsFromLastThreeYears: [],
    };
  }

  const mostRecentFirms = getMostRecentFirms(uniqueRatingsFromLastThreeYears);

  const aggregateRatings = getAggregateRatings(uniqueRatingsFromLastThreeYears);

  // return {
  //   uniqueRatingsFromLastThreeYears,
  // };

  const highestPriceRating = uniqueRatingsFromLastThreeYears
    .sort((a, b) => Number(b?.pt_current) - Number(a?.pt_current))
    .slice(0, 1)[0];

  const highestPriceTarget = Number(highestPriceRating?.pt_current).toFixed(2);

  const highestPricedFormatted = {
    date: highestPriceRating?.date,
    firm: highestPriceRating?.analyst,
    price: highestPriceTarget,
  };

  const lowestPriceRating = uniqueRatingsFromLastThreeYears
    .sort((a, b) => Number(a?.pt_current) - Number(b?.pt_current))
    .slice(0, 1)[0];

  const lowestPriceTarget = Number(lowestPriceRating?.pt_current).toFixed(2);

  const lowestPricedFormatted = {
    date: lowestPriceRating?.date,
    firm: lowestPriceRating?.analyst,
    price: lowestPriceTarget,
  };

  const priceTargetsArray = uniqueRatingsFromLastThreeYears.map(rating => Number(rating?.pt_current));
  const ratingArray = uniqueRatingsFromLastThreeYears.map(rating => rating.rating_current);
  const consensusPriceTarget = (
    priceTargetsArray.reduce((a, b) => Number(a) + Number(b)) / priceTargetsArray.length
  ).toFixed(2);

  const consensusRating = getMostFrequent(ratingArray);
  const consensusRatingValue = calculateConsensusRating(ratingArray);

  return {
    aggregateRatings,
    consensusPriceTarget,
    consensusRating,
    consensusRatingValue,
    highestPriceRating: highestPricedFormatted,
    highestPriceTarget,
    lowestPriceRating: lowestPricedFormatted,
    lowestPriceTarget,
    mostRecentFirms,
    numRatings: ratings.length,
    numUniqueRatings: uniqueValuesArray.length,
    ratings: sortedByNewest,
    uniqueRatingsFromLastThreeYears,
  };
};

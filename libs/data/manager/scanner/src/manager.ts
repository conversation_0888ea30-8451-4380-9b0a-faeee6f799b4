import { Subscribable, Subscription } from '@benzinga/subscribable';
import { ScannerRequest, ScannerInstrumentsRequestEvent } from './request';
import { Session } from '@benzinga/session';
import { ScannerProtos } from './scanner_protos';
import { SafePromise } from '@benzinga/safe-await';
import { deepEqual } from '@benzinga/utils';
import { ScannerFeed } from './feed';
import { ScannerConfig } from '@benzinga/scanner-config-manager';

import { QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';

export type ScannerManagerEvent = ScannerInstrumentsRequestEvent;
export class ScannerManager extends Subscribable<ScannerManagerEvent> {
  private request: ScannerRequest;
  private session: Session;
  private requestSubscription?: Subscription<ScannerRequest>;
  private feeds: { config: ScannerConfig; feed: ScannerFeed }[] = [];

  constructor(session: Session) {
    super();
    this.session = session;
    this.request = new ScannerRequest(session);
    this.session.getManager(QuotesV3FieldsManager).getScannerDefs();
  }

  public static getName = () => 'benzinga-scanner';

  public getInstruments = async (query: ScannerConfig): SafePromise<ScannerProtos.IQueryResponse> => {
    return this.request.getScannerInstruments(query);
  };

  /**
   * Get instruments using the same query format as @benzinga/legacy-scanner-manager (to not break existing code)
   */
  public getInstrumentsWithIQuery = async (query: ScannerProtos.IQuery): SafePromise<ScannerProtos.IQueryResponse> => {
    return this.request.getScannerInstrumentsWithQuery(query);
  };

  public getFeed = (config: ScannerConfig) => {
    const feed = this.feeds.find(f => deepEqual(f.config, config));
    if (feed) {
      return feed.feed;
    } else {
      const newFeed = {
        config,
        feed: new ScannerFeed(this.session, config, this.request, () => {
          this.feeds = this.feeds.filter(f => f.config !== config);
        }),
      };
      this.feeds.push(newFeed);
      return newFeed.feed;
    }
  };

  public getSectorHeatmap = async () => {
    return this.request.getSectorHeatmap();
  };

  protected onFirstSubscription(): void {
    this.requestSubscription = this.request.listen(event => this.dispatch(event));
  }

  protected onZeroSubscriptions(): void {
    this.requestSubscription?.unsubscribe();
  }
}

export interface ParsedArticleUrl {
  type: string;
  year?: string;
  month?: string;
  id?: string;
  slug?: string;
  fullSlug: string;
}

/**
 * Parses article URLs to extract components needed for the 410 API
 * Supports various URL patterns like:
 * - /news/25/05/13434134/article-slug
 * - /category/25/05/13434134/article-slug
 * - /category/subcategory/25/05/13434134/article-slug
 * - /pressreleases/25/05/13434134/article-slug
 * - /secfilings/25/05/13434134/article-slug
 * - /content/13434134/article-slug
 */
export const parseArticleUrl = (url: string): ParsedArticleUrl | null => {
  try {
    // Remove leading slash and split into parts
    const cleanUrl = url.replace(/^\/+/, '');
    const parts = cleanUrl.split('/');

    // Handle /content/ID/slug pattern
    if (parts[0] === 'content' && parts.length >= 2) {
      const id = parts[1];
      const slug = parts.slice(2).join('/');
      return {
        type: 'article',
        id,
        slug,
        fullSlug: `content/${id}/${slug}`,
      };
    }

    // Handle /pressreleases/YY/MM/ID/slug pattern
    if (parts[0] === 'pressreleases' && parts.length >= 4) {
      const year = parts[1];
      const month = parts[2];
      const id = parts[3];
      const slug = parts.slice(4).join('/');
      return {
        type: 'article',
        year,
        month,
        id,
        slug,
        fullSlug: `pressreleases/${year}/${month}/${id}/${slug}`,
      };
    }

    // Handle /secfilings/YY/MM/ID/slug pattern
    if (parts[0] === 'secfilings' && parts.length >= 4) {
      const year = parts[1];
      const month = parts[2];
      const id = parts[3];
      const slug = parts.slice(4).join('/');
      return {
        type: 'article',
        year,
        month,
        id,
        slug,
        fullSlug: `secfilings/${year}/${month}/${id}/${slug}`,
      };
    }

    // Handle patterns with year/month/id/slug
    // Could be: /YY/MM/ID/slug or /cat1/YY/MM/ID/slug or /cat1/cat2/YY/MM/ID/slug
    let categoryParts: string[] = [];
    let yearIndex = -1;

    // Find the year/month pattern (YY/MM where YY is 2 digits and MM is 2 digits)
    for (let i = 0; i < parts.length - 2; i++) {
      const yearPart = parts[i];
      const monthPart = parts[i + 1];
      
      // Check if this looks like a year/month pattern
      if (
        yearPart &&
        monthPart &&
        /^[0-2][0-9]$/.test(yearPart) &&
        /^[0-1][0-9]$/.test(monthPart) &&
        parts[i + 2] &&
        /^[0-9]+$/.test(parts[i + 2])
      ) {
        yearIndex = i;
        categoryParts = parts.slice(0, i);
        break;
      }
    }

    if (yearIndex >= 0 && parts.length >= yearIndex + 3) {
      const year = parts[yearIndex];
      const month = parts[yearIndex + 1];
      const id = parts[yearIndex + 2];
      const slug = parts.slice(yearIndex + 3).join('/');

      // Determine type based on categories
      let type = 'article';
      if (categoryParts.length > 0) {
        // For topic/channel pages, we might want to use 'topic' type
        // For now, defaulting to 'article' as per the API examples
        type = 'article';
      }

      return {
        type,
        year,
        month,
        id,
        slug,
        fullSlug: cleanUrl,
      };
    }

    // If no pattern matches, return null
    return null;
  } catch (error) {
    console.error('Error parsing article URL:', error);
    return null;
  }
};

/**
 * Builds the slug for the 410 API call
 * Format: category/year/month/id/slug-text
 */
export const buildExpiredUrlSlug = (parsedUrl: ParsedArticleUrl): string => {
  return parsedUrl.fullSlug;
};

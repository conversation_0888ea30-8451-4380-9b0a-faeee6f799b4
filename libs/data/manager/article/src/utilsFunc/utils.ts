import { insert, join, length, match, pipe } from 'ramda';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { isMobile } from '@benzinga/device-utils';

import { getInitialTickers } from '../utils';

import {
  ArticleBlock,
  ArticleBlocksList,
  ArticleData,
  ArticleDFPTags,
  CampaignStrategy,
  InternalNodeCamelCase,
} from '../entities/article';

import { ArticleManager } from '../manager';

import {
  AdvancedNewsManager,
  StoryCategory,
  StoryObject,
  nodeHasChannel,
  nodeHasTag,
} from '@benzinga/advanced-news-manager';

import { ICalendarKeys } from '@benzinga/calendars';
import {
  getAMPPrimisIDbyChannel,
  getPrimisIDbyChannel,
  isZacksAppliesToTickers,
  ZACK_DFP_SLUG,
} from '@benzinga/ads-utils';
import { formatDFPTagCollection, runningClientSide, appEnvironment } from '@benzinga/utils';
import { MetaProps } from '@benzinga/seo';
import { Session } from '@benzinga/session';
import cboeKeywordsList from '../assets/keywords/cboe';
import tradestationKeywordsList from '../assets/keywords/tradestation';
import rocketMortgageKeywordsList from '../assets/keywords/rocketMortgage';
import { Block, MoneyBlockType } from '@benzinga/content-manager';
import { getSettings, getCampaignStrategySetting, getAllRefPartners } from '@benzinga/ads-utils';

const CAMPAIGN_REF_STORAGE_KEY = 'campaign-referrer';

export const authorTypeGenerator = (
  authorType = 'Benzinga Staff Writer',
  showAdvertiserDisclosure: boolean,
  contentType: string,
): string => {
  if (showAdvertiserDisclosure) {
    //authorType = 'Benzinga Partner';
  }
  if (contentType !== 'story') {
    //   authorType = 'Contributor';
  }

  return authorType;
};

type TargetingElement = string | number;

export const attachThirdPartyDFPTargeting = async (
  article: ArticleData,
  targeting: ArticleDFPTags,
): Promise<ArticleDFPTags> => {
  if (!Array.isArray(article.tickers)) {
    return targeting;
  }

  const tickers = article.tickers.map(ticker => ticker.name);
  const isZacksApplied = await isZacksAppliesToTickers(tickers);
  if (isZacksApplied) {
    Array.isArray(targeting.BZ_TAG) && targeting.BZ_TAG.push(ZACK_DFP_SLUG);
  }

  return targeting;
};

export const setDFPTargeting = (article: ArticleData) => {
  if (!article || !runningClientSide()) return;

  const targeting = getDFPTargeting(article);
  if (!targeting) return;

  //DFPManager.setTargetingArguments(targeting);
  //DFPManager.load();
};

export const getDFPTargeting = async (article: ArticleData): Promise<ArticleDFPTags | null> => {
  if (!article) return null;
  const channelTargeting: TargetingElement[] = formatDFPTagCollection<StoryCategory>(article.channels);
  const tagTargeting: TargetingElement[] = formatDFPTagCollection<StoryCategory>(article.tags);
  const tickersTargeting: TargetingElement[] = formatDFPTagCollection<StoryCategory>(article.tickers);

  let targeting: ArticleDFPTags = {
    BZ_AUTHOR: article.name ?? null,
    BZ_CHANNEL: channelTargeting ?? null,
    BZ_NID: `${article.nodeId}`,
    BZ_PTYPE: article.type || article.metaProps?.pageType || undefined,
    BZ_TAG: tagTargeting ?? null,
    BZ_TICKER: tickersTargeting ?? null,
    ...(article.dfpTags || {}),
  };

  const targetingWithThirdPartyDTFTagsAttrached = await attachThirdPartyDFPTargeting(article, targeting)
    .then(newTargeting => {
      targeting = newTargeting;
      return targeting;
    })
    .catch(error => {
      console.log(`Error attaching third party targeting on article: ${article.nodeId} -`, error);
      return targeting;
    });

  return targetingWithThirdPartyDTFTagsAttrached;
};

interface CountStatistics {
  characterCount: number;
  wordCount: number;
}

export const getWordsAndCharactersCount = (articleBody: string): CountStatistics => {
  const htmlRegex = /(<([^>]+)>)/gi;
  const text = articleBody?.replace(htmlRegex, '');
  const words = text.trim().split(/\s+/);

  return {
    characterCount: text.replaceAll(' ', '').length,
    wordCount: words ? words.length : 0,
  };
};

export const checkIfArticleBodyIsShortherThan = (body: string, count: number) => {
  const wordCount = getWordsAndCharactersCount(body).wordCount;
  if (wordCount < count) {
    return true;
  } else {
    return false;
  }
};

export const calculateReadTime = (body: string) => {
  try {
    const trimmed = body.trim()?.replace(/['";:,.?¿\-!¡]+/g, '');
    const wordCount = trimmed?.match(/\S+/g)?.length ?? 0;
    // Set reading speed as 200 words per minute.
    const wpm = 200;
    const estimatedTime = wordCount / wpm;
    const minutes = Math.round(estimatedTime);
    return minutes < 1 ? `${Math.round(estimatedTime * 60)} seconds read` : `${minutes} min read`;
  } catch {
    return '';
  }
};

export const splitElements = (htmlString: string): string[] | null => {
  const searchTagsRegexp = /<p.*?>(.*?)<\/p.*?>/g;
  return match(searchTagsRegexp, htmlString);
};

export type Tweet = {
  content: string;
  id: number;
};

/*
  We have to replace <blockquote twitter-tweet with <p id="tweet-id"></p> before inserting the Campaign.
  If this is not done, the Campaign will be inserted directly into the tweet,
  which in turn will damage the structure of the document and the tweet will not be displayed.
 */
// https://www.benzinga.com/amp/content/19518254
export const replaceAmpTwitterToP = (htmlString: string): { htmlString: string; tweets: Tweet[] } => {
  const tweets: any[] = [];

  // find full twitter content
  //const ampTwitterRegex = /<blockquote[^>]*twitter-tweet[^>]*>(?<fullTweeet>.*?)<\/blockquote>/g;
  const ampTwitterRegex = /<blockquote[^>]*twitter-tweet[^>]*>(.*?)<\/blockquote>/g;
  htmlString = htmlString.replace(ampTwitterRegex, (...match) => {
    const groups = match.pop();
    // find twitter id
    const result = groups.fullTweeet.match(/https:\/\/twitter\.com\/\S*\/(\d*)\?/);

    if (result && result.length > 1) {
      const tweet = {
        content: groups.fullTweeet,
        id: result[1],
      };
      tweets.push(tweet);
      return `<p id="${result[1]}"></p>`;
    }

    return '';
  });

  return { htmlString, tweets };
};

export const replaceRedditHTML = (htmlString: string): string => {
  //const regexPattern = /<figure[^>]*?\bwp-block-embed-reddit\b[^>]*>.*?<\/figure>/s;
  const regexPattern = /<figure[^>]*?\bwp-block-embed-reddit\b[^>]*>[\s\S]*?<\/figure>/;
  htmlString = htmlString.replace(regexPattern, (...match) => {
    //const firstAnchorTag = match?.[0]?.match(/<a\b.*?<\/a>/s);
    const firstAnchorTag = match?.[0]?.match(/<a\b[\s\S]*?<\/a>/);
    if (firstAnchorTag) {
      return `<figure class="wp-block-embed is-type-rich is-provider-reddit wp-block-embed-reddit"><blockquote class="reddit-embed-bq">${firstAnchorTag}</blockquote></figure>`;
    } else {
      return match[0];
    }
  });
  return htmlString;
};

export const insertCampaignInBlocks = (
  articleBlocks: ArticleBlocksList[],
  campaignBlock: Block,
  position = 3,
): { index: number | null; modifiedBlocks: ArticleBlocksList[] } => {
  if (articleBlocks) {
    const elementsCount = length(articleBlocks);

    if (elementsCount === 4 || elementsCount === 5) {
      position = 2;
    }

    return { index: position, modifiedBlocks: insert(position, campaignBlock, articleBlocks) };
  } else {
    return { index: null, modifiedBlocks: articleBlocks };
  }
};

export const insertCampaign = (html: string | string[], stringToInsert: string, position = 3) => {
  if (Array.isArray(html)) {
    const elementsCount = length(html);

    if (elementsCount === 4 || elementsCount === 5) {
      position = 2;
    }

    return insert(position, stringToInsert, html);
  } else {
    const elementsArray = splitElements(html) ?? [html];
    const elementsCount = length(elementsArray);

    if (elementsCount === 4 || elementsCount === 5) {
      position = 2;
    }

    return pipe(insert(position, stringToInsert), join(''))(elementsArray);
  }
};

export const parseTagAttributes = (htmlString: string): RegExpMatchArray[] => {
  if (!htmlString) return [];

  // return [...htmlString.matchAll(/([^\r\n\t\f\v= '"]+)(?:=(["'])?((?:.(?!\2?\s+(?:\S+)=|\1))+.)\2?)/gm)];
  return [...htmlString.matchAll(/(([a-z]*)="(.*?)")/gm)];
};

export const getCanonicalUrl = (node: {
  nodeId?: number;
  NodeID?: string | number;
  canonicalPath?: string;
  CanonicalPath?: string;
}): string => {
  const canonicalPath = node?.canonicalPath || node?.CanonicalPath;
  const nodeId = node?.nodeId || node?.NodeID;
  let articleUrl = `https://www.benzinga.com/article/${nodeId}`;
  if (canonicalPath?.match(/https:\/\/[A-z]+.benzinga.com/)) {
    articleUrl = canonicalPath;
  } else if (canonicalPath) {
    articleUrl = `${appEnvironment().config().url}/${canonicalPath?.trim()}`;
  }
  return articleUrl;
};

export const findPlaceholderPositionInParsedContent = (
  parsedContent: string[],
  startPositionIndex: number,
  insertCount: number,
) => {
  try {
    let currentInsertCount = 1;
    let isLastElementP = false;
    let placeholderPosition = startPositionIndex + 1;

    const isElementTag = (elementString: string) => {
      return elementString.indexOf('<') === 0;
    };

    const isElementP = (elementString: string) => {
      return elementString.indexOf('<p') === 0;
    };

    let canInsertPlaceholder: boolean | null = null;
    while (canInsertPlaceholder === null) {
      const element = parsedContent[placeholderPosition];
      if (element) {
        if (isElementTag(element)) {
          if (isElementP(element)) {
            if (isLastElementP && currentInsertCount > insertCount) {
              canInsertPlaceholder = true;
            } else {
              isLastElementP = true;
              currentInsertCount++;
              placeholderPosition++;
            }
          } else {
            isLastElementP = false;
            placeholderPosition++;
          }
        } else {
          placeholderPosition++;
        }
      } else {
        canInsertPlaceholder = false;
      }
    }

    return !canInsertPlaceholder ? placeholderPosition : false;
  } catch (error) {
    console.error('PLACEHOLDER FIND ERROR:', error);
    return false;
  }
};

export const findPlaceholderPlaceInBlocks = (
  parsedContent: ArticleBlock[] | MoneyBlockType[],
  startPositionIndex: number,
  insertCount: number,
): number | false => {
  try {
    let currentInsertCount = 1;
    let isLastElementP = false;
    let placeholderPosition = startPositionIndex + 1;

    const isElementTag = (element: ArticleBlock | MoneyBlockType) => {
      return element.nodeType === 1;
    };

    const isElementP = (element: ArticleBlock | MoneyBlockType) => {
      return element?.tag === 'p';
    };

    let canInsertPlaceholder: boolean | null = null;
    while (canInsertPlaceholder === null) {
      const element = parsedContent[placeholderPosition];
      if (element) {
        if (isElementTag(element)) {
          if (isElementP(element)) {
            if (isLastElementP && currentInsertCount > insertCount) {
              canInsertPlaceholder = true;
            } else {
              isLastElementP = true;
              currentInsertCount++;
              placeholderPosition++;
            }
          } else {
            isLastElementP = false;
            placeholderPosition++;
          }
        } else {
          placeholderPosition++;
        }
      } else {
        canInsertPlaceholder = false;
      }
    }

    return canInsertPlaceholder ? placeholderPosition : false;
  } catch (error) {
    console.error('PLACEHOLDER FIND ERROR:', error);
    return false;
  }
};

export const printProHeadlineArticleContent = () => {
  return `<p>Never miss a trade again with the fastest news alerts in the world!</p>
          <p>This headline only article is a sample of real-time intelligence
          <a href="https://benzinga.grsm.io/headlineonlyarticles3398">Benzinga Pro</a> traders use to win in the markets everyday.</p>
          <p>Want the fastest, most accurate stock market intelligence? Want EXCLUSIVE stories originated by Benzinga reporters?
          <a href="https://benzinga.grsm.io/headlineonlyarticles3398">Join 10,000+ serious traders in the Benzinga Pro community!</a>
          </p>
         `;
};

export const getSpecificArticlesPrimisID = (articleId: number): string | null => {
  const DIREXION_IDS = {
    desktop: '116214',
    mobile: '116215',
  };

  const DATAWRKZ_IDS = {
    desktop: '116239',
    mobile: '116240',
  };

  const articles = {
    34423367: DIREXION_IDS,
    34444357: DIREXION_IDS,
    34449307: DIREXION_IDS,
    34502231: DATAWRKZ_IDS,
  };

  const targetId = runningClientSide() && isMobile() ? articles[articleId]?.mobile : articles[articleId]?.desktop;

  return targetId || null;
};

export const getPrimisIdByTaxonomies = (channels: StoryCategory[], tags: StoryCategory[], amp = false): string => {
  const taxonomies: StoryCategory[] = [];

  if (Array.isArray(channels)) {
    taxonomies.push(...channels);
  }

  if (Array.isArray(tags)) {
    taxonomies.push(...tags);
  }

  const taxonomiesNames = taxonomies.map(item => item.name);

  if (amp) {
    return getAMPPrimisIDbyChannel(taxonomiesNames);
  }

  return getPrimisIDbyChannel(taxonomiesNames);
};

interface AllowedChannelI {
  articleChannel: string;
  calendarKey: ICalendarKeys;
}

export const getInitialTab = (article: ArticleData): ICalendarKeys | null => {
  if (!Array.isArray(article.channels)) {
    return null;
  }

  const allowedChannels: AllowedChannelI[] = [
    {
      articleChannel: 'earnings',
      calendarKey: 'earnings',
    },
    {
      articleChannel: 'analyst ratings',
      calendarKey: 'ratings',
    },
    {
      articleChannel: 'options',
      calendarKey: 'option_activity',
    },
    {
      articleChannel: 'dividends',
      calendarKey: 'dividends',
    },
  ];

  const formattedChannels: string[] =
    article.channels.length > 0 ? article.channels.map(el => el.name.toLowerCase()) : [];
  const intersections: ICalendarKeys[] = allowedChannels
    .filter(item => formattedChannels.includes(item.articleChannel))
    .map(item => item.calendarKey);

  return intersections?.[0] ? intersections[0] : null;
};

export const checkIfShouldLoadCalendar = (article: ArticleData): boolean => {
  const initialTab = getInitialTab(article);

  if (!initialTab || !getInitialTickers(article)?.length) {
    return false;
  } else return true;
};

export const getArticleCampaignStrategy = (queryParams: any, articleData?: ArticleData): CampaignStrategy => {
  if (articleData?.layout?.campaign_layout === 'custom') {
    return 'custom';
  }

  if (articleData?.meta?.wp_post_meta?.campaign_strategy === 'Custom') {
    return 'none';
  }

  if (!(queryParams || typeof queryParams !== 'object') && typeof articleData?.metaProps !== 'object') {
    return 'external';
  }

  if (queryParams?.campaigns_strategy) {
    return queryParams.campaigns_strategy;
  }

  const settings = getSettings(queryParams?.utm_source?.toLowerCase());
  if (settings) {
    return settings as CampaignStrategy;
  }

  const targetReferer = getCampaignSessionReferrer() || getArticleReferer(articleData?.metaProps);
  if (targetReferer) {
    let targetRefStrategy: string | null = '';
    const partnersRef = getAllRefPartners();

    partnersRef.forEach(partnerKey => {
      if (targetReferer.toLowerCase().includes(partnerKey)) {
        targetRefStrategy = getCampaignStrategySetting(partnerKey);
      }
    });

    if (targetRefStrategy) {
      return targetRefStrategy as CampaignStrategy;
    }
  }

  return 'external';
};

export const getArticleCampaignQueryParams = (): Record<string, string | null> => {
  if (!runningClientSide() || !document?.location) {
    return {};
  }

  const params = new URL(`${document.location}`).searchParams;

  return {
    campaigns_strategy: params.get('campaigns_strategy'),
    utm_source: params.get('utm_source'),
  };
};

export const getArticleReferer = (articleMeta?: MetaProps): string | undefined | null => {
  if (!runningClientSide()) {
    return articleMeta?.referer;
  }

  return document?.referrer;
};

export const injectNodeIdInCampaignBlocks = (blocks: Block[], identifierName: string, ident: string) => {
  if (Array.isArray(blocks)) {
    blocks.forEach(block => {
      if (block.blockName === 'acf/google-ad-placement') {
        const blockGoogleAdId = block.attrs.data['slot_google_ad_id'];

        if (blockGoogleAdId?.includes(ident)) {
          return;
        }

        block.attrs.data['slot_google_ad_id'] = blockGoogleAdId
          ? `${blockGoogleAdId}-${ident}`
          : `${identifierName}-${ident}`;
      } else if (block.blockName === 'acf/investing-channel-block' || block.blockName === 'acf/investing-channel') {
        if (block?.attrs?.data['id']?.includes(ident)) {
          return;
        }

        block.attrs.data['id'] = `${block.attrs.data['id']}-${ident}`;
      } else if (block.blockName === 'acf/widget-container' && block.blocks) {
        block.blocks = injectNodeIdInCampaignBlocks(block.blocks, identifierName, ident);
      }
    });
  }

  return blocks;
};

export const getArticleKeywordsGroup = (articleBody: string): { positive: string[]; negative: string[] } => {
  const groups = {
    cboe: {
      keys: cboeKeywordsList,
      name: 'CBOE',
      type: 'positive',
    },
    rocketMortgage: {
      keys: rocketMortgageKeywordsList,
      name: 'Rocket Mortgage',
      type: 'negative',
    },
    tradestation: {
      keys: tradestationKeywordsList,
      name: 'Tradestation',
      type: 'positive',
    },
  };

  const chosenGroups: { positive: string[]; negative: string[] } = { negative: [], positive: [] };
  for (const keyGroup in groups) {
    const matched = matchKeywords(groups[keyGroup].keys, articleBody);

    if (matched) {
      if (groups[keyGroup].type === 'positive') {
        chosenGroups.positive.push(groups[keyGroup].name);
      } else {
        chosenGroups.negative.push(groups[keyGroup].name);
      }
    }
  }

  return chosenGroups;
};

export const matchKeywords = (keywords: string[], articleBody: string): boolean => {
  const matches: string[] = [];
  if (Array.isArray(keywords) && articleBody) {
    for (const keyword of keywords) {
      const regex = new RegExp(`\\b${keyword.toLowerCase()}\\b`);
      if (articleBody.toLowerCase().match(regex)) {
        matches.push(keyword);
      }
    }
  }
  return !!matches.length;
};

export const injectVendorInArticleUrls = (referrer?: string) => {
  if (runningClientSide()) {
    const targetRef = referrer || document?.referrer;
    const { utm_source } = getArticleCampaignQueryParams();

    const targetSelector = targetRef || utm_source;

    if (targetSelector) {
      const allowedRefs = ['newsbreak.com', 'newsbreakapp.com'];

      const allowed = allowedRefs.some(ref => targetSelector.toLowerCase().includes(ref));

      if (allowed) {
        const links = document.querySelectorAll('.article-layout-wrapper a, .layout-sidebar a');

        if (links) {
          injectUtmSourceInUrls(links as NodeListOf<HTMLAnchorElement>, targetSelector);
        }
      }
    }
  }
};

export const setCampaignSessionReferrer = (referrer?: string): void => {
  if (runningClientSide()) {
    const targetRef = referrer || document?.referrer;
    if (targetRef) {
      const allowedRefs = ['newsbreak.com', 'newsbreakapp.com'];

      const allowed = allowedRefs.some(ref => targetRef.toLowerCase().includes(ref));

      if (allowed) {
        window.sessionStorage.setItem(CAMPAIGN_REF_STORAGE_KEY, targetRef);
      }
    }
  }
};

export const injectUtmSourceInUrls = (urlsElements: NodeListOf<HTMLAnchorElement>, utmSource: string): void => {
  if (urlsElements?.length) {
    urlsElements.forEach((item: HTMLAnchorElement) => {
      if (item.href.includes('benzinga.com') && !item.href.includes('utm_source')) {
        item.href += item.href.includes('?') ? `&utm_source=${utmSource}` : `?utm_source=${utmSource}`;
      }
    });
  }
};

export const getCampaignSessionReferrer = () => {
  if (runningClientSide()) {
    return window.sessionStorage.getItem(CAMPAIGN_REF_STORAGE_KEY);
  }

  return null;
};

export const performRegexesOnCampaignHTML = (campaign: string) => {
  let campaignHTML = campaign;
  const regexes = [
    {
      replaceValue: 'border="1"',
      searchValue: /border="[2-9]"/g,
    },
  ];
  regexes.forEach(regex => {
    campaignHTML = campaignHTML.replaceAll(regex.searchValue, regex.replaceValue);
  });
  return campaignHTML;
};

const formatParagraph = (text: string) => {
  const trimmedText = text.trim();
  const isPeriod = trimmedText[trimmedText.length - 1] === '.';
  const result = isPeriod ? trimmedText.slice(0, trimmedText.length - 1) : text;

  const htmlTagRegex = new RegExp(/(.)<[^>]*>$/, 'gi');
  const newResult = result?.replace(htmlTagRegex, '');

  return newResult;
};

export const generatePartialViewPreviewFromArticleBlocks = (blocks: ArticleBlock[]): string | null => {
  let newTeaser: null | string = null;

  const getParagraphBlocks = (blocks: ArticleBlock[]): ArticleBlock[] => {
    if (!Array.isArray(blocks)) return [];
    return blocks.filter(block => block?.tag === 'p');
  };

  const paragraphBlocks = getParagraphBlocks(blocks);

  const partialBlocks = paragraphBlocks.slice(0, 6);

  const blocksIndexes = [0, 1, 2, 3, 5];
  blocksIndexes.forEach(blockIndex => {
    if (!partialBlocks?.[blockIndex]?.innerHTML) return;
    newTeaser =
      blockIndex > 0
        ? `${newTeaser} ${formatParagraph(partialBlocks[blockIndex].innerHTML)}`
        : `${partialBlocks?.[0]?.innerHTML}`;
  });

  return newTeaser;
};

export const generatePreviewFromBody = (body: string, characterLimit = 80): string => {
  const allowedTags = ['a', 'strong', 'em', 'i', 'b', 'span', 'br'];

  const countVisibleChars = (html: string): number => {
    return html.replace(/<[^>]*>/g, '').length;
  };

  const truncateAndCombineHtml = (html: string, limit: number): string => {
    const paragraphs = html.split('</p>');
    let result = '<p>';
    let charCount = 0;
    let isTruncated = false;

    const processContent = (content: string): boolean => {
      let currentTag = '';
      let inTag = false;
      let lastWordBoundary = result.length;

      for (let i = 0; i < content.length; i++) {
        const char = content[i];

        if (char === '<') {
          inTag = true;
          currentTag = '<';
        } else if (char === '>') {
          inTag = false;
          if (currentTag.startsWith('</') || allowedTags.includes(currentTag.slice(1).split(/\s/)[0])) {
            result += currentTag + '>';
          }
          currentTag = '';
        } else if (inTag) {
          currentTag += char;
        } else {
          if (charCount >= limit) {
            isTruncated = true;
            result = result.slice(0, lastWordBoundary);
            return false;
          }
          result += char;
          charCount++;
          if (char === ' ' || char === '\n' || char === '\t' || char === '.') {
            lastWordBoundary = result.length;
          }
        }
      }
      return true;
    };

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].replace(/<p[^>]*>/g, '').trim();
      if (paragraph) {
        if (charCount > 0) result += ' ';
        if (!processContent(paragraph)) break;
      }
    }

    if (isTruncated) {
      result = result.trim();
      if (result.endsWith('.')) {
        result = result.slice(0, -1);
      }
      result += '...';
    }

    result += '</p>';
    return result;
  };

  const cleanHtml = body.replace(/<\/?(?!p\b)[a-z](?:[^>"']|"[^"]*"|'[^']*')*>/gi, '');
  const plainTextLength = countVisibleChars(cleanHtml);

  if (plainTextLength <= characterLimit) {
    return truncateAndCombineHtml(cleanHtml, Infinity);
  } else {
    return truncateAndCombineHtml(cleanHtml, characterLimit);
  }
};

export const getInfiniteScrollArticles = async (
  articleManager: ArticleManager,
  articleData: ArticleData,
  //userId?: number | string,
): Promise<ArticleData[]> => {
  try {
    const { ok: relevantArticlesResponse } = await articleManager.getRelevantArticles(articleData);

    const nextArticles = [
      ...sortTopStoriesAndSponsoredWithAlgo(
        relevantArticlesResponse?.topStories ?? [],
        relevantArticlesResponse?.sponsored ?? [],
      ),
    ];

    return nextArticles;

    // if (Array.isArray(topArticlesResponse) && topArticlesResponse.length) {
    //   const nextArticles = [
    //     ...sortTopStoriesAndSponsoredWithAlgo(topArticlesResponse ?? [], relevantArticlesResponse?.sponsored ?? []),
    //   ];
    //   return nextArticles;
    // }
  } catch {
    return [];
  }
};

export const getInfiniteScrollArticlesIDs = async (
  articleManager: ArticleManager,
  articleData: ArticleData,
): Promise<number[]> => {
  try {
    const { ok: relevantArticlesResponse } = await articleManager.getRelevantArticlesIDs(articleData);

    const topStories = relevantArticlesResponse?.topStories ?? [];
    const sponsored = relevantArticlesResponse?.sponsored ?? [];

    return [...sortTopStoriesAndSponsoredWithAlgo<number>(topStories, sponsored)];
  } catch {
    return [];
  }
};

const sortTopStoriesAndSponsoredWithAlgo = <T = ArticleData>(topStories: T[], sponsored: T[]): T[] => {
  const result: T[] = [];
  let firstIndex = 0;
  let secondIndex = 0;
  let counter = 0;

  while (firstIndex < topStories?.length || secondIndex < sponsored?.length) {
    if (counter % 4 === 3 && secondIndex < sponsored?.length) {
      result.push(sponsored[secondIndex]);
      secondIndex++;
    } else if (firstIndex < topStories?.length) {
      result.push(topStories[firstIndex]);
      firstIndex++;
    }
    counter++;
  }

  return result;
};

export const getSecFormType = (Tags: StoryCategory[]): string | null => {
  let found: string | null = null;
  if (Tags && Tags.length) {
    Tags.forEach(tag => {
      if (tag.vid === 12) {
        found = tag.name;
        return;
      }
    });
  }
  return found;
};

export const isStoryTaggedPressRelease = async (
  session: Session,
  article: { channels: StoryCategory[]; tags: StoryCategory[]; type: string },
) => {
  const advancedNewsManager = session.getManager(AdvancedNewsManager);
  const contentTypes = await advancedNewsManager.getContentTypes();

  const result =
    nodeHasChannel(article as unknown as StoryObject, 'Press Releases') ||
    nodeHasTag(article as unknown as StoryObject, 'Press Releases') ||
    (Array.isArray(contentTypes?.ok?.pressRelease) && contentTypes?.ok?.pressRelease.includes(article?.type));

  return result;
};

export const automatedContentAuthorIds = [178351]; //Benzinga insights

export const isContentAutomated = (userId: number) => {
  if (!userId) return false;
  return automatedContentAuthorIds.includes(userId) ?? false;
};

export const injectOptinMonsterInArticlePlaceholder = (articleBlocks: ArticleBlock[]) => {
  if (!Array.isArray(articleBlocks) || articleBlocks?.length < 4) return articleBlocks;

  const injectedBlock = {
    attrs: {
      ids: ['om-sgyzzeizskvbrtpwegkk-holder', 'om-bqz3ksge7qwckmhxxv4l-holder'],
    },
    blockName: 'optinmonster-placeholder',
  };

  const getParagraphCount = () => {
    const result = articleBlocks.filter((block: ArticleBlock) => block?.tag === 'p');
    return result?.length ?? 0;
  };

  const generateNewBlocks = () => {
    const paragraphCount = getParagraphCount();
    const result = [...articleBlocks];
    if (paragraphCount >= 4) {
      result.splice(2, 0, injectedBlock as ArticleBlock);
    }
    return result;
  };

  const newBlocks = generateNewBlocks();

  return newBlocks;
};

export const formatArticleSurrogateKeys = (nid: string): string => {
  if (!nid) return '';

  const surrogateKeys = ['article', `/node/${nid}`, `nid:${nid}`];

  return surrogateKeys.join(' ');
};

const DISABLE_CAMPAIGNIFY_UNIT_AUTHOR_IDS = [
  178351, // Benzinga Insights
];

export const shouldDisableCampaignifyUnit = (article: ArticleData, isDisabledFromLayout: boolean) => {
  if (isDisabledFromLayout) {
    return isDisabledFromLayout;
  }
  const userId = article.author?.uid || (article as InternalNodeCamelCase).userId;

  if (userId && DISABLE_CAMPAIGNIFY_UNIT_AUTHOR_IDS.includes(userId)) {
    return true;
  }
  return false;
};

export const isTagged = (article: ArticleData, tids: number[]) => {
  return article.tags?.some(tag => tids.includes(tag?.tid));
};

export const isChanneled = (article: ArticleData, tids: number[]) => {
  return article.channels?.some(channel => tids.includes(channel?.tid));
};

export const getAuthorName = (articleData: ArticleData): string => {
  if (articleData?.author?.firstname && articleData?.author?.lastname) {
    return `${articleData.author.firstname} ${articleData.author.lastname}`;
  }
  return articleData?.author?.name || articleData?.name || '';
};

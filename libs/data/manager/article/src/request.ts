import { SafeError, SafePromise, safeAwaitAll } from '@benzinga/safe-await';
import { ListenableSubscribable } from '@benzinga/subscribable';

import {
  Campaigns,
  InternalNode,
  Author,
  AuthorId,
  NodeId,
  ArticleData,
  TrendingIndiaPosts,
  DraftArticle,
  EditorialArticlePreviewResponse,
  ExpiredUrlResponse,
  RelevantArticlesIds,
  TrendingIndiaTopics,
  WNSTNFollowUpQuestionsResponse,
} from './entities/article';
import {
  CampaignRestful,
  ExternalRestful,
  AuthorRestful,
  InternalContentRestful,
  RelevantArticlesRestful,
} from './restful';
import { ingestAuthor } from './ingest';
import { Session } from '@benzinga/session';
import { EditorialToolsRestful } from './restful/editorialTools';
import { CommentCountResponse } from './entities';
import { CommentsRestful } from './restful/comments';

interface ErrorEvent {
  error?: SafeError;
  errorType:
    | 'article:get_author_error'
    | 'article:get_article_error'
    | 'article:get_draft_article_error'
    | 'article:get_trending_india_stories_ids_error'
    | 'article:get_trending_india_topics_error';
  type: 'error';
}

interface AuthorUpdateEvent {
  type: 'article:retrieving_author';
}

interface ArticleUpdateEvent {
  type: 'article:retrieving_article' | 'article:retrieving_draft_article';
}

export type ArticleRequestEvent = ErrorEvent | ArticleUpdateEvent | AuthorUpdateEvent;

export class ArticleRequest extends ListenableSubscribable<ArticleRequestEvent> {
  private authorRestful: AuthorRestful;
  private campaignRequest: CampaignRestful;
  private internalContentRestful: InternalContentRestful;
  private externalRestful: ExternalRestful;
  private relevantArticlesRestful: RelevantArticlesRestful;
  private editorialToolsRestful: EditorialToolsRestful;
  private commentsRestful: CommentsRestful;

  constructor(session: Session) {
    super();
    this.authorRestful = new AuthorRestful(session);
    this.campaignRequest = new CampaignRestful(session);
    this.externalRestful = new ExternalRestful(session);
    this.internalContentRestful = new InternalContentRestful(session);
    this.editorialToolsRestful = new EditorialToolsRestful(session);
    this.relevantArticlesRestful = new RelevantArticlesRestful(session);
    this.commentsRestful = new CommentsRestful(session);
  }

  public getCampaigns = async (nodeId: NodeId): SafePromise<Campaigns> => {
    return this.campaignRequest.getCampaigns(nodeId);
  };

  public getAuthorByPath = async (authorSlug: string): SafePromise<Author> => {
    this.dispatch({
      type: 'article:retrieving_author',
    });
    const author = await ingestAuthor(this.authorRestful.getAuthorByPath(authorSlug));
    if (author.err) {
      this.dispatch({
        error: author.err,
        errorType: 'article:get_author_error',
        type: 'error',
      });
    }
    return author;
  };

  public getAuthor = async (authorId: AuthorId): SafePromise<Author> => {
    this.dispatch({
      type: 'article:retrieving_author',
    });
    const author = await ingestAuthor(this.authorRestful.getAuthor(authorId));
    if (author.err) {
      this.dispatch({
        error: author.err,
        errorType: 'article:get_author_error',
        type: 'error',
      });
    }
    return author;
  };

  public getArticle = async (nodeId: NodeId): SafePromise<ArticleData> => {
    this.dispatch({
      type: 'article:retrieving_article',
    });
    const article = await this.externalRestful.getArticle(nodeId);
    if (article.err) {
      this.dispatch({
        error: article.err,
        errorType: 'article:get_article_error',
        type: 'error',
      });
    }
    return article;
  };

  public getDraftArticle = async (nodeId: NodeId, token?: string): SafePromise<DraftArticle> => {
    this.dispatch({
      type: 'article:retrieving_draft_article',
    });
    const article = await this.externalRestful.getDraftArticle(nodeId, token);
    if (article.err) {
      this.dispatch({
        error: article.err,
        errorType: 'article:get_draft_article_error',
        type: 'error',
      });
    }
    return article;
  };

  getEditorialArticlePreview = async (nodeId: number | string): SafePromise<EditorialArticlePreviewResponse> => {
    return await this.editorialToolsRestful.getEditorialArticlePreview(nodeId);
  };

  public getTrendingIndiaStoriesIds = async (): SafePromise<TrendingIndiaPosts[]> => {
    const response = await this.editorialToolsRestful.getTrendingIndiaStoriesIds();
    if (response.err) {
      this.dispatch({ error: response.err, errorType: 'article:get_trending_india_stories_ids_error', type: 'error' });
      return response;
    }
    return response;
  };

  public getTrendingIndiaTopics = async (): SafePromise<TrendingIndiaTopics[]> => {
    const response = await this.editorialToolsRestful.getTrendingIndiaTopics();
    if (response.err) {
      this.dispatch({ error: response.err, errorType: 'article:get_trending_india_topics_error', type: 'error' });
      return response;
    }
    return response;
  };

  public getArticlesWithIds = async (ids: number[]): SafePromise<ArticleData[]> => {
    const reqs = ids.map(id => this.getArticle(id));

    const response = await safeAwaitAll(reqs);

    if (response.err && response.err.length) {
      return { err: response.err[0] };
    }

    if (Array.isArray(response.ok)) {
      return {
        ok: response.ok,
      };
    } else {
      return { ok: [] };
    }
  };

  public getInternalNode = async (nodeId: NodeId): SafePromise<InternalNode> => {
    return this.internalContentRestful.getInternalNode(nodeId);
  };

  public getRelevantArticles = async (channelId = 0): SafePromise<RelevantArticlesIds> => {
    return this.relevantArticlesRestful.getRelevantArticles(channelId);
  };

  getCommentCount = async (id: number): SafePromise<CommentCountResponse | undefined> => {
    return this.commentsRestful.getCommentCount(id);
  };

  getFollowUpQuestions = async (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    return this.editorialToolsRestful.getEditorialFollowUpsQuestions(nodeId);
  };

  getFollowUpQuestionsInternal = async (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    return this.editorialToolsRestful.getEditorialFollowUpsQuestionsInternal(nodeId);
  };

  checkExpiredUrl = async (type: string, slug: string): SafePromise<ExpiredUrlResponse> => {
    return this.editorialToolsRestful.checkExpiredUrl(type, slug);
  };
}

import { SearchItem } from './module';

export interface GptSearchItemProps {
  prompt: string;
}

export const GptSearchModuleId = 'AI' as const;

export const GptSearchItemVersion = '1' as const;
export interface GptSearchItem extends SearchItem<GptSearchItemProps> {
  version: typeof GptSearchItemVersion;
  moduleId: typeof GptSearchModuleId;
}

export const isGptSearchItem = (item: SearchItem): item is GptSearchItem =>
  item.moduleId === GptSearchModuleId && item.version === GptSearchItemVersion;

export const createGptSearchItem = (prompt: string) => ({
  data: {
    prompt,
  },
  id: prompt,
  moduleId: GptSearchModuleId,
  version: GptSearchItemVersion,
});
